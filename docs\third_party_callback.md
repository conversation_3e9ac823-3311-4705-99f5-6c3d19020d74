# 第三方回调接口说明

## 概述

`/rsoc/postback` 是专门用于接收第三方平台回传转化数据的回调接口。当用户在第三方平台完成转化行为时，第三方平台会调用此接口回传转化数据。

## 接口信息

**接口地址**: `/rsoc/postback`  
**请求方式**: `GET` 或 `POST`  
**用途**: 接收第三方平台的转化数据回传  
**响应格式**: JSON

## 标准回调 URL 格式

根据文档提供的标准格式：
```
http://www.xxxxxxxxx.com/rsoc/postback?campaign=campaign&click_id=ad_click_cpc&payout=EPC&country=country&zip=zip&os_type=os_type&browser=browser&device_type=device_type&device_brand=device_brand&s1=subid1&s2=subid2&s3=subid3
```

## 支持的参数

### 基础参数
| 参数名 | 说明 | 示例 | 必填 |
|--------|------|------|------|
| campaign | 广告系列ID | 12345 | 是 |
| click_id | 点击ID | abc123xyz | 否 |
| payout | 收益金额 | 1.50 | 否 |
| epayout | 估算收益金额 | 0.123 | 否 |

### 地理位置参数
| 参数名 | 说明 | 示例 | 必填 |
|--------|------|------|------|
| country | 2位国家代码 | US | 否 |
| country_name | 完整国家名称 | United States | 否 |
| state | 州/省代码或名称 | CA | 否 |
| city | 城市名称 | Los Angeles | 否 |
| zip | 邮编 | 90001 | 否 |

### 设备信息参数
| 参数名 | 说明 | 示例 | 必填 |
|--------|------|------|------|
| os_type | 操作系统 | WINDOWS | 否 |
| browser | 浏览器类型 | CHROME | 否 |
| device_type | 设备类型 | MOBILE/DESKTOP | 否 |
| device_brand | 设备品牌 | APPLE | 否 |

### 自定义参数
| 参数名 | 说明 | 示例 | 必填 |
|--------|------|------|------|
| s1, s2, s3 | 自定义子ID (AdTech格式) | test1, test2, test3 | 否 |
| subid1-subid5 | 自定义子ID (SedoTMP格式) | AdSetID, PictureID | 否 |

## 回调示例

### AdTech 平台回调
```bash
GET /rsoc/postback?campaign=123&click_id=abc123&payout=1.50&country=US&s1=test1
```

### SedoTMP 平台回调
```bash
GET /rsoc/postback?campaign=12345&click_id=xyz789&epayout=0.123&country=US&country_name=United%20States&state=CA&city=Los%20Angeles&subid1=AdSetID123&subid2=PictureID456
```

### 标准格式回调
```bash
GET /rsoc/postback?campaign=campaign&click_id=ad_click_cpc&payout=EPC&country=country&zip=zip&os_type=os_type&browser=browser&device_type=device_type&device_brand=device_brand&s1=subid1&s2=subid2&s3=subid3
```

## 响应格式

### 成功响应
```json
{
    "code": 1,
    "msg": "ok"
}
```

### 错误响应
```json
{
    "code": 400,
    "msg": "数据格式错误"
}
```

或

```json
{
    "code": 500,
    "msg": "处理数据失败"
}
```

## 数据处理逻辑

1. **参数解析**: 自动解析 GET 参数或 POST 表单数据
2. **收益处理**: 优先使用 `payout`，如果为空则使用 `epayout`
3. **数据存储**: 转化数据存储到 `facebook_insights` 表
4. **重复处理**: 同一天同一个 campaign 的数据会更新而不是重复创建

### 数据映射规则

| 回调参数 | 数据库字段 | 说明 |
|----------|------------|------|
| campaign | campaign_id, account_id, ad_id, adset_id | 作为各种ID |
| payout/epayout | real_price | 真实收益 |
| 当前日期 | date | 数据日期 |
| 固定值1 | clicks | 转化次数 |
| 当前时间 | update_time | 更新时间 |

## 第三方平台集成

### 在 AdTech 平台设置
1. 登录 AdTech 管理后台
2. 找到 Postback 设置页面
3. 设置回调 URL: `http://your-domain.com/rsoc/postback`
4. 配置字段映射（参考 API 文档）

### 在 SedoTMP 平台设置
1. 登录 SedoTMP 管理后台
2. 在 Postback 设置中输入: `http://your-domain.com/rsoc/postback?click_id={click_id}&epayout={epayout}&campaign={campaign}&country={country}&subid1={subid1}&subid2={subid2}`

### 自定义平台集成
根据平台支持的宏参数，构建相应的回调 URL。确保至少包含 `campaign` 参数。

## 监控和调试

### 查看回调日志
```bash
# 查看 postback 相关日志
tail -f /var/log/your-app.log | grep "postback"

# 查看转化数据处理日志
tail -f /var/log/your-app.log | grep "处理postback数据"
```

### 验证数据存储
```sql
-- 查看最新的转化数据
SELECT campaign_id, date, real_price, update_time 
FROM facebook_insights 
WHERE real_price IS NOT NULL 
ORDER BY update_time DESC 
LIMIT 10;

-- 查看特定广告系列的转化数据
SELECT * FROM facebook_insights 
WHERE campaign_id = 'your_campaign_id' 
ORDER BY date DESC;
```

### 测试回调接口
```bash
# 手动测试回调接口
curl "http://your-domain.com/rsoc/postback?campaign=test123&payout=1.50&country=US"

# 检查响应
curl -v "http://your-domain.com/rsoc/postback?campaign=test123&payout=1.50"
```

## 常见问题

### 1. 回调没有收到数据
- 检查第三方平台的回调 URL 配置是否正确
- 确认网络连接和防火墙设置
- 查看应用日志是否有错误信息

### 2. 数据没有存储到数据库
- 检查 `campaign` 参数是否传递
- 确认数据库连接正常
- 查看应用日志中的错误信息

### 3. 收益数据不正确
- 确认传递的是 `payout` 还是 `epayout`
- 检查数值格式是否正确（应为数字）
- 查看日志中的参数解析信息

## 安全考虑

1. **IP 白名单**: 建议配置第三方平台的 IP 白名单
2. **参数验证**: 系统会自动验证参数格式
3. **日志记录**: 所有回调请求都会记录日志
4. **错误处理**: 异常情况会返回相应的错误码

## 性能优化

1. **异步处理**: 考虑使用队列处理大量回调请求
2. **数据库优化**: 为常用查询字段添加索引
3. **缓存机制**: 对频繁查询的数据进行缓存
4. **监控告警**: 设置回调处理失败的告警机制
