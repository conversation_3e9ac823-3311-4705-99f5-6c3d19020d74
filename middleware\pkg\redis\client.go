package redis

import (
	"context"
	"log"
	"sync"

	"github.com/go-redis/redis/v8"
)

var (
	// 默认的 Redis 客户端
	defaultClient *redis.Client
	// 用于存储不同数据库的 Redis 客户端
	clients    = make(map[int]*redis.Client)
	clientsMux sync.RWMutex
)

// SetDefaultClient 设置默认的 Redis 客户端
func SetDefaultClient(client *redis.Client) {
	defaultClient = client
}

// GetDefaultClient 获取默认的 Redis 客户端
func GetDefaultClient() *redis.Client {
	return defaultClient
}

// SelectDB 切换到指定的 Redis 数据库
func SelectDB(db int) *redis.Client {
	if defaultClient == nil {
		log.Printf("[redis] Redis client not initialized")
		return nil
	}

	// 如果是默认数据库（通常是0），直接返回全局客户端
	if db == 0 {
		return defaultClient
	}

	// 首先尝试从缓存中获取已存在的客户端
	clientsMux.RLock()
	if client, exists := clients[db]; exists {
		clientsMux.RUnlock()
		return client
	}
	clientsMux.RUnlock()

	// 如果不存在，创建新的客户端
	clientsMux.Lock()
	defer clientsMux.Unlock()

	// 双重检查，防止在加锁期间其他协程已创建
	if client, exists := clients[db]; exists {
		return client
	}

	// 创建新的客户端实例，指定新的数据库
	newOpts := *defaultClient.Options()
	newOpts.DB = db
	client := redis.NewClient(&newOpts)

	// 测试连接
	ctx := context.Background()
	if err := client.Ping(ctx).Err(); err != nil {
		log.Printf("[redis] Failed to connect to database %d: %v", db, err)
		client.Close()
		return nil
	}

	clients[db] = client
	return client
}

// CloseDB 关闭指定数据库的连接
func CloseDB(db int) {
	// 不允许关闭默认数据库连接
	if db == 0 {
		return
	}

	clientsMux.Lock()
	defer clientsMux.Unlock()

	if client, exists := clients[db]; exists {
		client.Close()
		delete(clients, db)
	}
}

// CloseAllDBs 关闭所有额外的数据库连接
func CloseAllDBs() {
	clientsMux.Lock()
	defer clientsMux.Unlock()

	for db, client := range clients {
		client.Close()
		delete(clients, db)
	}
}
