package recovery

import (
	"bytes"
	"context"
	"fmt"
	"log"
	"net/http"
	"os"
	"runtime"
	"syscall"
	"time"

	"rsoc-system-go/service"

	"github.com/gin-gonic/gin"
)

// 定义上报配置
type ReportConfig struct {
	// 上报地址
	ReportURL string
	// 是否启用自动重启
	EnableRestart bool
	// 最大重试次数
	MaxRetries int
	// 重试间隔（秒）
	RetryInterval time.Duration
	// 是否启用Telegram通知
	EnableTelegram bool
	// 是否启用Bark通知
	EnableBark bool
}

var (
	defaultConfig = ReportConfig{
		ReportURL:      os.Getenv("LOG_REPORT_URL"),
		EnableRestart:  true,
		MaxRetries:     3,
		RetryInterval:  5 * time.Second,
		EnableTelegram: true,
		EnableBark:     true,
	}
)

// Recovery 返回一个用于恢复panic的中间件
func Recovery(config *ReportConfig) gin.HandlerFunc {
	if config == nil {
		config = &defaultConfig
	}

	return func(c *gin.Context) {
		defer func() {
			if err := recover(); err != nil {
				// 获取堆栈信息
				stack := make([]byte, 4096)
				stack = stack[:runtime.Stack(stack, false)]

				// 构建错误信息
				errorMsg := fmt.Sprintf("Panic recovered: %v\nStack:\n%s", err, stack)

				// 记录到本地日志
				log.Printf("[FATAL] %s", errorMsg)

				// 异步上报错误
				go reportError(config, errorMsg)

				// 发送Telegram通知
				if config.EnableTelegram {
					go func() {
						telegramService := service.GetTelegramService()
						if err := telegramService.SendErrorReport(err, string(stack)); err != nil {
							log.Printf("发送Telegram通知失败: %v", err)
						}
					}()
				}

				// 发送Bark通知
				if config.EnableBark {
					go func() {
						barkService := service.GetBarkService()
						if err := barkService.SendErrorReport(err, string(stack)); err != nil {
							log.Printf("发送Bark通知失败: %v", err)
						}
					}()
				}

				// 如果启用了自动重启
				if config.EnableRestart {
					go func() {
						log.Println("服务即将重启...")
						// 给一些时间让日志写入和上报完成
						time.Sleep(2 * time.Second)
						// 重启程序
						restartProgram()
					}()
				}

				// 返回500错误
				c.AbortWithStatusJSON(http.StatusInternalServerError, gin.H{
					"error": "Internal Server Error",
					"time":  time.Now().Unix(),
				})
			}
		}()
		c.Next()
	}
}

// 上报错误信息到指定接口
func reportError(config *ReportConfig, errorMsg string) {
	if config.ReportURL == "" {
		log.Println("未配置日志上报地址，跳过上报")
		return
	}

	for i := 0; i < config.MaxRetries; i++ {
		// 创建上报请求
		req, err := http.NewRequest("POST", config.ReportURL, bytes.NewBufferString(errorMsg))
		if err != nil {
			log.Printf("创建上报请求失败: %v", err)
			continue
		}

		req.Header.Set("Content-Type", "application/json")

		// 设置超时上下文
		ctx, cancel := context.WithTimeout(context.Background(), 10*time.Second)
		defer cancel()
		req = req.WithContext(ctx)

		// 发送请求
		client := &http.Client{}
		resp, err := client.Do(req)
		if err != nil {
			log.Printf("上报失败(尝试 %d/%d): %v", i+1, config.MaxRetries, err)
			time.Sleep(config.RetryInterval)
			continue
		}
		defer resp.Body.Close()

		if resp.StatusCode == http.StatusOK {
			log.Println("错误日志上报成功")
			return
		}

		log.Printf("上报失败(尝试 %d/%d): 状态码 %d", i+1, config.MaxRetries, resp.StatusCode)
		time.Sleep(config.RetryInterval)
	}

	log.Printf("错误日志上报失败，已达到最大重试次数 %d", config.MaxRetries)
}

// 重启程序
func restartProgram() {
	executable, err := os.Executable()
	if err != nil {
		log.Printf("获取程序路径失败: %v", err)
		return
	}

	// 设置环境变量标记这是一次重启
	os.Setenv("RESTART_COUNT", fmt.Sprintf("%d", getRestartCount()+1))
	os.Setenv("LAST_RESTART", fmt.Sprintf("%d", time.Now().Unix()))

	// 使用新进程替换当前进程
	err = syscall.Exec(executable, os.Args, os.Environ())
	if err != nil {
		log.Printf("重启失败: %v", err)
	}
}

// 获取重启次数
func getRestartCount() int {
	countStr := os.Getenv("RESTART_COUNT")
	if countStr == "" {
		return 0
	}
	count := 0
	fmt.Sscanf(countStr, "%d", &count)
	return count
}
