package jobs

import (
	"context"
	"log"
	"rsoc-system-go/dao"
	"rsoc-system-go/model"
	"rsoc-system-go/service"
	"rsoc-system-go/store"
	"time"
)

const GoogleReportSyncJobClass = "google_report_sync_job"

func GoogleReportSyncJobFunc(ctx context.Context) error {
	log.Println("开始执行GoogleReport同步任务")

	keyDao := dao.NewRoscKeyDao(store.DB)
	var keys model.RsocKey
	query := map[string]interface{}{
		"key_type": map[string]interface{}{
			"eq": 1,
		},
	}
	list, err := keyDao.List(&keys, query)
	if err != nil {
		log.Println("获取API密钥失败: ", err)
		return err
	}
	for _, item := range list {
		if item.KeyType == 1 {
			syncService := service.GetGoogleReportSyncService()
			result, err := syncService.SyncGoogleReportData(ctx, &model.GoogleReportSyncRequest{
				SecurityKey: item.Key,
				DataDate:    time.Now().Format(time.DateOnly),
				Limit:       100,
				Page:        1,
			})
			if err != nil {
				log.Printf("GoogleReport同步任务执行失败: %v", err)
				return err
			}
			log.Printf("GoogleReport同步任务执行完成，总计: %d, 已同步: %d, 失败: %d",
				result.TotalCount, result.SyncedCount, result.ErrorCount)
		}
	}

	return nil
}
