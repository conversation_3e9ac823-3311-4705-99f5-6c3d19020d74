# Postback 功能使用示例

## 场景说明

假设您有一个广告投放系统，需要跟踪用户点击广告后的转化情况。当用户完成转化（如购买、注册等）时，第三方平台会向您的系统发送 postback 数据。

## 步骤1：设置 Postback 配置

首先，您需要告诉 AdTech API 您的字段映射关系：

```bash
curl --location --request POST 'http://your-domain.com/rsoc/postback' \
--header 'Content-Type: application/x-www-form-urlencoded' \
--data-urlencode 'security_key=your_security_key' \
--data-urlencode 'domain_name=http://your-callback-domain.com/postback' \
--data-urlencode 'campaign=campaign_id' \
--data-urlencode 'click_id=click_id' \
--data-urlencode 'payout=revenue' \
--data-urlencode 'country=geo' \
--data-urlencode 's1=sub1' \
--data-urlencode 's2=sub2'
```

这告诉系统：
- 您的 `campaign_id` 字段对应我们的 `campaign`
- 您的 `click_id` 字段对应我们的 `click_id`
- 您的 `revenue` 字段对应我们的 `payout`
- 等等...

## 步骤2：投放广告

您的投放链接格式为：
```
https://your-landing-page.com/article?campaign=123&click_id={click_id}&sub1={sub1}&sub2={sub2}
```

## 步骤3：接收转化数据

当用户完成转化时，第三方平台会向您的回调地址发送数据：

```bash
# GET 方式
http://your-callback-domain.com/postback?campaign_id=123&click_id=abc123&revenue=5.50&geo=US&sub1=test1

# POST 方式
curl -X POST 'http://your-callback-domain.com/postback' \
  -d 'campaign_id=123&click_id=abc123&revenue=5.50&geo=US&sub1=test1'
```

## 步骤4：数据存储

系统会自动将转化数据存储到 `facebook_insights` 表中：

| 字段 | 值 | 说明 |
|------|-----|------|
| date | 2024-01-15 | 当前日期 |
| campaign_id | 123 | 广告系列ID |
| account_id | 123 | 账户ID（同campaign_id） |
| ad_id | 123 | 广告ID（同campaign_id） |
| adset_id | 123 | 广告组ID（同campaign_id） |
| clicks | 1 | 点击数（新记录时设为1） |
| real_price | 5.50 | 真实收益 |
| update_time | 2024-01-15 10:30:00 | 更新时间 |

## 数据查询

您可以通过查询 `facebook_insights` 表来获取转化数据：

```sql
-- 查询某个广告系列的转化数据
SELECT campaign_id, date, clicks, real_price, update_time 
FROM facebook_insights 
WHERE campaign_id = '123' 
ORDER BY date DESC;

-- 查询某天的总收益
SELECT date, SUM(real_price) as total_revenue 
FROM facebook_insights 
WHERE date = '2024-01-15' 
GROUP BY date;
```

## 注意事项

1. **重复数据处理**：如果同一天同一个 campaign_id 有多次转化，系统会更新 `real_price` 字段，而不是创建新记录。

2. **必需字段**：`campaign` 参数是必需的，如果没有提供，系统会跳过处理。

3. **数据类型**：`payout` 字段会自动转换为浮点数存储在 `real_price` 字段中。

4. **错误处理**：如果数据格式错误或处理失败，系统会返回相应的错误信息。

## 监控和调试

查看日志以监控 postback 处理情况：

```bash
# 查看应用日志
tail -f /var/log/your-app.log | grep "postback"
```

日志示例：
```
2024-01-15 10:30:00 INFO 接收到postback数据: {Campaign:123 ClickID:abc123 Payout:5.50 Country:US S1:test1 ...}
2024-01-15 10:30:00 INFO Postback配置已设置: domain=http://your-callback-domain.com/postback, security_key=your_key
```
