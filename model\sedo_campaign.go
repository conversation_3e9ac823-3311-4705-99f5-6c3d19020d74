package model

import "time"

type SedoCreateCampaignRequest struct {
	PublishDomainName string `json:"publishDomainName"`
	Article           struct {
		Country       string `json:"country"`
		Locale        string `json:"locale"`
		FeaturedImage struct {
			Generate bool `json:"generate"`
		} `json:"featuredImage"`
		Title          string   `json:"title"`
		Excerpt        string   `json:"excerpt"`
		Topics         []string `json:"topics"`
		ContentPhrases []string `json:"contentPhrases"`
		CategoryId     string   `json:"categoryId"`
		Type           string   `json:"type"`
	} `json:"article"`
	Campaign struct {
		Name         string `json:"name"`
		TrackingData struct {
			TrafficSource    string `json:"trafficSource"`
			TrackingSettings struct {
				PixelMetaPixelId          string `json:"pixelMetaPixelId"`
				PixelMetaLandingPageEvent string `json:"pixelMetaLandingPageEvent"`
				PixelMetaClickEvent       string `json:"pixelMetaClickEvent"`
				PixelMetaSearchEvent      string `json:"pixelMetaSearchEvent"`
				Type                      string `json:"type"`
			} `json:"trackingSettings"`
			TrackingMethod string `json:"trackingMethod"`
			Postbacks      []struct {
				EventName    string `json:"eventName"`
				Url          string `json:"url"`
				ClickIdParam string `json:"clickIdParam"`
			} `json:"postbacks"`
		} `json:"trackingData"`
		Type string `json:"type"`
	} `json:"campaign"`
	Partner string `json:"partner"`
}

type SedoCampaign struct {
	Id                string `json:"id"`
	Partner           string `json:"partner"`
	TrackingUrl       string `json:"trackingUrl"`
	PublishDomainName string `json:"publishDomainName"`
	Article           struct {
		Id            string `json:"id"`
		Country       string `json:"country"`
		Locale        string `json:"locale"`
		FeaturedImage struct {
			Generate bool `json:"generate"`
		} `json:"featuredImage"`
		Title          string   `json:"title"`
		Excerpt        string   `json:"excerpt"`
		Topics         []string `json:"topics"`
		ContentPhrases []string `json:"contentPhrases"`
		CategoryId     string   `json:"categoryId"`
	} `json:"article"`
	Campaign struct {
		Id           string `json:"id"`
		Name         string `json:"name"`
		TrackingData struct {
			TrafficSource    string `json:"trafficSource"`
			TrackingSettings struct {
				PixelMetaPixelId          string `json:"pixelMetaPixelId"`
				PixelMetaLandingPageEvent string `json:"pixelMetaLandingPageEvent"`
				PixelMetaClickEvent       string `json:"pixelMetaClickEvent"`
				PixelMetaSearchEvent      string `json:"pixelMetaSearchEvent"`
				Type                      string `json:"type"`
			} `json:"trackingSettings"`
			TrackingMethod string `json:"trackingMethod"`
			Postbacks      []struct {
				EventName    string `json:"eventName"`
				Url          string `json:"url"`
				ClickIdParam string `json:"clickIdParam"`
			} `json:"postbacks"`
		} `json:"trackingData"`
	} `json:"campaign"`
	Status                 string `json:"status"`
	ProcessingErrorDetails struct {
		Type     string `json:"type"`
		Title    string `json:"title"`
		Status   int    `json:"status"`
		Detail   string `json:"detail"`
		Instance string `json:"instance"`
	} `json:"processingErrorDetails"`
	CreatedDate      time.Time `json:"createdDate"`
	CreatedBy        string    `json:"createdBy"`
	LastModifiedDate time.Time `json:"lastModifiedDate"`
	LastModifiedBy   string    `json:"lastModifiedBy"`
}
