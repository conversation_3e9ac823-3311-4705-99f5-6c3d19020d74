package dao

import (
	"rsoc-system-go/model"

	"gorm.io/gorm"
)

// ProxySetDAO 代理设置数据访问对象
type ProxySetDAO struct {
	db *gorm.DB
}

// NewProxySetDAO 创建新的ProxySetDAO实例
func NewProxySetDAO(db *gorm.DB) *ProxySetDAO {
	return &ProxySetDAO{db: db}
}

// Create 创建代理设置
func (dao *ProxySetDAO) Create(proxySet *model.ProxySet) error {
	return dao.db.Create(proxySet).Error
}

// Update 更新代理设置
func (dao *ProxySetDAO) Update(proxySet *model.ProxySet) error {
	return dao.db.Save(proxySet).Error
}

// Delete 删除代理设置
func (dao *ProxySetDAO) Delete(id uint) error {
	return dao.db.Delete(&model.ProxySet{}, id).Error
}

// FindByID 根据ID获取代理设置
func (dao *ProxySetDAO) FindByID(id uint) (*model.ProxySet, error) {
	var result model.ProxySet
	err := dao.db.First(&result, id).Error
	if err != nil {
		return nil, err
	}
	return &result, nil
}

// FindByUserID 根据用户ID获取代理设置列表
func (dao *ProxySetDAO) FindByUserID(userID uint) ([]*model.ProxySet, error) {
	var result []*model.ProxySet
	err := dao.db.Where("user_id = ?", userID).Find(&result).Error
	return result, err
}

// List 获取代理设置列表（支持分页）
func (dao *ProxySetDAO) List(page, pageSize int) ([]*model.ProxySet, error) {
	var result []*model.ProxySet
	offset := (page - 1) * pageSize
	err := dao.db.Offset(offset).Limit(pageSize).Find(&result).Error
	return result, err
}

// FindOne 根据条件查询单条代理设置记录
func (dao *ProxySetDAO) FindOne(conditions map[string]interface{}) (*model.ProxySet, error) {
	var result model.ProxySet
	err := dao.db.Where(conditions).First(&result).Error
	if err != nil {
		return nil, err
	}
	return &result, nil
}

// UpdateStatus 更新代理设置状态
func (dao *ProxySetDAO) UpdateStatus(id uint, status int) error {
	return dao.db.Model(&model.ProxySet{}).Where("id = ?", id).Update("status", status).Error
}

// UpdateRate 更新代理设置评分
func (dao *ProxySetDAO) UpdateRate(id uint, rate int) error {
	return dao.db.Model(&model.ProxySet{}).Where("id = ?", id).Update("rate", rate).Error
}

// Count 获取记录总数
func (dao *ProxySetDAO) Count() (int64, error) {
	var count int64
	err := dao.db.Model(&model.ProxySet{}).Count(&count).Error
	return count, err
}
