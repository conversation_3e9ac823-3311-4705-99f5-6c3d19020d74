package handler

import (
	"net/http"
	"rsoc-system-go/model"
	"rsoc-system-go/service"
	"time"

	"github.com/gin-gonic/gin"
	"github.com/sirupsen/logrus"
)

// GoogleReportSyncHandler Google报告同步处理器
type GoogleReportSyncHandler struct {
	syncService *service.GoogleReportSyncService
	logger      *logrus.Logger
}

// NewGoogleReportSyncHandler 创建新的Google报告同步处理器
func NewGoogleReportSyncHandler() *GoogleReportSyncHandler {
	logger := logrus.New()
	return &GoogleReportSyncHandler{
		syncService: service.GetGoogleReportSyncService(),
		logger:      logger,
	}
}

// SyncReportData 同步Google报告数据
func (h *GoogleReportSyncHandler) SyncReportData(c *gin.Context) {
	var request model.GoogleReportSyncRequest

	// 解析请求参数
	if err := c.ShouldBindQuery(&request); err != nil {
		h.logger.Errorf("解析同步请求参数失败: %v", err)
		c.<PERSON>(http.StatusBadRequest, model.GoogleReportSyncResponse{
			Code: 400,
			Msg:  "请求参数格式错误: " + err.Error(),
			Time: time.Now().Unix(),
		})
		return
	}

	// 验证必需参数
	if request.SecurityKey == "" {
		c.JSON(http.StatusBadRequest, model.GoogleReportSyncResponse{
			Code: 400,
			Msg:  "security_key 不能为空",
			Time: time.Now().Unix(),
		})
		return
	}

	if request.DataDate == "" {
		c.JSON(http.StatusBadRequest, model.GoogleReportSyncResponse{
			Code: 400,
			Msg:  "data_date 不能为空",
			Time: time.Now().Unix(),
		})
		return
	}

	// 验证日期格式
	if _, err := time.Parse("2006-01-02", request.DataDate); err != nil {
		c.JSON(http.StatusBadRequest, model.GoogleReportSyncResponse{
			Code: 400,
			Msg:  "data_date 格式错误，应为 YYYY-MM-DD",
			Time: time.Now().Unix(),
		})
		return
	}

	h.logger.Infof("开始同步Google报告数据: date=%s, security_key=%s", request.DataDate, request.SecurityKey)

	// 执行同步
	ctx := c.Request.Context()
	response, err := h.syncService.SyncGoogleReportData(ctx, &request)
	if err != nil {
		h.logger.Errorf("同步Google报告数据失败: %v", err)
		c.JSON(http.StatusInternalServerError, model.GoogleReportSyncResponse{
			Code: 500,
			Msg:  "同步失败: " + err.Error(),
			Time: time.Now().Unix(),
		})
		return
	}

	h.logger.Infof("Google报告数据同步成功: %+v", response)
	c.JSON(http.StatusOK, response)
}

// SyncReportDataRange 按日期范围同步Google报告数据
func (h *GoogleReportSyncHandler) SyncReportDataRange(c *gin.Context) {
	securityKey := c.Query("security_key")
	startDate := c.Query("start_date")
	endDate := c.Query("end_date")

	// 验证必需参数
	if securityKey == "" {
		c.JSON(http.StatusBadRequest, model.GoogleReportSyncResponse{
			Code: 400,
			Msg:  "security_key 不能为空",
			Time: time.Now().Unix(),
		})
		return
	}

	if startDate == "" {
		c.JSON(http.StatusBadRequest, model.GoogleReportSyncResponse{
			Code: 400,
			Msg:  "start_date 不能为空",
			Time: time.Now().Unix(),
		})
		return
	}

	if endDate == "" {
		c.JSON(http.StatusBadRequest, model.GoogleReportSyncResponse{
			Code: 400,
			Msg:  "end_date 不能为空",
			Time: time.Now().Unix(),
		})
		return
	}

	// 验证日期格式
	if _, err := time.Parse("2006-01-02", startDate); err != nil {
		c.JSON(http.StatusBadRequest, model.GoogleReportSyncResponse{
			Code: 400,
			Msg:  "start_date 格式错误，应为 YYYY-MM-DD",
			Time: time.Now().Unix(),
		})
		return
	}

	if _, err := time.Parse("2006-01-02", endDate); err != nil {
		c.JSON(http.StatusBadRequest, model.GoogleReportSyncResponse{
			Code: 400,
			Msg:  "end_date 格式错误，应为 YYYY-MM-DD",
			Time: time.Now().Unix(),
		})
		return
	}

	h.logger.Infof("开始批量同步Google报告数据: start_date=%s, end_date=%s", startDate, endDate)

	// 执行批量同步
	ctx := c.Request.Context()
	response, err := h.syncService.SyncGoogleReportDataByDateRange(ctx, securityKey, startDate, endDate)
	if err != nil {
		h.logger.Errorf("批量同步Google报告数据失败: %v", err)
		c.JSON(http.StatusInternalServerError, model.GoogleReportSyncResponse{
			Code: 500,
			Msg:  "批量同步失败: " + err.Error(),
			Time: time.Now().Unix(),
		})
		return
	}

	h.logger.Infof("Google报告数据批量同步成功: %+v", response)
	c.JSON(http.StatusOK, response)
}

// AutoSyncReportData 自动同步最近7天的Google报告数据
func (h *GoogleReportSyncHandler) AutoSyncReportData(c *gin.Context) {
	securityKey := c.Query("security_key")

	// 验证必需参数
	if securityKey == "" {
		c.JSON(http.StatusBadRequest, model.GoogleReportSyncResponse{
			Code: 400,
			Msg:  "security_key 不能为空",
			Time: time.Now().Unix(),
		})
		return
	}

	// 计算最近7天的日期范围
	endDate := time.Now().Format("2006-01-02")
	startDate := time.Now().AddDate(0, 0, -7).Format("2006-01-02")

	h.logger.Infof("开始自动同步最近7天Google报告数据: %s 到 %s", startDate, endDate)

	// 执行批量同步
	ctx := c.Request.Context()
	response, err := h.syncService.SyncGoogleReportDataByDateRange(ctx, securityKey, startDate, endDate)
	if err != nil {
		h.logger.Errorf("自动同步Google报告数据失败: %v", err)
		c.JSON(http.StatusInternalServerError, model.GoogleReportSyncResponse{
			Code: 500,
			Msg:  "自动同步失败: " + err.Error(),
			Time: time.Now().Unix(),
		})
		return
	}

	h.logger.Infof("Google报告数据自动同步成功: %+v", response)
	c.JSON(http.StatusOK, response)
}
