package dao

import (
	"rsoc-system-go/model"

	"gorm.io/gorm"
)

type AutoTaskDao struct {
	db *gorm.DB
}

func NewAutoTaskDao(db *gorm.DB) *AutoTaskDao {
	return &AutoTaskDao{
		db: db,
	}
}

// Create 创建自动任务
func (d *AutoTaskDao) Create(task *model.AutoTask) error {
	return d.db.Save(task).Error
}

// Update 更新自动任务
func (d *AutoTaskDao) Update(task *model.AutoTask) error {
	return d.db.Save(task).Error
}

// GetByID 根据ID获取任务
func (d *AutoTaskDao) GetByID(id int) (*model.AutoTask, error) {
	var task model.AutoTask
	err := d.db.First(&task, id).Error
	if err != nil {
		return nil, err
	}
	return &task, nil
}

// GetByTaskID 根据TaskID获取任务
func (d *AutoTaskDao) GetByTaskID(taskID string) (*model.AutoTask, error) {
	var task model.AutoTask
	err := d.db.Where("task_id = ?", taskID).First(&task).Error
	if err != nil {
		return nil, err
	}
	return &task, nil
}

// GetByClickID 根据ClickID获取任务
func (d *AutoTaskDao) GetByClickID(clickID string) (*model.AutoTask, error) {
	var task model.AutoTask
	err := d.db.Where("click_id = ?", clickID).First(&task).Error
	if err != nil {
		return nil, err
	}
	return &task, nil
}

// List 获取任务列表
func (d *AutoTaskDao) List(page, pageSize int) ([]model.AutoTask, int64, error) {
	var tasks []model.AutoTask
	var total int64

	err := d.db.Model(&model.AutoTask{}).Count(&total).Error
	if err != nil {
		return nil, 0, err
	}

	err = d.db.Offset((page - 1) * pageSize).Limit(pageSize).Find(&tasks).Error
	if err != nil {
		return nil, 0, err
	}

	return tasks, total, nil
}

// UpdateStatus 更新任务状态
func (d *AutoTaskDao) UpdateStatus(id int, status int) error {
	return d.db.Model(&model.AutoTask{}).Where("id = ?", id).Update("status", status).Error
}

// UpdateConver 更新转化状态
func (d *AutoTaskDao) UpdateConver(id int, conver int) error {
	return d.db.Model(&model.AutoTask{}).Where("id = ?", id).Update("conver", conver).Error
}

// Delete 删除任务
func (d *AutoTaskDao) Delete(id int) error {
	return d.db.Delete(&model.AutoTask{}, id).Error
}

// DeleteByTaskID 根据TaskID删除任务
func (d *AutoTaskDao) DeleteByTaskID(taskID string) error {
	return d.db.Delete(&model.AutoTask{}, "task_id = ?", taskID).Error
}
