[GIN-debug] [WARNING] Creating an Engine instance with the Logger and Recovery middleware already attached.

[GIN-debug] [WARNING] Running in "debug" mode. Switch to "release" mode in production.
 - using env:	export GIN_MODE=release
 - using code:	gin.SetMode(gin.ReleaseMode)

2025/04/01 05:01:27 mysql.go:41: [mysql] Initializing MySQL instance: default
2025/04/01 05:01:27 mysql.go:42: [mysql] DSN: root:root@tcp(127.0.0.1:3306)/rosc?charset=utf8mb4&parseTime=true&loc=Local
[GIN-debug] GET    /swagger/*any             --> github.com/swaggo/gin-swagger.CustomWrapHandler.func1 (5 handlers)
[GIN-debug] GET    /assets/*filepath         --> github.com/gin-gonic/gin.(*RouterGroup).createStaticHandler.func1 (5 handlers)
[GIN-debug] HEAD   /assets/*filepath         --> github.com/gin-gonic/gin.(*RouterGroup).createStaticHandler.func1 (5 handlers)
[GIN-debug] GET    /                         --> rosc-system-go/server.LoadTmpl.func1 (5 handlers)
2025/04/01 05:01:27 main.go:53: 守护进程启动失败: daemon: Non-POSIX OS is not supported
2025/04/01 05:01:27 main.go:61: 守护进程启动成功
2025/04/01 05:01:27 main.go:93: 服务器已启动在端口 8080
2025/04/01 05:01:27 redis.go:125: [redis] Main Redis instance connected successfully
2025/04/01 05:01:28 mysql.go:93: [mysql] MySQL instance default connected successfully
2025/04/01 05:01:28 mysql.go:99: [mysql] Default MySQL instance set successfully
2025/04/01 05:02:20 main.go:97: 正在关闭服务器...
2025/04/01 05:02:20 main.go:104: 正在停止调度器...
2025/04/01 05:02:20 main.go:106: 调度器已停止
2025/04/01 05:02:20 main.go:113: 服务器已成功关闭
[GIN-debug] [WARNING] Creating an Engine instance with the Logger and Recovery middleware already attached.

[GIN-debug] [WARNING] Running in "debug" mode. Switch to "release" mode in production.
 - using env:	export GIN_MODE=release
 - using code:	gin.SetMode(gin.ReleaseMode)

2025/04/01 05:02:25 mysql.go:41: [mysql] Initializing MySQL instance: default
2025/04/01 05:02:25 mysql.go:42: [mysql] DSN: root:root@tcp(127.0.0.1:3306)/rosc?charset=utf8mb4&parseTime=true&loc=Local
[GIN-debug] GET    /swagger/*any             --> github.com/swaggo/gin-swagger.CustomWrapHandler.func1 (5 handlers)
2025/04/01 05:02:25 main.go:53: 守护进程启动失败: daemon: Non-POSIX OS is not supported
2025/04/01 05:02:25 main.go:61: 守护进程启动成功
2025/04/01 05:02:25 main.go:93: 服务器已启动在端口 8080
2025/04/01 05:02:25 redis.go:125: [redis] Main Redis instance connected successfully
2025/04/01 05:02:29 mysql.go:93: [mysql] MySQL instance default connected successfully
2025/04/01 05:02:29 mysql.go:99: [mysql] Default MySQL instance set successfully
2025/04/01 05:18:28 main.go:97: 正在关闭服务器...
2025/04/01 05:18:28 main.go:104: 正在停止调度器...
2025/04/01 05:18:28 main.go:106: 调度器已停止
2025/04/01 05:18:28 main.go:113: 服务器已成功关闭
[GIN-debug] [WARNING] Creating an Engine instance with the Logger and Recovery middleware already attached.

[GIN-debug] [WARNING] Running in "debug" mode. Switch to "release" mode in production.
 - using env:	export GIN_MODE=release
 - using code:	gin.SetMode(gin.ReleaseMode)

[GIN-debug] GET    /swagger/*any             --> github.com/swaggo/gin-swagger.CustomWrapHandler.func1 (5 handlers)
2025/04/01 05:18:38 main.go:53: 守护进程启动失败: daemon: Non-POSIX OS is not supported
2025/04/01 05:18:38 mysql.go:41: [mysql] Initializing MySQL instance: default
2025/04/01 05:18:38 mysql.go:42: [mysql] DSN: root:root@tcp(127.0.0.1:3306)/rosc?charset=utf8mb4&parseTime=true&loc=Local
2025/04/01 05:18:38 main.go:61: 守护进程启动成功
2025/04/01 05:18:38 main.go:93: 服务器已启动在端口 8080
2025/04/01 05:18:38 redis.go:125: [redis] Main Redis instance connected successfully
2025/04/01 05:18:38 mysql.go:93: [mysql] MySQL instance default connected successfully
2025/04/01 05:18:38 mysql.go:99: [mysql] Default MySQL instance set successfully
2025/04/01 05:19:14 main.go:97: 正在关闭服务器...
2025/04/01 05:19:14 main.go:104: 正在停止调度器...
2025/04/01 05:19:14 main.go:106: 调度器已停止
2025/04/01 05:19:14 main.go:113: 服务器已成功关闭
