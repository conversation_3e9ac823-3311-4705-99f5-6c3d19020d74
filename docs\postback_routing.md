# Postback 路由判断逻辑说明

## 概述

系统现在支持基于 URL 路径的智能路由判断，可以根据不同的请求路径自动跳转到相应的处理方法。这样可以更清晰地区分配置设置和数据接收功能。

## 路由规则

### 1. 专用路径（推荐使用）

#### 配置设置路径
- `/rsoc/postback/setup` - 专用于 postback 配置设置
- `/rsoc/postback/config` - 配置设置的别名路径

#### 数据接收路径
- `/rsoc/postback/receive` - 专用于接收 postback 数据
- `/rsoc/postback/callback` - 数据接收的别名路径

### 2. 智能判断路径

#### 通用路径 `/rsoc/postback`
系统会根据以下规则自动判断处理方式：

1. **POST 请求 + security_key 参数** → 配置设置
   - 如果是 POST 请求且包含 `security_key` 参数（Query 或 Form），则判断为配置设置

2. **其他情况** → 数据接收
   - GET 请求默认为数据接收
   - POST 请求但不包含 `security_key` 参数，默认为数据接收

## 判断逻辑流程图

```
请求到达 /rsoc/postback/*
         ↓
    检查 URL 路径
         ↓
┌─────────────────────────────────────┐
│ 路径匹配检查                        │
├─────────────────────────────────────┤
│ /rsoc/postback/setup     → 配置设置 │
│ /rsoc/postback/config    → 配置设置 │
│ /rsoc/postback/receive   → 数据接收 │
│ /rsoc/postback/callback  → 数据接收 │
└─────────────────────────────────────┘
         ↓ (如果是 /rsoc/postback)
    智能判断逻辑
         ↓
┌─────────────────────────────────────┐
│ POST + security_key?                │
├─────────────────────────────────────┤
│ 是  → 配置设置                      │
│ 否  → 数据接收                      │
└─────────────────────────────────────┘
```

## 使用示例

### 配置设置示例

```bash
# 方式1：使用专用路径（推荐）
curl -X POST 'http://localhost:8080/rsoc/postback/setup' \
  -H 'Content-Type: application/x-www-form-urlencoded' \
  -d 'security_key=your_key&domain_name=http://example.com&campaign=test'

# 方式2：使用别名路径
curl -X POST 'http://localhost:8080/rsoc/postback/config' \
  -H 'Content-Type: application/x-www-form-urlencoded' \
  -d 'security_key=your_key&domain_name=http://example.com&campaign=test'

# 方式3：使用通用路径（自动判断）
curl -X POST 'http://localhost:8080/rsoc/postback' \
  -H 'Content-Type: application/x-www-form-urlencoded' \
  -d 'security_key=your_key&domain_name=http://example.com&campaign=test'
```

### 数据接收示例

```bash
# 方式1：使用专用路径（推荐）
curl 'http://localhost:8080/rsoc/postback/receive?campaign=123&payout=1.50'

# 方式2：使用回调路径
curl 'http://localhost:8080/rsoc/postback/callback?campaign=123&payout=1.50'

# 方式3：使用通用路径（自动判断）
curl 'http://localhost:8080/rsoc/postback?campaign=123&payout=1.50'

# 方式4：POST 方式接收数据
curl -X POST 'http://localhost:8080/rsoc/postback/receive' \
  -H 'Content-Type: application/x-www-form-urlencoded' \
  -d 'campaign=123&payout=1.50'
```

## 优势

### 1. 清晰的功能分离
- 配置设置和数据接收使用不同的路径
- 便于日志分析和监控
- 减少误操作的可能性

### 2. 向后兼容
- 保持原有 `/rsoc/postback` 路径的兼容性
- 现有集成无需修改即可继续工作
- 智能判断逻辑确保正确处理

### 3. 灵活性
- 支持多种路径选择
- 适应不同的集成需求
- 便于第三方平台集成

## 最佳实践

### 1. 新集成推荐
- **配置设置**：使用 `/rsoc/postback/setup`
- **数据接收**：使用 `/rsoc/postback/receive`

### 2. 第三方平台集成
- **AdTech 平台**：可以继续使用 `/rsoc/postback`
- **SedoTMP 平台**：推荐使用 `/rsoc/postback/callback`
- **自定义集成**：根据需要选择合适的路径

### 3. 监控和日志
```bash
# 查看配置设置相关日志
grep "postback/setup\|postback/config" /var/log/app.log

# 查看数据接收相关日志
grep "postback/receive\|postback/callback" /var/log/app.log

# 查看自动判断日志
grep "postback.*security_key" /var/log/app.log
```

## 故障排除

### 常见问题

1. **配置请求被当作数据接收处理**
   - 检查是否使用了正确的路径
   - 确认 POST 请求包含 `security_key` 参数
   - 建议使用专用路径 `/rsoc/postback/setup`

2. **数据接收请求被当作配置处理**
   - 检查请求中是否意外包含了 `security_key` 参数
   - 建议使用专用路径 `/rsoc/postback/receive`

3. **路径不存在错误**
   - 确认路由配置正确
   - 检查应用是否正确重启

### 调试方法

```bash
# 测试路径判断逻辑
curl -v 'http://localhost:8080/rsoc/postback/setup' -d 'security_key=test'
curl -v 'http://localhost:8080/rsoc/postback/receive?campaign=test'
curl -v 'http://localhost:8080/rsoc/postback' -d 'security_key=test'
curl -v 'http://localhost:8080/rsoc/postback?campaign=test'
```

## 技术实现

### 路由配置
```go
// 支持多种路径的路由配置
apis.Any("/rsoc/postback", backHandler.RsocHandler)
apis.Any("/rsoc/postback/setup", backHandler.RsocHandler)
apis.Any("/rsoc/postback/config", backHandler.RsocHandler)
apis.Any("/rsoc/postback/receive", backHandler.RsocHandler)
apis.Any("/rsoc/postback/callback", backHandler.RsocHandler)
```

### 判断逻辑
```go
func (h *PostBackHandler) RsocHandler(c *gin.Context) {
    path := c.Request.URL.Path
    
    switch {
    case path == "/rsoc/postback/setup" || path == "/rsoc/postback/config":
        h.handlePostbackSetup(c)
    case path == "/rsoc/postback/receive" || path == "/rsoc/postback/callback":
        h.handlePostbackReceive(c)
    case c.Request.Method == "POST" && (c.Query("security_key") != "" || c.PostForm("security_key") != ""):
        h.handlePostbackSetup(c)
    default:
        h.handlePostbackReceive(c)
    }
}
```
