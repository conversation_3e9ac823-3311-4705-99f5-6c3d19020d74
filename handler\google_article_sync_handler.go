package handler

import (
	"net/http"
	"rsoc-system-go/service"
	"strconv"

	"github.com/gin-gonic/gin"
)

// GoogleArticleSyncHandler GoogleArticle同步处理器
type GoogleArticleSyncHandler struct {
	syncService *service.GoogleArticleSyncService
}

// NewGoogleArticleSyncHandler 创建新的GoogleArticle同步处理器
func NewGoogleArticleSyncHandler() *GoogleArticleSyncHandler {
	return &GoogleArticleSyncHandler{
		syncService: service.GetGoogleArticleSyncService(),
	}
}

// SyncAll 同步所有待审核的GoogleArticle
func (h *GoogleArticleSyncHandler) SyncAll(c *gin.Context) {
	// 执行同步
	result, err := h.syncService.SyncGoogleArticles(c.Request.Context())
	if err != nil {
		c.JSON(http.StatusInternalServerError, gin.H{
			"code":    500,
			"message": "同步失败: " + err.Error(),
		})
		return
	}

	// 返回同步结果
	c.J<PERSON>(http.StatusOK, gin.H{
		"code":    1,
		"message": "同步完成",
		"data":    result,
	})
}

// SyncById 同步指定ID的GoogleArticle
func (h *GoogleArticleSyncHandler) SyncById(c *gin.Context) {
	// 获取ID参数
	idStr := c.Param("id")
	id, err := strconv.ParseUint(idStr, 10, 32)
	if err != nil {
		c.JSON(http.StatusBadRequest, gin.H{
			"code":    400,
			"message": "无效的ID参数",
		})
		return
	}

	// 执行同步
	article, err := h.syncService.SyncGoogleArticleByID(c.Request.Context(), uint(id))
	if err != nil {
		c.JSON(http.StatusInternalServerError, gin.H{
			"code":    500,
			"message": "同步失败: " + err.Error(),
		})
		return
	}

	// 返回同步结果
	c.JSON(http.StatusOK, gin.H{
		"code":    200,
		"message": "同步完成",
		"data":    article,
	})
}
