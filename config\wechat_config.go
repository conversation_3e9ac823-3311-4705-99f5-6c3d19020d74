package config

import (
	"fmt"
	"os"
)

var WcConfig *WechatConfig

type WechatConfig struct {
	AppID          string
	AppSecret      string
	Token          string
	EncodingAESKey string
}

func init() {
	Wechat := os.Getenv("Wechat")
	if Wechat == "false" || Wechat == "" {
		return
	}
	fmt.Println("Wechat config initial")
	AppID := os.Getenv("AppID")
	AppSecret := os.Getenv("AppSecret")
	Token := os.Getenv("Token")
	EncodingAESKey := os.Getenv("EncodingAESKey")
	WcConfig = &WechatConfig{
		AppID:          AppID,
		AppSecret:      AppSecret,
		Token:          Token,
		EncodingAESKey: EncodingAESKey,
	}

}
