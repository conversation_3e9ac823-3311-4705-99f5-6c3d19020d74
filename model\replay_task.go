package model

import (
	"database/sql"
)

// ReplayTask 重放任务数据
type ReplayTask struct {
	ID             int          `json:"id" gorm:"primary_key"`
	Name           string       `json:"name" gorm:"column:name;type:varchar(100)" comment:"名称"`
	TaskID         string       `json:"task_id" gorm:"column:task_id;type:varchar(100)" comment:"历史任务id"`
	TaskDate       string       `json:"task_date" gorm:"column:task_date;type:varchar(50)" comment:"历史任务日期"`
	TaskCreateDate string       `json:"task_create_date" gorm:"column:task_create_date;type:varchar(50)" comment:"任务创建时间"`
	TaskNumber     int64        `json:"task_number" gorm:"column:task_number" comment:"任务数"`
	CreateTime     sql.NullTime `json:"create_time" gorm:"column:create_time" comment:"创建时间"`
	Status         int          `json:"status" gorm:"column:status" comment:"1 启动 2停用"`
	Repeat         string       `json:"repeat" gorm:"column:repeat;type:varchar(2)" comment:"1 不重复 2重复"`
	UpdateTime     sql.NullTime `json:"update_time" gorm:"column:update_time" comment:"修改时间"`
	CreateBy       string       `json:"create_by" gorm:"column:create_by;type:varchar(100)" comment:"创建人"`
	UpdateBy       string       `json:"update_by" gorm:"column:update_by;type:varchar(100)" comment:"修改人"`
	Flag           int          `json:"flag" gorm:"column:flag" comment:"1 成功 2失败"`
	Note           string       `json:"note" gorm:"column:note;type:text" comment:"备注"`
	TaskDataId     int          `json:"task_data_id" gorm:"column:task_data_id"`
	ProxySetId     int          `json:"proxy_set_id" gorm:"column:proxy_set_id"`
}

// TableName 表名
func (r *ReplayTask) TableName() string {
	return "replay_task"
}
