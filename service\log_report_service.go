package service

import (
	"bytes"
	"context"
	"encoding/json"
	"fmt"
	"log"
	"net/http"
	"sync"
	"time"
)

// LogReport 日志上报结构
type LogReport struct {
	Timestamp    int64  `json:"timestamp"`
	Level        string `json:"level"`
	Message      string `json:"message"`
	Stack        string `json:"stack,omitempty"`
	RestartCount int    `json:"restart_count,omitempty"`
	LastRestart  int64  `json:"last_restart,omitempty"`
}

// LogReportService 日志上报服务
type LogReportService struct {
	reportURL     string
	maxRetries    int
	retryInterval time.Duration
	queue         chan LogReport
	wg            sync.WaitGroup
	ctx           context.Context
	cancel        context.CancelFunc
}

// NewLogReportService 创建新的日志上报服务
func NewLogReportService(reportURL string, maxRetries int, retryInterval time.Duration) *LogReportService {
	ctx, cancel := context.WithCancel(context.Background())
	service := &LogReportService{
		reportURL:     reportURL,
		maxRetries:    maxRetries,
		retryInterval: retryInterval,
		queue:         make(chan <PERSON>g<PERSON>eport, 1000), // 缓冲队列，避免阻塞
		ctx:           ctx,
		cancel:        cancel,
	}

	// 启动后台处理协程
	go service.processQueue()

	return service
}

// Report 异步上报日志
func (s *LogReportService) Report(level string, message string, stack string) {
	report := LogReport{
		Timestamp: time.Now().Unix(),
		Level:     level,
		Message:   message,
		Stack:     stack,
	}

	// 尝试发送到队列，如果队列满了则记录本地日志
	select {
	case s.queue <- report:
		// 成功加入队列
	default:
		log.Printf("日志上报队列已满，记录到本地: %+v", report)
	}
}

// processQueue 处理上报队列
func (s *LogReportService) processQueue() {
	for {
		select {
		case <-s.ctx.Done():
			return
		case report := <-s.queue:
			s.wg.Add(1)
			go func(r LogReport) {
				defer s.wg.Done()
				s.sendReport(r)
			}(report)
		}
	}
}

// sendReport 发送单条日志
func (s *LogReportService) sendReport(report LogReport) {
	data, err := json.Marshal(report)
	if err != nil {
		log.Printf("日志序列化失败: %v", err)
		return
	}

	for i := 0; i < s.maxRetries; i++ {
		if err := s.doSendReport(data); err != nil {
			log.Printf("日志上报失败(尝试 %d/%d): %v", i+1, s.maxRetries, err)
			time.Sleep(s.retryInterval)
			continue
		}
		return
	}

	log.Printf("日志上报最终失败，已达到最大重试次数 %d", s.maxRetries)
}

// doSendReport 执行实际的上报请求
func (s *LogReportService) doSendReport(data []byte) error {
	ctx, cancel := context.WithTimeout(context.Background(), 10*time.Second)
	defer cancel()

	req, err := http.NewRequestWithContext(ctx, "POST", s.reportURL, bytes.NewBuffer(data))
	if err != nil {
		return fmt.Errorf("创建请求失败: %v", err)
	}

	req.Header.Set("Content-Type", "application/json")

	resp, err := http.DefaultClient.Do(req)
	if err != nil {
		return fmt.Errorf("发送请求失败: %v", err)
	}
	defer resp.Body.Close()

	if resp.StatusCode != http.StatusOK {
		return fmt.Errorf("上报失败，状态码: %d", resp.StatusCode)
	}

	return nil
}

// Stop 停止服务
func (s *LogReportService) Stop() {
	s.cancel()
	s.wg.Wait()
	close(s.queue)
}

// GetQueueSize 获取当前队列大小
func (s *LogReportService) GetQueueSize() int {
	return len(s.queue)
}
