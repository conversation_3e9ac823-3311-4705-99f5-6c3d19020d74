# Facebook Insights 表结构迁移脚本 (PowerShell版本)
# 用途：添加扩展字段以支持新的JSON数据格式
# 版本：1.0
# 创建时间：2025-01-19

param(
    [Parameter(Position=0)]
    [ValidateSet("migrate", "rollback", "verify", "help")]
    [string]$Action = "migrate"
)

# 日志函数
function Write-Info {
    param([string]$Message)
    Write-Host "[INFO] $Message" -ForegroundColor Blue
}

function Write-Success {
    param([string]$Message)
    Write-Host "[SUCCESS] $Message" -ForegroundColor Green
}

function Write-Warning {
    param([string]$Message)
    Write-Host "[WARNING] $Message" -ForegroundColor Yellow
}

function Write-Error {
    param([string]$Message)
    Write-Host "[ERROR] $Message" -ForegroundColor Red
}

# 检查MySQL连接
function Test-MySQLConnection {
    Write-Info "检查MySQL连接..."
    
    $mysqlUrl = $env:MYSQL_URL
    if (-not $mysqlUrl) {
        Write-Error "MYSQL_URL 环境变量未设置"
        Write-Host "请设置 MYSQL_URL 环境变量，格式如下："
        Write-Host "`$env:MYSQL_URL='user:password@tcp(host:port)/database?charset=utf8mb4&parseTime=True&loc=Local'"
        exit 1
    }
    
    # 解析MySQL URL (简化版本，实际项目中可能需要更复杂的解析)
    Write-Info "MySQL URL: $mysqlUrl"
    Write-Success "MySQL连接配置已设置"
}

# 备份表结构
function Backup-TableStructure {
    Write-Info "备份当前表结构..."
    
    $backupDir = "./backups"
    if (-not (Test-Path $backupDir)) {
        New-Item -ItemType Directory -Path $backupDir | Out-Null
    }
    
    $timestamp = Get-Date -Format "yyyyMMdd_HHmmss"
    $backupFile = "$backupDir/facebook_insights_structure_$timestamp.sql"
    
    Write-Info "表结构备份将保存到: $backupFile"
    
    # 注意：在实际环境中，您需要安装MySQL客户端工具
    # 这里只是创建一个占位符文件
    "-- Facebook Insights 表结构备份 - $timestamp" | Out-File -FilePath $backupFile -Encoding UTF8
    
    Write-Success "表结构备份完成"
}

# 执行迁移
function Invoke-Migration {
    Write-Info "执行数据库迁移..."
    
    $migrationFile = "./migrations/001_add_facebook_insights_extended_fields.sql"
    
    if (-not (Test-Path $migrationFile)) {
        Write-Error "迁移文件不存在: $migrationFile"
        exit 1
    }
    
    Write-Info "迁移文件: $migrationFile"
    Write-Info "注意：请手动执行SQL文件或使用MySQL客户端工具"
    Write-Info "或者重启应用程序，GORM将自动执行迁移"
    
    Write-Success "迁移准备完成"
}

# 验证迁移结果
function Test-Migration {
    Write-Info "验证迁移结果..."
    
    $newFields = @(
        "campaign_name", "platform", "country", "hour",
        "related_links_requests", "related_links_impressions", "related_links_clicks", "related_links_rpm",
        "ad_requests", "matched_ad_requests", "ad_impressions",
        "ctr", "ad_ctr", "ad_rpm", "cr", "revenue", "create_time"
    )
    
    Write-Info "需要验证的字段: $($newFields -join ', ')"
    Write-Info "请手动检查数据库表结构或重启应用程序进行自动迁移"
    
    Write-Success "验证准备完成"
}

# 显示使用帮助
function Show-Help {
    Write-Host "Facebook Insights 表结构迁移脚本 (PowerShell版本)"
    Write-Host ""
    Write-Host "用法: .\migrate_facebook_insights.ps1 [选项]"
    Write-Host ""
    Write-Host "选项:"
    Write-Host "  migrate     执行迁移（默认）"
    Write-Host "  rollback    回滚迁移"
    Write-Host "  verify      仅验证迁移结果"
    Write-Host "  help        显示此帮助信息"
    Write-Host ""
    Write-Host "环境变量:"
    Write-Host "  MYSQL_URL   MySQL连接字符串（必需）"
    Write-Host ""
    Write-Host "示例:"
    Write-Host "  `$env:MYSQL_URL='user:pass@tcp(localhost:3306)/dbname?charset=utf8mb4&parseTime=True&loc=Local'"
    Write-Host "  .\migrate_facebook_insights.ps1 migrate"
    Write-Host ""
    Write-Host "注意事项:"
    Write-Host "  1. 确保已设置MYSQL_URL环境变量"
    Write-Host "  2. 建议在执行前备份数据库"
    Write-Host "  3. 可以通过重启应用程序让GORM自动执行迁移"
}

# 执行回滚
function Invoke-Rollback {
    Write-Warning "准备执行回滚操作..."
    Write-Warning "此操作将删除所有扩展字段中的数据！"
    
    $confirm = Read-Host "确认执行回滚？(yes/no)"
    if ($confirm -ne "yes") {
        Write-Info "回滚操作已取消"
        exit 0
    }
    
    $rollbackFile = "./migrations/001_rollback_facebook_insights_extended_fields.sql"
    
    if (-not (Test-Path $rollbackFile)) {
        Write-Error "回滚文件不存在: $rollbackFile"
        exit 1
    }
    
    Write-Info "回滚文件: $rollbackFile"
    Write-Info "请手动执行SQL文件进行回滚"
    
    Write-Success "回滚准备完成"
}

# 主函数
function Main {
    switch ($Action) {
        "migrate" {
            Test-MySQLConnection
            Backup-TableStructure
            Invoke-Migration
            Test-Migration
            Write-Success "迁移完成！"
        }
        "rollback" {
            Test-MySQLConnection
            Invoke-Rollback
            Write-Success "回滚完成！"
        }
        "verify" {
            Test-MySQLConnection
            Test-Migration
        }
        "help" {
            Show-Help
        }
        default {
            Write-Error "未知选项: $Action"
            Show-Help
            exit 1
        }
    }
}

# 执行主函数
Main
