package handler

import (
	"log"
	"net/http"
	"rsoc-system-go/dao"
	"rsoc-system-go/model"
	"rsoc-system-go/service"
	"rsoc-system-go/store"
	"strconv"
	"sync"

	"github.com/gin-gonic/gin"
)

// SedoArticleHandler Sedo文章处理器
type SedoArticleHandler struct {
	service            *service.SedoCampaignService
	sedoArticleService *service.SedoArticleService
	sedoDomainService  *service.SedoDomainService
	googleArticleDAO   *dao.GoogleArticleDAO
	mutex              sync.Mutex // 添加互斥锁
}

// NewSedoArticleHandler 创建新的Sedo文章处理器
func NewSedoArticleHandler(service *service.SedoCampaignService) *SedoArticleHandler {
	return &SedoArticleHandler{
		service:          service,
		googleArticleDAO: dao.NewGoogleArticleDAO(store.DB),
	}
}

// GetArticles 获取文章列表
func (h *SedoArticleHandler) GetArticles(c *gin.Context) {
	// 解析分页参数
	page, _ := strconv.Atoi(c.DefaultQuery("page", "1"))
	size, _ := strconv.Atoi(c.DefaultQuery("size", "10"))
	sort := c.DefaultQuery("sort", "createdDate,desc")
	term := c.DefaultQuery("term", "")

	articles, pageResponse, err := h.sedoArticleService.GetArticles(c.Request.Context(), page, size, sort, term)
	if err != nil {
		c.JSON(http.StatusInternalServerError, gin.H{"error": err.Error()})
		return
	}

	c.JSON(http.StatusOK, gin.H{
		"data": articles,
		"page": pageResponse,
	})
}

// GetArticleByID 根据ID获取文章
func (h *SedoArticleHandler) GetArticleByID(c *gin.Context) {
	id := c.Param("id")

	article, err := h.sedoArticleService.GetArticleByID(c.Request.Context(), id)
	if err != nil {
		c.JSON(http.StatusInternalServerError, gin.H{"error": err.Error()})
		return
	}

	c.JSON(http.StatusOK, gin.H{"data": article})
}

// CreateArticle 创建文章
func (h *SedoArticleHandler) CreateArticle(c *gin.Context) {
	var request model.SedoCreateArticleRequest
	if err := c.ShouldBindJSON(&request); err != nil {
		c.JSON(http.StatusBadRequest, gin.H{"error": err.Error()})
		return
	}

	service.GetSedoArticleService()

	article, err := h.sedoArticleService.CreateArticle(c.Request.Context(), &request)
	if err != nil {
		c.JSON(http.StatusInternalServerError, gin.H{"error": err.Error()})
		return
	}

	c.JSON(http.StatusCreated, gin.H{"data": article})
}

// UpdateArticle 更新文章
func (h *SedoArticleHandler) UpdateArticle(c *gin.Context) {
	id := c.Param("id")

	var request model.SedoUpdateArticleRequest
	if err := c.ShouldBindJSON(&request); err != nil {
		c.JSON(http.StatusBadRequest, gin.H{"error": err.Error()})
		return
	}

	article, err := h.sedoArticleService.UpdateArticle(c.Request.Context(), id, &request)
	if err != nil {
		c.JSON(http.StatusInternalServerError, gin.H{"error": err.Error()})
		return
	}

	c.JSON(http.StatusOK, gin.H{"data": article})
}

// PatchArticle 部分更新文章
func (h *SedoArticleHandler) PatchArticle(c *gin.Context) {
	id := c.Param("id")

	var request model.SedoUpdateArticleRequest
	if err := c.ShouldBindJSON(&request); err != nil {
		c.JSON(http.StatusBadRequest, gin.H{"error": err.Error()})
		return
	}

	article, err := h.sedoArticleService.PatchArticle(c.Request.Context(), id, &request)
	if err != nil {
		c.JSON(http.StatusInternalServerError, gin.H{"error": err.Error()})
		return
	}

	c.JSON(http.StatusOK, gin.H{"data": article})
}

// DeleteArticle 删除文章
func (h *SedoArticleHandler) DeleteArticle(c *gin.Context) {
	id := c.Param("id")

	err := h.sedoArticleService.DeleteArticle(c.Request.Context(), id)
	if err != nil {
		c.JSON(http.StatusInternalServerError, gin.H{"error": err.Error()})
		return
	}

	c.JSON(http.StatusOK, gin.H{"message": "文章删除成功"})
}

// GenerateArticle 生成文章
func (h *SedoArticleHandler) GenerateArticle(c *gin.Context) {
	var params map[string]interface{}
	if err := c.ShouldBindJSON(&params); err != nil {
		c.JSON(http.StatusBadRequest, gin.H{"error": err.Error()})
		return
	}

	// 获取用户ID，如果不存在则使用默认值0
	userId := 0
	userIdInterface, exists := c.Get("userId")
	if exists {
		userId, _ = userIdInterface.(int)
	}

	// 从 params 构建 SedoCreateCampaignRequest
	var request model.SedoCreateCampaignRequest
	request.PublishDomainName = getStringParam(params, "publishDomainName", "")

	// 构建 Article 部分
	keywords := getStringParam(params, "keywords", "")
	var topics []string
	if keywords != "" {
		topics = []string{keywords}
	} else {
		topics = []string{}
	}

	request.Article.Country = getStringParam(params, "country", "")
	request.Article.Locale = getStringParam(params, "language", "")
	request.Article.Title = getStringParam(params, "article_title", "")
	request.Article.Excerpt = getStringParam(params, "article_intro", "")
	request.Article.Topics = topics
	request.Article.Type = "CreateArticle"
	request.Article.FeaturedImage.Generate = true

	// 构建 Campaign 部分
	request.Campaign.Name = getStringParam(params, "campaign_name", "")
	request.Campaign.Type = "CreateCampaign"

	// 构建 TrackingData 部分
	request.Campaign.TrackingData.TrafficSource = getStringParam(params, "traffic_source", "")
	request.Campaign.TrackingData.TrackingMethod = getStringParam(params, "tracking_method", "S2S")

	// 构建 TrackingSettings 部分
	request.Campaign.TrackingData.TrackingSettings.Type = "PixelMetaTrackingSettings"
	request.Campaign.TrackingData.TrackingSettings.PixelMetaPixelId = getStringParam(params, "pixel_id", "")
	request.Campaign.TrackingData.TrackingSettings.PixelMetaLandingPageEvent = "Search"
	request.Campaign.TrackingData.TrackingSettings.PixelMetaClickEvent = "Lead"
	request.Campaign.TrackingData.TrackingSettings.PixelMetaSearchEvent = "Purchase"

	// 创建GoogleArticle对象并保存到数据库，状态设置为处理中
	googleArticle := &model.GoogleArticle{
		Status:           0, // 0:待处理
		Keywords:         keywords,
		Language:         getStringParam(params, "language", ""),
		CreatedBy:        userId,
		TwoDirectoryId:   getIntParam(params, "two_directory_id", 0),
		TwoDirectoryName: getStringParam(params, "two_directory_name", ""),
		Key:              getStringParam(params, "key", ""),
		CampaignName:     getStringParam(params, "campaign_name", ""),
		OfferCategory:    getStringParam(params, "offer_category", ""),
		TrafficSource:    getStringParam(params, "traffic_source", ""),
		TrackingMethod:   getStringParam(params, "tracking_method", ""),
		PixelID:          getStringParam(params, "pixel_id", ""),
		Token:            getStringParam(params, "token", ""),
		EventName:        getStringParam(params, "event_name", ""),
		ArticleTitle:     getStringParam(params, "article_title", ""),
		ArticleIntro:     getStringParam(params, "article_intro", ""),
		RsocId:           getIntParam(params, "rsoc_id", 0),
		Rate:             getIntParam(params, "rate", 0),
		Note:             getStringParam(params, "note", ""),
		URLWPPost:        getStringParam(params, "publishDomainName", ""),
		Type:             2,
	}

	// 使用互斥锁保护数据库操作
	h.mutex.Lock()
	h.googleArticleDAO = dao.NewGoogleArticleDAO(store.DB)
	err := h.googleArticleDAO.Create(googleArticle)
	h.mutex.Unlock()

	if err != nil {
		c.JSON(http.StatusInternalServerError, gin.H{"error": "保存文章信息失败: " + err.Error()})
		return
	}

	// 使用请求的上下文
	ctx := c.Request.Context()

	log.Printf("开始生成文章，ID=%d", googleArticle.ID)

	// 获取API密钥
	keyDao := dao.NewRoscKeyDao(store.DB)
	one, err := keyDao.FindOne(map[string]interface{}{"id": 11})
	if err != nil {
		c.JSON(http.StatusInternalServerError, gin.H{"error": "获取API密钥失败: " + err.Error()})
		return
	}

	// 初始化服务
	h.service.InitializeWithOAuth(one.ClientId, one.Key)

	// 调用service创建广告系列
	article, err := h.service.CreateCampaign(ctx, &request)

	// 获取最新的GoogleArticle对象
	currentArticle, getErr := h.googleArticleDAO.GetByID(googleArticle.ID)

	if getErr != nil {
		c.JSON(http.StatusInternalServerError, gin.H{"error": "获取文章失败: " + getErr.Error()})
		return
	}

	// 更新GoogleArticle对象
	if err != nil {
		// 生成失败，更新状态为失败
		log.Printf("文章生成失败，ID=%d，错误：%v", googleArticle.ID, err)
		currentArticle.Status = 2 // 2:失败
		currentArticle.ResultReason = err.Error()

		h.mutex.Lock()
		updateErr := h.googleArticleDAO.Updates(currentArticle)
		h.mutex.Unlock()

		if updateErr != nil {
			log.Printf("更新文章状态失败，ID=%d，错误：%v", googleArticle.ID, updateErr)
		}

		c.JSON(http.StatusInternalServerError, gin.H{"error": "文章生成失败: " + err.Error()})
		return
	}

	// 生成成功，更新状态和其他字段
	if article != nil {
		log.Printf("文章生成成功，ID=%d，文章ID=%s", googleArticle.ID, article.Id)
		currentArticle.ArticleTitle = article.Article.Title
		currentArticle.ArticleIntro = article.Article.Excerpt
		currentArticle.CampaignId = article.Id // 使用文章ID作为活动ID

		// 如果有发布域名，可以设置相关字段
		if request.PublishDomainName != "" {
			currentArticle.URLWPPost = request.PublishDomainName

			// 设置跟踪URL（如果有）
			if article.TrackingUrl != "" {
				currentArticle.FinalURL = article.TrackingUrl
				log.Printf("设置广告系列跟踪URL：%s", article.TrackingUrl)
			}
		}

		updateErr := h.googleArticleDAO.Updates(currentArticle)

		if updateErr != nil {
			log.Printf("更新文章状态失败，ID=%d，错误：%v", googleArticle.ID, updateErr)
			c.JSON(http.StatusInternalServerError, gin.H{"error": "更新文章状态失败: " + updateErr.Error()})
			return
		}

		log.Printf("更新文章状态成功，ID=%d", googleArticle.ID)
		c.JSON(http.StatusOK, gin.H{
			"message": "文章生成成功",
			"data": gin.H{
				"id":          googleArticle.ID,
				"campaignId":  article.Id,
				"title":       article.Article.Title,
				"excerpt":     article.Article.Excerpt,
				"trackingUrl": article.TrackingUrl,
			},
			"code": 1,
		})
		return
	}

	// 如果article为nil但没有错误，返回一个通用的成功消息
	c.JSON(http.StatusOK, gin.H{
		"message": "操作成功，但未返回文章信息",
		"data": gin.H{
			"id": googleArticle.ID,
		},
		"code": 1,
	})
}

// GetPublishedArticles 获取已发布文章列表
func (h *SedoArticleHandler) GetPublishedArticles(c *gin.Context) {
	// 解析分页参数
	page, _ := strconv.Atoi(c.DefaultQuery("page", "")) // 1
	size, _ := strconv.Atoi(c.DefaultQuery("size", "")) //10
	sort := c.DefaultQuery("sort", "")                  //publishedDate,desc
	term := c.DefaultQuery("term", "")

	articles, pageResponse, err := h.sedoArticleService.GetPublishedArticles(c.Request.Context(), page, size, sort, term)
	if err != nil {
		c.JSON(http.StatusInternalServerError, gin.H{"error": err.Error()})
		return
	}

	c.JSON(http.StatusOK, gin.H{
		"data": articles,
		"page": pageResponse,
	})
}

// GetPublishedArticleByID 根据ID获取已发布文章
func (h *SedoArticleHandler) GetPublishedArticleByID(c *gin.Context) {
	id := c.Param("id")

	article, err := h.sedoArticleService.GetPublishedArticleByID(c.Request.Context(), id)
	if err != nil {
		c.JSON(http.StatusInternalServerError, gin.H{"error": err.Error()})
		return
	}

	c.JSON(http.StatusOK, gin.H{"data": article})
}

// PublishArticle 发布文章
func (h *SedoArticleHandler) PublishArticle(c *gin.Context) {
	var request model.SedoPublishArticleRequest
	if err := c.ShouldBindJSON(&request); err != nil {
		c.JSON(http.StatusBadRequest, gin.H{"error": err.Error()})
		return
	}

	// 获取异步请求标志和引用ID
	async := c.GetHeader("X-Sedo-Request-Flow") == "ASYNC"
	referenceID := c.GetHeader("X-Sedo-Reference-Id")

	article, err := h.sedoArticleService.PublishArticle(c.Request.Context(), &request, async, referenceID)
	if err != nil {
		c.JSON(http.StatusInternalServerError, gin.H{"error": err.Error()})
		return
	}

	// 如果是异步请求且没有立即结果，返回202状态码
	if async && article == nil {
		c.JSON(http.StatusAccepted, gin.H{"message": "文章发布请求已接受，正在处理中"})
		return
	}

	c.JSON(http.StatusOK, gin.H{"data": article})
}

// UnpublishArticle 取消发布文章
func (h *SedoArticleHandler) UnpublishArticle(c *gin.Context) {
	id := c.Param("id")

	err := h.sedoArticleService.UnpublishArticle(c.Request.Context(), id)
	if err != nil {
		c.JSON(http.StatusInternalServerError, gin.H{"error": err.Error()})
		return
	}

	c.JSON(http.StatusOK, gin.H{"message": "文章取消发布成功"})
}

// GetDetailedArticles 获取详细文章列表（包含发布信息）
func (h *SedoArticleHandler) GetDetailedArticles(c *gin.Context) {
	// 解析分页参数
	page, _ := strconv.Atoi(c.DefaultQuery("page", "1"))
	size, _ := strconv.Atoi(c.DefaultQuery("size", "10"))
	sort := c.DefaultQuery("sort", "createdDate,desc")
	term := c.DefaultQuery("term", "")

	articles, pageResponse, err := h.sedoArticleService.GetDetailedArticles(c.Request.Context(), page, size, sort, term)
	if err != nil {
		c.JSON(http.StatusInternalServerError, gin.H{"error": err.Error()})
		return
	}

	c.JSON(http.StatusOK, gin.H{
		"data": articles,
		"page": pageResponse,
	})
}

// GetDetailedArticleByID 根据ID获取详细文章（包含发布信息）
func (h *SedoArticleHandler) GetDetailedArticleByID(c *gin.Context) {
	id := c.Param("id")

	article, err := h.sedoArticleService.GetDetailedArticleByID(c.Request.Context(), id)
	if err != nil {
		c.JSON(http.StatusInternalServerError, gin.H{"error": err.Error()})
		return
	}

	c.JSON(http.StatusOK, gin.H{"data": article})
}

// CampaignReport 报表数据
func (h *SedoArticleHandler) CampaignReport(c *gin.Context) {
	// 解析分页参数
	page, _ := strconv.Atoi(c.DefaultQuery("page", "1"))
	size, _ := strconv.Atoi(c.DefaultQuery("size", "10"))
	sort := c.DefaultQuery("sort", "createdDate,desc")
	term := c.DefaultQuery("term", "")

	articles, pageResponse, err := h.service.CampaignReport(c.Request.Context(), page, size, sort, term)
	if err != nil {
		c.JSON(http.StatusInternalServerError, gin.H{"error": err.Error()})
		return
	}

	c.JSON(http.StatusOK, gin.H{
		"data": articles,
		"page": pageResponse,
	})
}

// SyncCampaignData 同步广告系列数据
func (h *SedoArticleHandler) SyncCampaignData(c *gin.Context) {
	// 获取API密钥
	keyDao := dao.NewRoscKeyDao(store.DB)
	key, err := keyDao.FindOne(map[string]interface{}{"id": 11})
	if err != nil {
		c.JSON(http.StatusInternalServerError, gin.H{"error": "获取API密钥失败: " + err.Error()})
		return
	}

	// 初始化服务
	h.service.InitializeWithOAuth(key.ClientId, key.Key)

	// 查询status为0的GoogleArticle数据
	h.mutex.Lock()
	h.googleArticleDAO = dao.NewGoogleArticleDAO(store.DB)
	articles, err := h.googleArticleDAO.FindByStatus(0)
	h.mutex.Unlock()

	if err != nil {
		c.JSON(http.StatusInternalServerError, gin.H{"error": "查询待处理文章失败: " + err.Error()})
		return
	}

	if len(articles) == 0 {
		c.JSON(http.StatusOK, gin.H{"message": "没有待处理的文章"})
		return
	}

	// 处理结果
	results := make([]map[string]interface{}, 0)

	// 创建上下文
	ctx := c.Request.Context()

	// 处理每条数据
	for _, article := range articles {
		result := map[string]interface{}{
			"id":     article.ID,
			"status": "unchanged",
		}

		// 如果有CampaignId，则调用GetCampaignByID获取广告系列信息
		if article.CampaignId != "" {
			campaign, err := h.service.GetCampaignByID(ctx, article.CampaignId)
			if err != nil {
				result["status"] = "error"
				result["message"] = "获取广告系列数据失败: " + err.Error()
				results = append(results, result)
				continue
			}

			// 根据广告系列的status字段判断状态
			switch campaign.Status {
			case "COMPLETED":
				// 成功状态
				article.Status = 1 // 1:成功
				if campaign.TrackingUrl != "" {
					article.FinalURL = campaign.TrackingUrl
				}
				result["status"] = "success"
				result["message"] = "更新为成功状态"
			case "FAILED", "ERROR", "PROCESSING_ERROR":
				// 失败状态
				article.Status = 2 // 2:失败
				if campaign.ProcessingErrorDetails.Detail != "" {
					article.ResultReason = campaign.ProcessingErrorDetails.Detail
				} else {
					article.ResultReason = "广告系列处理失败"
				}
				result["status"] = "failed"
				result["message"] = "更新为失败状态"
			case "PROCESSING":
				// 待处理状态，保持不变
				result["status"] = "pending"
				result["message"] = "广告系列仍在处理中"
				results = append(results, result)
				continue
			default:
				// 未知状态，保持不变
				result["status"] = "unknown"
				result["message"] = "未知的广告系列状态: " + campaign.Status
				results = append(results, result)
				continue
			}

			// 更新GoogleArticle对象
			h.mutex.Lock()
			updateErr := h.googleArticleDAO.Updates(article)
			h.mutex.Unlock()

			if updateErr != nil {
				result["status"] = "error"
				result["message"] = "更新文章状态失败: " + updateErr.Error()
			}
		} else {
			result["status"] = "skipped"
			result["message"] = "没有关联的广告系列ID"
		}

		results = append(results, result)
	}

	// 返回处理结果
	c.JSON(http.StatusOK, gin.H{
		"message": "广告系列数据同步完成",
		"data":    results,
		"code":    1,
	})
}

// getStringParam 从 map 中获取字符串参数，如果不存在或类型不匹配则返回默认值
func getStringParam(params map[string]interface{}, key string, defaultValue string) string {
	if val, ok := params[key]; ok {
		if strVal, ok := val.(string); ok {
			return strVal
		}
	}
	return defaultValue
}

// getIntParam 从 map 中获取整数参数，如果不存在或类型不匹配则返回默认值
func getIntParam(params map[string]interface{}, key string, defaultValue int) int {
	if val, ok := params[key]; ok {
		switch v := val.(type) {
		case int:
			return v
		case float64:
			return int(v)
		case string:
			if intVal, err := strconv.Atoi(v); err == nil {
				return intVal
			}
		}
	}
	return defaultValue
}

// GetDomains 获取域名列表
func (h *SedoArticleHandler) GetDomains(c *gin.Context) {
	// 解析分页参数
	page, _ := strconv.Atoi(c.DefaultQuery("page", "0"))
	size, _ := strconv.Atoi(c.DefaultQuery("size", "10"))

	// 解析key_id参数
	keyIdStr := c.Query("key")
	if keyIdStr == "" {
		c.JSON(http.StatusBadRequest, gin.H{"error": "key_id参数不能为空"})
		return
	}

	keyId, err := strconv.Atoi(keyIdStr)
	if err != nil {
		c.JSON(http.StatusBadRequest, gin.H{"error": "key_id参数格式错误"})
		return
	}

	// 获取API密钥
	keyDao := dao.NewRoscKeyDao(store.DB)
	key, err := keyDao.FindOne(map[string]interface{}{"id": keyId})
	if err != nil {
		c.JSON(http.StatusInternalServerError, gin.H{"error": "获取API密钥失败: " + err.Error()})
		return
	}

	// 设置OAuth认证凭据
	h.sedoDomainService = service.GetSedoDomainService()
	h.sedoDomainService.SetOAuthCredentials(key.ClientId, key.Key)

	domains, pageResponse, err := h.sedoDomainService.GetDomains(c.Request.Context(), page, size)
	if err != nil {
		c.JSON(http.StatusInternalServerError, gin.H{"error": err.Error()})
		return
	}

	c.JSON(http.StatusOK, gin.H{
		"data": domains,
		"page": pageResponse,
	})
}
