package facebook

import (
	"bytes"
	"encoding/json"
	"fmt"
	"net/http"
)

// API 表示Facebook API客户端
type API struct {
	accessToken string
	client      *http.Client
}

// NewAPI 创建一个新的Facebook API客户端
func NewAPI(accessToken string) *API {
	return &API{
		accessToken: accessToken,
		client:      &http.Client{},
	}
}

// UserData 表示Facebook像素用户数据
type UserData struct {
	ClientIPAddress string `json:"client_ip_address,omitempty"`
	ClientUserAgent string `json:"client_user_agent,omitempty"`
}

// CustomData 表示Facebook像素自定义数据
type CustomData struct {
	// 可以根据需要添加自定义字段
}

// Event 表示Facebook像素事件
type Event struct {
	EventName      string      `json:"event_name"`
	EventTime      int64       `json:"event_time"`
	EventSourceURL string      `json:"event_source_url,omitempty"`
	UserData       *UserData   `json:"user_data,omitempty"`
	CustomData     *CustomData `json:"custom_data,omitempty"`
	ActionSource   string      `json:"action_source"`
}

// EventRequest 表示Facebook像素事件请求
type EventRequest struct {
	PixelID string   `json:"-"`
	Events  []*Event `json:"data"`
}

// ActionSource 定义事件来源类型
const (
	ActionSourceWebsite = "website"
)

// Execute 执行Facebook像素事件请求
func (er *EventRequest) Execute(api *API) (*http.Response, error) {
	url := fmt.Sprintf("https://graph.facebook.com/v18.0/%s/events", er.PixelID)

	// 准备请求体
	body, err := json.Marshal(er)
	if err != nil {
		return nil, err
	}

	// 创建请求
	req, err := http.NewRequest("POST", url, bytes.NewBuffer(body))
	if err != nil {
		return nil, err
	}

	// 设置请求头
	req.Header.Set("Content-Type", "application/json")
	req.Header.Set("Authorization", "Bearer "+api.accessToken)

	// 发送请求
	return api.client.Do(req)
}
