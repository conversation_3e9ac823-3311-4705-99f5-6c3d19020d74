package service

import (
	"context"
	"fmt"
	"rsoc-system-go/model"
	"sync"
)

// SedoDomainService Sedo域名服务
type SedoDomainService struct {
	httpClient *SedoHTTPClient
}

var (
	sedoDomainService     *SedoDomainService
	sedoDomainServiceOnce sync.Once
)

// GetSedoDomainService 获取Sedo域名服务单例
func GetSedoDomainService() *SedoDomainService {
	sedoDomainServiceOnce.Do(func() {
		sedoDomainService = NewSedoDomainService(GetSedoHTTPClient())
	})
	return sedoDomainService
}

// NewSedoDomainService 创建新的Sedo域名服务
func NewSedoDomainService(httpClient *SedoHTTPClient) *SedoDomainService {
	return &SedoDomainService{
		httpClient: httpClient,
	}
}

// GetDomains 获取域名列表
func (s *SedoDomainService) GetDomains(ctx context.Context, page, size int) ([]model.SedoDomain, *model.SedoPageResponse, error) {
	query := s.httpClient.BuildQueryParams(page, size, "", "")

	resp, err := s.httpClient.Get(ctx, "/domains", query, nil)
	if err != nil {
		return nil, nil, fmt.Errorf("获取域名列表请求失败: %v", err)
	}

	var domains []model.SedoDomain
	if err := s.httpClient.ParseResponse(resp, &domains); err != nil {
		return nil, nil, fmt.Errorf("解析域名列表响应失败: %v", err)
	}

	totalCount, totalPages, err := s.httpClient.GetPageHeaders(resp)
	if err != nil {
		return nil, nil, fmt.Errorf("获取分页信息失败: %v", err)
	}

	pageResponse := &model.SedoPageResponse{
		TotalCount: totalCount,
		TotalPages: totalPages,
	}

	return domains, pageResponse, nil
}

// GetDomainByID 根据ID获取域名
func (s *SedoDomainService) GetDomainByID(ctx context.Context, id string) (*model.SedoDomain, error) {
	resp, err := s.httpClient.Get(ctx, fmt.Sprintf("/domains/%s", id), nil, nil)
	if err != nil {
		return nil, fmt.Errorf("获取域名详情请求失败: %v", err)
	}

	var domain model.SedoDomain
	if err := s.httpClient.ParseResponse(resp, &domain); err != nil {
		return nil, fmt.Errorf("解析域名详情响应失败: %v", err)
	}

	return &domain, nil
}

// SetBearerToken 设置Bearer令牌
func (s *SedoDomainService) SetBearerToken(token string) {
	s.httpClient.SetBearerToken(token)
}

// UseOAuth 启用OAuth身份验证
func (s *SedoDomainService) UseOAuth(enable bool) {
	s.httpClient.UseOAuth(enable)
}

// SetOAuthCredentials 设置OAuth凭据
func (s *SedoDomainService) SetOAuthCredentials(clientID, clientSecret string) {
	s.httpClient.SetOAuthCredentials(clientID, clientSecret)
}

// SetOAuthConfig 设置OAuth配置
func (s *SedoDomainService) SetOAuthConfig(tokenEndpoint, clientID, clientSecret, audience, grantType string) {
	oauthClient := GetSedoOAuthClient()
	oauthClient.SetConfig(SedoOAuthConfig{
		TokenEndpoint: tokenEndpoint,
		ClientID:      clientID,
		ClientSecret:  clientSecret,
		Audience:      audience,
		GrantType:     grantType,
		Logger:        oauthClient.logger,
	})
	s.httpClient.UseOAuth(true)
}

// -----------------------------
// 辅助方法
// -----------------------------

// InitializeWithToken 使用令牌初始化服务
func (s *SedoDomainService) InitializeWithToken(token string) {
	s.SetBearerToken(token)
}

// InitializeWithOAuth 使用OAuth凭据初始化服务
func (s *SedoDomainService) InitializeWithOAuth(clientID, clientSecret string) {
	s.SetOAuthCredentials(clientID, clientSecret)
}

// InitializeWithFullOAuth 使用完整OAuth配置初始化服务
func (s *SedoDomainService) InitializeWithFullOAuth(tokenEndpoint, clientID, clientSecret, audience, grantType string) {
	s.SetOAuthConfig(tokenEndpoint, clientID, clientSecret, audience, grantType)
}
