package model

import (
	"time"
)

// GoogleArticle Google文章模型
type GoogleArticle struct {
	ID               uint       `json:"id" gorm:"primaryKey"`
	RsocId           int        `json:"rsoc_id" gorm:"size:20;"`
	TwoDirectoryId   int        `json:"two_directory_id" gorm:"size:20;"`
	TwoDirectoryName string     `json:"two_directory_name" gorm:"size:200;"`
	Key              string     `json:"key" gorm:"size:200;"`
	CampaignName     string     `json:"campaign_name" gorm:"size:200;not null"`
	OfferCategory    string     `json:"offer_category" gorm:"size:200;not null"`
	Language         string     `json:"language" gorm:"size:50;not null"`
	TrafficSource    string     `json:"traffic_source" gorm:"size:50;not null"`
	TrackingMethod   string     `json:"tracking_method" gorm:"size:50;not null"`
	PixelID          string     `json:"pixel_id" gorm:"size:100;not null"`
	Token            string     `json:"token" gorm:"size:500"`
	EventName        string     `json:"event_name" gorm:"size:100;not null"`
	Keywords         string     `json:"keywords" gorm:"type:text"`
	ArticleTitle     string     `json:"article_title" gorm:"size:500"`
	ArticleIntro     string     `json:"article_intro" gorm:"type:text"`
	URLWPPost        string     `json:"url_wp_post" gorm:"size:500"`
	FinalURL         string     `json:"final_url" gorm:"size:500"`
	Channel          string     `json:"available_channel" gorm:"size:100"`
	Status           int        `json:"status" gorm:"default:0"` // 0:待审核 1:已通过 2:已拒绝
	ReviewResult     string     `json:"review_result" gorm:"type:text"`
	ResultReason     string     `json:"result_reason" gorm:"type:text"`
	Rate             int        `json:"rate" gorm:"size:11;"`
	CreatedAt        *time.Time `json:"created_at"`
	UpdatedAt        *time.Time `json:"updated_at"`
	Note             string     `json:"note" gorm:"type:text"`
	CampaignId       string     `json:"campaign_id"`
	Campaign         int        `json:"campaign"`
	CreatedBy        int        `json:"created_by"`
	Type             int        `json:"type"` // 1国内 2海外
}

// GoogleArticleListRequest Google文章列表请求参数
type GoogleArticleListRequest struct {
	Page     int `json:"page" form:"page"`
	PageSize int `json:"limit" form:"limit"`
	Status   int `json:"status" form:"status"`
	//Keyword        string `json:"keyword" form:"keyword"`
	Key            string `json:"key" form:"key"`
	RsocId         int    `json:"rsoc_id" form:"rsoc_id"`
	Type           int    `json:"type"` // 1国内 2海外
	TwoDirectoryId int    `json:"two_directory_id" form:"two_directory_id"`
	CreatedBy      int    `json:"created_by" form:"created_by"`
}
