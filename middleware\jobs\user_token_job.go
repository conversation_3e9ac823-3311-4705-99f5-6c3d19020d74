package jobs

import (
	"context"
	"log"
	"os"
	"rsoc-system-go/dao"
	"rsoc-system-go/middleware/pkg/redis"
	"rsoc-system-go/store"
	"strconv"
)

const UserTokenJobClass = "user_token_job"

func UserTokenFunc(ctx context.Context) error {
	userDao := dao.NewUserDao(store.DB)
	list, err := userDao.List()
	if err != nil {
		return err
	}
	/*redisManager := store.GetRedisManager()
	err = redisManager.ConnectRedis("task")
	if err != nil {
		return err
	}
	client := redisManager.GetRedisClient("task")*/
	indexdbStr := os.Getenv("redisdb")
	indexdb, _ := strconv.Atoi(indexdbStr)
	client := redis.SelectDB(indexdb)
	//client := redis.SelectDB(1)
	_, err = client.Del(ctx, "Token").Result()
	if err != nil {
		log.Println("token删除失败", err)
		return err
	}

	for _, user := range list {
		_, err = client.HSet(ctx, "Token", user.Token, user.Token).Result()
		if err != nil {
			log.Println("token设置失败", err)
			return err
		}
	}
	//log.Println("token同步成功")
	return nil
}
