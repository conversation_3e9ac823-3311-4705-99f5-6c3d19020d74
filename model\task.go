package model

import (
	"database/sql"
)

// Task 任务表
type Task struct {
	ID              int            `json:"id" gorm:"column:id;primaryKey;autoIncrement;type:int(11)"`
	FbID            sql.NullInt64  `json:"fbId" gorm:"column:fbId;type:int(11);default:null;comment:fb账户的主id;index"`
	TaskID          sql.NullString `json:"task_id" gorm:"column:task_id;type:varchar(255);not null;comment:任务ID;uniqueIndex:idx_task_id"`
	Country         sql.NullString `json:"country" gorm:"column:country;type:varchar(255);default:null;comment:国家"`
	TwoDirectory    sql.NullString `json:"two_directory" gorm:"column:two_directory;type:varchar(255);default:null;comment:二级目录"`
	Traffic         sql.NullString `json:"traffic" gorm:"column:traffic;type:varchar(255);default:null;comment:流量源"`
	TrafficURL      sql.NullString `json:"traffic_url" gorm:"column:traffic_url;type:text;default:null;comment:投放广告URL"`
	Platform        sql.NullString `json:"platform" gorm:"column:platform;type:varchar(255);default:null;comment:平台"`
	PlatformID      sql.NullString `json:"platform_id" gorm:"column:platform_id;type:varchar(255);default:null;comment:平台ID"`
	PlatformURL     sql.NullString `json:"platform_url" gorm:"column:platform_url;type:varchar(255);default:null;comment:平台URL（随机）-不用"`
	HoldRate        sql.NullString `json:"hold_rate" gorm:"column:hold_rate;type:varchar(255);default:null;comment:拦截比例"`
	CampaginID      sql.NullString `json:"campagin_id" gorm:"column:campagin_id;type:varchar(255);default:null;comment:fb campagin id;index"`
	FbAdsAccount    sql.NullString `json:"fb_ads_account" gorm:"column:fb_ads_account;type:varchar(255);default:null;comment:fb广告账户ID"`
	SeachRate       sql.NullString `json:"seach_rate" gorm:"column:seach_rate;type:varchar(255);default:null;comment:搜索比例"`
	Click1Rate      sql.NullString `json:"click1_rate" gorm:"column:click1_rate;type:varchar(255);default:null;comment:点击概率"`
	Click2Rate      sql.NullString `json:"click2_rate" gorm:"column:click2_rate;type:varchar(255);default:null;comment:二次点击"`
	Click3Rate      sql.NullString `json:"click3_rate" gorm:"column:click3_rate;type:varchar(255);default:null;comment:三次点击"`
	Note            sql.NullString `json:"note" gorm:"column:note;type:varchar(255);default:null;comment:备注"`
	UserID          sql.NullInt64  `json:"userId" gorm:"column:userId;type:int(11);default:null;comment:用户ID;index"`
	Status          sql.NullInt64  `json:"status" gorm:"column:status;type:int(11);default:1;comment:任务状态;index"`
	FbPixelID       sql.NullString `json:"fb_pixel_id" gorm:"column:fb_pixel_id;type:varchar(255);default:null;comment:FB像素ID"`
	FbPixelAPIToken sql.NullString `json:"fb_pixel_api_token" gorm:"column:fb_pixel_api_token;type:varchar(255);default:null;comment:FB像素token"`
	Ref             sql.NullString `json:"ref" gorm:"column:ref;type:varchar(255);default:null;comment:来源"`
	BlackURL        sql.NullString `json:"black_url" gorm:"column:black_url;type:varchar(255);default:null;comment:跳转URL"`
	AllNum          sql.NullInt64  `json:"all_num" gorm:"column:all_num;type:int(11);default:0;comment:总数量"`
	AllAutoNum      sql.NullInt64  `json:"all_auto_num" gorm:"column:all_auto_num;type:int(11);default:0;comment:到自动化任务的数量"`
	AllView         sql.NullInt64  `json:"all_view" gorm:"column:all_view;type:int(11);default:0;comment:总浏览"`
	AllClick1       sql.NullInt64  `json:"all_click1" gorm:"column:all_click1;type:int(11);default:0;comment:总点击1"`
	AllClick2       sql.NullInt64  `json:"all_click2" gorm:"column:all_click2;type:int(11);default:0;comment:总点击2"`
	AllClick3       sql.NullInt64  `json:"all_click3" gorm:"column:all_click3;type:int(11);default:0;comment:总点击3"`
	AllConv         sql.NullInt64  `json:"all_conv" gorm:"column:all_conv;type:int(11);default:0;comment:总转化数量"`
	AllAppConv      sql.NullInt64  `json:"all_app_conv" gorm:"column:all_app_conv;type:int(11);default:0;comment:程序转化数量"`
	ProxyID         sql.NullInt64  `json:"proxy_id" gorm:"column:proxy_id;type:int(11);default:null;comment:代理ID;index"`
	ProxyName       sql.NullString `json:"proxy_name" gorm:"column:proxy_name;type:varchar(255);default:null;comment:代理名称"`
	PutURL          sql.NullString `json:"put_url" gorm:"column:put_url;type:text;default:null"`
	IsGlobal        sql.NullInt64  `json:"is_global" gorm:"column:is_global;type:int(11);default:0"`
	CreatedAt       sql.NullTime   `json:"created_at" gorm:"column:created_at;type:datetime;autoCreateTime;index"`
	UpdatedAt       sql.NullTime   `json:"updated_at" gorm:"column:updated_at;type:datetime;autoUpdateTime"`
}

// TableName 指定表名
func (Task) TableName() string {
	return "task"
}
