package dao

import (
	"rsoc-system-go/model"

	"gorm.io/gorm"
)

// UserDao 用户数据访问对象
type UserDao struct {
	db *gorm.DB
}

// NewUserDao 创建新的UserDao实例
func NewUserDao(db *gorm.DB) *UserDao {
	return &UserDao{db: db}
}

// Create 创建新用户
func (d *UserDao) Create(user *model.User) error {
	return d.db.Create(user).Error
}

// Update 更新用户信息
func (d *UserDao) Update(user *model.User) error {
	return d.db.Save(user).Error
}

// Delete 删除用户
func (d *UserDao) Delete(id int) error {
	return d.db.Delete(&model.User{}, id).Error
}

// FindByID 根据ID查找用户
func (d *UserDao) FindByID(id int) (*model.User, error) {
	var user model.User
	err := d.db.First(&user, id).Error
	if err != nil {
		return nil, err
	}
	return &user, nil
}

// FindByUsername 根据用户名查找用户
func (d *UserDao) FindByUsername(username string) (*model.User, error) {
	var user model.User
	err := d.db.Where("user = ?", username).First(&user).Error
	if err != nil {
		return nil, err
	}
	return &user, nil
}

// FindByToken 根据token查找用户
func (d *UserDao) FindByToken(token string) (*model.User, error) {
	var user model.User
	err := d.db.Where("token = ?", token).First(&user).Error
	if err != nil {
		return nil, err
	}
	return &user, nil
}

// List 获取所有用户
func (d *UserDao) List() ([]*model.User, error) {
	var users []*model.User
	err := d.db.Find(&users).Error
	if err != nil {
		return nil, err
	}
	return users, nil
}
