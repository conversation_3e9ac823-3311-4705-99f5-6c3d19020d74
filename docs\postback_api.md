# Postback API 使用文档

## 概述

Postback API 用于接收第三方平台回传的广告点击转化数据。`/rsoc/postback` 是专门的回调接口，用于接收第三方平台（如 AdTech、SedoTMP 等）的转化数据回传。接收到的转化数据将自动更新到 `facebook_insights` 表中的真实收益字段。

## API 端点

### 第三方回调数据接收（主要功能）

**请求方式**: `GET` 或 `POST`
**请求路径**:
- `/rsoc/postback` - 通用第三方回调接口
- `/rsoc/sedotmp/callback` - SedoTMP 专用回调接口

**用途**: 专门用于接收第三方平台的转化数据回传

这些是第三方平台调用的回调接口，用于回传转化数据。

### 设置 Postback 配置（管理功能）

**请求方式**: `POST`
**请求路径**:
- `/rsoc/postback/setup` (推荐)
- `/rsoc/postback/config`

**Content-Type**: `application/x-www-form-urlencoded` 或 `application/json`

#### 请求参数

| 参数名 | 类型 | 必填 | 说明 |
|--------|------|------|------|
| security_key | string | 是 | 安全密钥 |
| domain_name | string | 是 | 回传域名URL |
| campaign | string | 否 | 广告系列字段映射 |
| click_id | string | 否 | 点击ID字段映射 |
| payout | string | 否 | 收益字段映射 |
| country | string | 否 | 国家字段映射 |
| zip | string | 否 | 邮编字段映射 |
| os_type | string | 否 | 操作系统类型字段映射 |
| browser | string | 否 | 浏览器字段映射 |
| device_type | string | 否 | 设备类型字段映射 |
| device_brand | string | 否 | 设备品牌字段映射 |
| s1 | string | 否 | 自定义参数1字段映射 |
| s2 | string | 否 | 自定义参数2字段映射 |
| s3 | string | 否 | 自定义参数3字段映射 |

#### 示例请求

```bash
# 使用专用配置路径（推荐）
curl --location --request POST 'http://localhost:8080/rsoc/postback/setup' \
--header 'Content-Type: application/x-www-form-urlencoded' \
--data-urlencode 'security_key=xxxxxxxxx' \
--data-urlencode 'domain_name=http://www.example.com' \
--data-urlencode 'campaign=campaign' \
--data-urlencode 'click_id=ad_click_cpc' \
--data-urlencode 'payout=EPC' \
--data-urlencode 'country=country' \
--data-urlencode 'zip=zip' \
--data-urlencode 'os_type=os_type' \
--data-urlencode 'browser=browser' \
--data-urlencode 'device_type=device_type' \
--data-urlencode 'device_brand=device_brand' \
--data-urlencode 's1=subid1' \
--data-urlencode 's2=subid2' \
--data-urlencode 's3=subid3'

# 或使用通用路径（自动判断）
curl --location --request POST 'http://localhost:8080/rsoc/postback' \
--header 'Content-Type: application/x-www-form-urlencoded' \
--data-urlencode 'security_key=xxxxxxxxx' \
--data-urlencode 'domain_name=http://www.example.com' \
--data-urlencode 'campaign=campaign'
```

#### 响应格式

```json
{
    "code": 1,
    "msg": "设置成功",
    "time": 1640995200,
    "data": null
}
```

### 第三方平台回调示例

**请求方式**: `GET` 或 `POST`
**请求路径**: `/rsoc/postback`

这是第三方平台回传数据时调用的接口。

#### 通用回调接口 `/rsoc/postback`
根据文档提供的 Postback URL 格式：
```
http://www.xxxxxxxxx.com/rsoc/postback?campaign=campaign&click_id=ad_click_cpc&payout=EPC&country=country&zip=zip&os_type=os_type&browser=browser&device_type=device_type&device_brand=device_brand&s1=subid1&s2=subid2&s3=subid3
```

#### SedoTMP 专用回调接口 `/rsoc/sedotmp/callback`
专门用于 SedoTMP 平台的回调，支持完整的 SedoTMP 宏参数：
```
http://www.xxxxxxxxx.com/rsoc/sedotmp/callback?campaign={campaign}&click_id={click_id}&epayout={epayout}&country={country}&country_name={country_name}&state={state}&city={city}&zip={zip}&os_type={os_type}&browser={browser}&device_type={device_type}&device_brand={device_brand}&subid1={subid1}&subid2={subid2}&subid3={subid3}&subid4={subid4}&subid5={subid5}
```

#### 支持的宏参数

系统兼容 AdTech 和 SedoTMP 两种格式的 postback 数据：

| 宏参数 | 说明 | 示例 | 支持格式 |
|--------|------|------|----------|
| {campaign} | 广告系列ID | 12345 | AdTech, SedoTMP |
| {click_id} | 点击ID | abc123xyz | AdTech, SedoTMP |
| {payout} | 收益金额 | 1.50 | AdTech |
| {epayout} | 估算收益金额 | 0.123 | SedoTMP |
| {country} | 2位国家代码 | US | AdTech, SedoTMP |
| {country_name} | 完整国家名称 | United States | SedoTMP |
| {state} | 州/省代码或名称 | CA / California | SedoTMP |
| {city} | 城市名称 | Los Angeles | SedoTMP |
| {zip} | 邮编 | 90001 | AdTech, SedoTMP |
| {os_type} | 操作系统 | WINDOWS | AdTech, SedoTMP |
| {browser} | 浏览器类型 | CHROME | AdTech, SedoTMP |
| {device_type} | 设备类型 | MOBILE/DESKTOP | AdTech, SedoTMP |
| {device_brand} | 设备品牌 | APPLE | AdTech, SedoTMP |
| {s1},{s2},{s3} | 自定义子ID | test1, test2, test3 | AdTech |
| {subid1}-{subid5} | 自定义子ID | AdSetID, PictureID | SedoTMP |

#### 示例请求

**AdTech 格式示例：**
```bash
# 第三方平台回调
curl 'http://your-domain.com/rsoc/postback?campaign=123&ad_click_cpc=abc123&EPC=1.50&country=US&s1=test1'
```

**SedoTMP 格式示例：**
```bash
# 第三方平台回调
curl 'http://your-domain.com/rsoc/postback?campaign=12345&click_id=xyz789&epayout=0.123&country=US&country_name=United%20States&state=CA&city=Los%20Angeles&zip=90001&os_type=WINDOWS&browser=CHROME&device_type=DESKTOP&device_brand=DELL&subid1=AdSetID123&subid2=PictureID456'
```

**标准格式（按文档）：**
```bash
# 按照文档提供的 Postback URL 格式
curl 'http://your-domain.com/rsoc/postback?campaign=campaign&click_id=ad_click_cpc&payout=EPC&country=country&zip=zip&os_type=os_type&browser=browser&device_type=device_type&device_brand=device_brand&s1=subid1&s2=subid2&s3=subid3'
```

**SedoTMP 专用格式：**
```bash
# SedoTMP 专用回调接口（基础参数）
curl 'http://your-domain.com/rsoc/sedotmp/callback?campaign=12345&click_id=abc123xyz&epayout=0.123&country=US'

# SedoTMP 专用回调接口（完整参数）
curl 'http://your-domain.com/rsoc/sedotmp/callback?campaign=67890&click_id=xyz789&epayout=0.085&country=GB&country_name=United%20Kingdom&state=London&city=London&zip=SW1A&os_type=ANDROID&browser=CHROME&device_type=MOBILE&device_brand=SAMSUNG&subid1=AdSetID123&subid2=PictureID456&subid3=CreativeID789&subid4=AudienceID101&subid5=CampaignTypeA'
```

#### 响应格式

```json
{
    "code": 1,
    "msg": "ok"
}
```

#### 数据处理说明

接收到的 postback 数据会自动处理并存储到 `facebook_insights` 表中：

- `campaign` 参数用作 `campaign_id`、`account_id`、`ad_id`、`adset_id`
- `payout` 或 `epayout` 参数存储为 `real_price`（真实收益）
- 优先使用 `payout`，如果为空则使用 `epayout`
- 如果当天已有相同 `campaign_id` 的记录，则更新 `real_price`
- 如果没有记录，则创建新记录，`clicks` 设为 1
- 支持所有 SedoTMP 宏参数，提供更详细的转化跟踪信息

## 字段映射说明

系统支持以下字段的映射：

- `campaign`: 广告系列
- `click_id`: 点击ID  
- `payout`: 收益
- `country`: 国家
- `zip`: 邮编
- `os_type`: 操作系统类型
- `browser`: 浏览器
- `device_type`: 设备类型
- `device_brand`: 设备品牌
- `s1`: 自定义参数1
- `s2`: 自定义参数2
- `s3`: 自定义参数3

如果某个字段留空，则不会回传该字段的信息。

## 投放链接格式

设置完成后，您的投放链接格式为：

```
https://投放域名/article-title/?campaign=123&click_id={click_id}&subid1={subid1}&subid2={subid2}&subid3={subid3}
```

## 错误码说明

| 错误码 | 说明 |
|--------|------|
| 400 | 请求参数错误 |
| 500 | 服务器内部错误 |
| 1 | 成功 |
