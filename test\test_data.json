{"test_data": {"general_postback": {"description": "通用回调接口测试数据", "endpoint": "/rsoc/postback", "test_cases": [{"name": "AdTech格式 - 美国桌面端用户", "method": "GET", "params": {"campaign": "ADT001", "click_id": "us_desktop_001", "payout": "2.50", "country": "US", "zip": "10001", "os_type": "WINDOWS", "browser": "CHROME", "device_type": "DESKTOP", "device_brand": "DELL", "s1": "AdSetUS001", "s2": "CreativeDesktop", "s3": "AudienceRetargeting"}, "expected_result": {"campaign_id": "ADT001", "real_price": 2.5, "clicks": 1}}, {"name": "AdTech格式 - 英国移动端用户", "method": "GET", "params": {"campaign": "ADT002", "click_id": "uk_mobile_001", "payout": "1.75", "country": "GB", "zip": "SW1A", "os_type": "ANDROID", "browser": "CHROME", "device_type": "MOBILE", "device_brand": "SAMSUNG", "s1": "AdSetUK001", "s2": "CreativeMobile", "s3": "AudienceLookalike"}, "expected_result": {"campaign_id": "ADT002", "real_price": 1.75, "clicks": 1}}, {"name": "混合格式 - 加拿大用户", "method": "GET", "params": {"campaign": "MIX001", "click_id": "ca_user_001", "payout": "3.20", "country": "CA", "zip": "M5V", "os_type": "MACOS", "browser": "SAFARI", "device_type": "DESKTOP", "device_brand": "APPLE", "s1": "AdSetCA001", "subid1": "TestSubID1", "subid2": "TestSubID2"}, "expected_result": {"campaign_id": "MIX001", "real_price": 3.2, "clicks": 1}}, {"name": "POST方式 - 澳大利亚用户", "method": "POST", "params": {"campaign": "POST001", "click_id": "post_test_001", "payout": "1.95", "country": "AU", "zip": "2000", "os_type": "WINDOWS", "browser": "EDGE", "device_type": "DESKTOP", "device_brand": "HP", "s1": "AdSetAU001", "s2": "CreativeVideo", "s3": "AudienceInterest"}, "expected_result": {"campaign_id": "POST001", "real_price": 1.95, "clicks": 1}}, {"name": "最小参数集", "method": "GET", "params": {"campaign": "MIN001", "payout": "0.85"}, "expected_result": {"campaign_id": "MIN001", "real_price": 0.85, "clicks": 1}}]}, "sedotmp_callback": {"description": "SedoTMP专用回调接口测试数据", "endpoint": "/rsoc/sedotmp/callback", "test_cases": [{"name": "美国用户 - 完整参数", "method": "GET", "params": {"campaign": "SEDO001", "click_id": "us_full_001", "epayout": "1.25", "country": "US", "country_name": "United States", "state": "CA", "city": "Los Angeles", "zip": "90001", "os_type": "WINDOWS", "browser": "CHROME", "device_type": "DESKTOP", "device_brand": "DELL", "subid1": "AdSetUS001", "subid2": "CreativeDesktop", "subid3": "AudienceRetargeting", "subid4": "CampaignTypeConversion", "subid5": "SeasonSummer"}, "expected_result": {"campaign_id": "SEDO001", "real_price": 1.25, "clicks": 1}}, {"name": "英国移动端用户", "method": "GET", "params": {"campaign": "SEDO002", "click_id": "uk_mobile_002", "epayout": "0.85", "country": "GB", "country_name": "United Kingdom", "state": "London", "city": "London", "zip": "SW1A", "os_type": "ANDROID", "browser": "CHROME", "device_type": "MOBILE", "device_brand": "SAMSUNG", "subid1": "AdSetUK001", "subid2": "CreativeMobile", "subid3": "AudienceLookalike"}, "expected_result": {"campaign_id": "SEDO002", "real_price": 0.85, "clicks": 1}}, {"name": "德国用户 - iOS设备", "method": "GET", "params": {"campaign": "SEDO003", "click_id": "de_ios_001", "epayout": "2.10", "country": "DE", "country_name": "Germany", "state": "Bavaria", "city": "Munich", "zip": "80331", "os_type": "IOS", "browser": "SAFARI", "device_type": "MOBILE", "device_brand": "APPLE", "subid1": "AdSetDE001", "subid2": "CreativeVideo", "subid3": "AudienceInterest", "subid4": "CampaignTypeBranding", "subid5": "SeasonWinter"}, "expected_result": {"campaign_id": "SEDO003", "real_price": 2.1, "clicks": 1}}, {"name": "加拿大用户 - POST方式", "method": "POST", "params": {"campaign": "SEDO004", "click_id": "ca_post_001", "epayout": "1.60", "country": "CA", "country_name": "Canada", "state": "Ontario", "city": "Toronto", "zip": "M5V", "os_type": "MACOS", "browser": "SAFARI", "device_type": "DESKTOP", "device_brand": "APPLE", "subid1": "AdSetCA001", "subid2": "CreativeImage", "subid3": "AudienceCustom"}, "expected_result": {"campaign_id": "SEDO004", "real_price": 1.6, "clicks": 1}}, {"name": "澳大利亚用户 - 基础参数", "method": "GET", "params": {"campaign": "SEDO005", "click_id": "au_basic_001", "epayout": "0.95", "country": "AU", "subid1": "AdSetAU001", "subid2": "CreativeBasic"}, "expected_result": {"campaign_id": "SEDO005", "real_price": 0.95, "clicks": 1}}, {"name": "法国用户 - 平板设备", "method": "GET", "params": {"campaign": "SEDO006", "click_id": "fr_tablet_001", "epayout": "1.40", "country": "FR", "country_name": "France", "state": "Ile-de-France", "city": "Paris", "zip": "75001", "os_type": "ANDROID", "browser": "CHROME", "device_type": "TABLET", "device_brand": "SAMSUNG", "subid1": "AdSetFR001", "subid2": "CreativeTablet", "subid3": "AudienceRetargeting", "subid4": "CampaignTypeApp", "subid5": "SeasonSpring"}, "expected_result": {"campaign_id": "SEDO006", "real_price": 1.4, "clicks": 1}}]}, "setup_config": {"description": "配置设置接口测试数据", "endpoint": "/rsoc/postback/setup", "test_cases": [{"name": "AdTech平台配置", "method": "POST", "params": {"security_key": "adtech_test_key_123", "domain_name": "http://callback.adtech-platform.com/postback", "campaign": "campaign_id", "click_id": "click_tracking_id", "payout": "revenue_amount", "country": "geo_country", "zip": "geo_zip", "os_type": "user_os", "browser": "user_browser", "device_type": "user_device", "device_brand": "device_manufacturer", "s1": "custom_param_1", "s2": "custom_param_2", "s3": "custom_param_3"}, "expected_result": {"code": 200, "msg": "设置成功"}}, {"name": "SedoTMP平台配置", "method": "POST", "content_type": "application/json", "params": {"security_key": "sedotmp_test_key_456", "domain_name": "http://callback.sedotmp-platform.com/callback", "campaign": "campaign_identifier", "click_id": "click_uuid", "payout": "estimated_payout", "country": "user_country", "zip": "user_postal_code", "os_type": "operating_system", "browser": "browser_type", "device_type": "device_category", "device_brand": "device_manufacturer", "s1": "subid_1", "s2": "subid_2", "s3": "subid_3"}, "expected_result": {"code": 200, "msg": "设置成功"}}]}, "error_test_cases": {"description": "错误测试用例", "test_cases": [{"name": "缺少参数测试", "endpoint": "/rsoc/postback", "method": "GET", "params": {}, "expected_result": {"code": 1, "msg": "ok"}}, {"name": "无效收益格式", "endpoint": "/rsoc/postback", "method": "GET", "params": {"campaign": "ERROR001", "payout": "invalid_number"}, "expected_result": {"code": 1, "msg": "ok"}}, {"name": "特殊字符测试", "endpoint": "/rsoc/postback", "method": "GET", "params": {"campaign": "ERROR003", "click_id": "test with spaces", "payout": "1.00", "country": "US"}, "expected_result": {"code": 1, "msg": "ok"}}]}, "batch_test_data": {"description": "批量测试数据", "general_postback_batch": [{"campaign": "BATCH001", "payout": "1.25", "country": "US"}, {"campaign": "BATCH002", "payout": "2.50", "country": "GB"}, {"campaign": "BATCH003", "payout": "0.85", "country": "DE"}, {"campaign": "BATCH004", "payout": "3.75", "country": "CA"}, {"campaign": "BATCH005", "payout": "1.95", "country": "AU"}], "sedotmp_callback_batch": [{"campaign": "SBATCH001", "epayout": "0.123", "country": "US"}, {"campaign": "SBATCH002", "epayout": "0.456", "country": "GB"}, {"campaign": "SBATCH003", "epayout": "0.789", "country": "DE"}, {"campaign": "SBATCH004", "epayout": "1.234", "country": "CA"}, {"campaign": "SBATCH005", "epayout": "0.567", "country": "AU"}]}}}