package api

import (
	"os"
	"rsoc-system-go/config"
	"rsoc-system-go/dao"
	docs "rsoc-system-go/docs"
	"rsoc-system-go/handler"
	"rsoc-system-go/handler/system"
	"rsoc-system-go/middleware"
	"rsoc-system-go/middleware/pkg/logger"
	"rsoc-system-go/server"
	"rsoc-system-go/service"
	"rsoc-system-go/store"

	"github.com/gin-contrib/cors"
	"github.com/gin-gonic/gin"
	swaggerfiles "github.com/swaggo/files"
	ginSwagger "github.com/swaggo/gin-swagger"
)

var Router *gin.Engine

func init() {
	Router = gin.Default()
	config := cors.DefaultConfig()
	config.AllowAllOrigins = true
	config.AllowHeaders = []string{"Origin", "Content-Length", "Content-Type", "Token"}
	//config.AllowMethods = []string{"GET", "POST", "PUT", "PATCH", "DELETE", "HEAD", "OPTIONS"}
	Router.Use(cors.New(config))
	Router.Use(logger.GetGinLogger())

	// 全局中间件
	//Router.Use(middleware.MiddleWare())

	// API文档
	docs.SwaggerInfo.BasePath = "/"
	Router.GET("/swagger/*any", ginSwagger.WrapHandler(swaggerfiles.Handler))

	// 后台管理路由组
	//initAdminRoutes()

	// 业务API路由组
	initAPIRoutes()

	// 加载系统模板
	if systemFlag := os.Getenv("system"); systemFlag == "true" {
		server.LoadTmpl(Router)
	}
}

// initAdminRoutes 初始化后台管理路由
func initAdminRoutes() {
	adminPath := Router.Group("/admin")
	authorized := adminPath.Group("")
	authorized.Use(middleware.AuthMiddleware())
	{

	}

}

// initAPIRoutes 初始化业务API路由
func initAPIRoutes() {
	apis := Router.Group("")
	systemH := system.SystemH{}

	backHandler := handler.NewPostBackHandler()
	apis.POST("/rsoc/reload", systemH.Reload)

	// Postback 路由
	// /rsoc/postback - 专门用于第三方回调数据接收
	apis.Any("/rsoc/postback", backHandler.RsocHandler)

	// SedoTMP 专用回调路由
	// /rsoc/sedotmp/callback - 专门用于SedoTMP平台回调数据接收
	apis.Any("/rsoc/sedotmp/callback", backHandler.SedoTMPCallbackHandler)

	// Postback 配置设置路由
	apis.POST("/rsoc/postback/setup", backHandler.SetupHandler)
	apis.POST("/rsoc/postback/config", backHandler.SetupHandler)

	// 添加RSOCStart路由
	rsocStart := handler.NewRSOCStart(config.NewConfig())
	start := apis.Group("/startRsoc")
	{
		start.GET("", rsocStart.Index)
		start.POST("/viewport", rsocStart.Viewport)
	}

	apis.Use(middleware.ReplayAuthMiddleware())
	rosc := apis.Group("/rsoc")
	{
		rsocKey := handler.RsocKey{}
		rosc.POST("/list", rsocKey.List)
		rosc.POST("/add", rsocKey.Create)
		rosc.POST("/update", rsocKey.Update)
	}
	replay := handler.RePlay{}
	replayH := apis.Group("")
	{
		replayTask := handler.ReplayTaskHandler{}
		replayH.POST("/replay/replay", replayTask.Replay)
		replayH.POST("/replay/inittaskline", replayTask.IntTaskLine)
		replayH.POST("/replay/add", replayTask.Create)
		replayH.POST("/replay/updateStatus", replayTask.UpdateStatus)
		replayH.POST("/replay/list", replayTask.List)
		replayH.POST("/replay/del", replayTask.Delete)
		replayH.POST("/replay/updateNumber", replayTask.UpdateNumber)
		replayH.POST("/replay/updateProxy", replayTask.UpdateProxy)

		replayH.GET("/replay/line", replay.RePlayHandler)
		replayH.GET("/replay/task-line", replay.RePlayTaskLineHandler)

		//apiAuthorized.GET("/replay/task", replayTask.List)
	}

	// 文章相关路由
	articleDAO := dao.NewArticleDAO(store.DB)
	articleHandler := handler.NewArticleHandler(articleDAO)

	articles := apis.Group("/articles")
	{
		articles.GET("", articleHandler.List)
		articles.POST("", middleware.Auth(), articleHandler.Create)
		articles.PUT("/:id/status", middleware.Auth(), articleHandler.UpdateStatus)
	}

	// Sedo文章相关路由
	sedoArticleHandler := handler.NewSedoArticleHandler(service.GetSedoCampaignService())

	// 普通文章相关路由
	sedoArticles := apis.Group("/sedo/articles")
	{
		sedoArticles.GET("", sedoArticleHandler.GetArticles)
		sedoArticles.GET("/:id", sedoArticleHandler.GetArticleByID)
		sedoArticles.POST("", sedoArticleHandler.CreateArticle)
		sedoArticles.PUT("/:id", sedoArticleHandler.UpdateArticle)
		sedoArticles.PATCH("/:id", sedoArticleHandler.PatchArticle)
		sedoArticles.DELETE("/:id", sedoArticleHandler.DeleteArticle)
	}

	// 已发布文章相关路由
	sedoPublishedArticles := apis.Group("/sedo/published-articles")
	{
		sedoPublishedArticles.GET("", sedoArticleHandler.GetPublishedArticles)
		sedoPublishedArticles.GET("/:id", sedoArticleHandler.GetPublishedArticleByID)
		sedoPublishedArticles.POST("", sedoArticleHandler.PublishArticle)
		sedoPublishedArticles.DELETE("/:id", sedoArticleHandler.UnpublishArticle)
	}

	// 生成文章相关路由
	apis.POST("/sedo/generated-articles", sedoArticleHandler.GenerateArticle)

	// 添加广告系列同步路由
	apis.GET("/sedo/campaigns/sync", sedoArticleHandler.SyncCampaignData)

	apis.GET("/sedo/campaigns/report", sedoArticleHandler.CampaignReport)

	// 详细文章相关路由
	sedoDetailedArticles := apis.Group("/sedo/detailed-articles")
	{
		sedoDetailedArticles.GET("", sedoArticleHandler.GetDetailedArticles)
		sedoDetailedArticles.GET("/:id", sedoArticleHandler.GetDetailedArticleByID)
	}

	// 域名相关路由
	apis.GET("/sedo/domains", sedoArticleHandler.GetDomains)

	// 分类相关路由
	categoryHandler := handler.NewCategoryHandler(service.GetSedoCategoryService())
	categories := apis.Group("/categories")
	{
		categories.GET("", categoryHandler.GetCategories)
		categories.POST("create", categoryHandler.CreateCategory)

	}

	// Google相关路由
	googleHandler := handler.NewGoogleHandler()
	google := apis.Group("/google")
	{
		google.POST("/addLaunch", googleHandler.AddLaunch)
		google.POST("/updateLaunch", googleHandler.UpdateLaunch)
		google.POST("/batchLaunch", googleHandler.BatchLaunch)
		google.POST("/resultList", googleHandler.ResultList)
		google.POST("/requestLog", googleHandler.RequestLog)
		google.GET("/reportDataSource", googleHandler.ReportDataSource)
		google.GET("/cateList", googleHandler.CateList)
		google.GET("/syncData", googleHandler.SyncData)
		google.POST("/getCampaign", googleHandler.GetCampaign)

		// 添加GoogleArticle同步路由
		googleArticleSyncHandler := handler.NewGoogleArticleSyncHandler()
		google.POST("/syncArticles", googleArticleSyncHandler.SyncAll)
		google.POST("/syncArticle/:id", googleArticleSyncHandler.SyncById)

		// 添加Google报告同步路由
		googleReportSyncHandler := handler.NewGoogleReportSyncHandler()
		google.GET("/syncReportData", googleReportSyncHandler.SyncReportData)
		google.GET("/syncReportDataRange", googleReportSyncHandler.SyncReportDataRange)
		google.GET("/autoSyncReportData", googleReportSyncHandler.AutoSyncReportData)
	}

	// AdTech相关路由
	adTechHandler := handler.NewAdTechHandler(service.GetAdTechCampaignService())
	adTech := apis.Group("/adtech")
	{
		adTech.GET("/domains", adTechHandler.GetDomains)
		adTech.GET("/campaign", adTechHandler.GetCampaign)

	}
}

/*func Listen(w http.ResponseWriter, r *http.Request) {
	Router.ServeHTTP(w, r)
}
*/
