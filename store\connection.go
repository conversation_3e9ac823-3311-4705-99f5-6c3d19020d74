package store

import (
	"os"
	"sync"
)

var (
	// 连接就绪状态
	ready     bool
	readyOnce sync.Once
	// 连接就绪通知通道
	readyChan = make(chan struct{})
)

// MarkReady 标记所有必需的数据库连接已就绪
func MarkReady() {
	readyOnce.Do(func() {
		ready = true
		close(readyChan)
	})
}

// WaitForConnections 等待所有数据库连接就绪
func WaitForConnections() {
	if !ready {
		<-readyChan
	}
}

// IsReady 检查数据库连接是否就绪
func IsReady() bool {
	return ready
}

// CheckDatabaseConnections 检查所有必需的数据库连接
func CheckDatabaseConnections() {
	// 检查MySQL/PostgreSQL连接
	needDB := os.Getenv("MYSQL_URL") != "" || os.Getenv("POSTGRES_URL") != ""
	if needDB && DB == nil {
		return
	}

	// 检查Redis连接
	needRedis := os.Getenv("REDIS_URL") != ""
	if needRedis && RedisClient == nil {
		return
	}

	// 检查TDEngine连接
	needTDEngine := os.Getenv("TDENGINE_URL") != ""
	if needTDEngine && TDB == nil {
		return
	}

	// 如果所有需要的连接都已就绪，标记为ready
	if (!needDB || DB != nil) && (!needRedis || RedisClient != nil) && (!needTDEngine || TDB != nil) {
		MarkReady()
	}
}
