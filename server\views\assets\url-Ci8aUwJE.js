import{_ as Q,u as W,o as X,r as k,a as Z,M as r,j as ee,c as te,b as m,d as o,w as l,e as n,B as oe,k as ne,f as N,h as _,m as j,n as A,F as ae,g as le,p as se,I as de,s as U}from"./index-DlVegDiC.js";import{S as re,a as ie}from"./index-CSU5nP3m.js";import{P as ue}from"./PlusOutlined-Cg2o2XQN.js";import{_ as pe}from"./index-1uCBjWky.js";import{_ as fe}from"./index-DXcpAzs8.js";import{_ as me}from"./index-B8PO_1fg.js";const ce={class:"main"},ge={class:"filter"},_e={class:"filter_box"},he={class:"filter_item"},ye={__name:"url",setup(ke){const h="",c=W(),q=Z();X(()=>{y()});const x=k({id:void 0,two:void 0,status:"1"}),b=k({data:[],loading:!1,sedo_list:[],two_list:[],main_list:[]}),y=()=>{b.loading=!0,fetch(`${h}/Yahoo1/urlList`,{method:"POST",body:JSON.stringify({sedoId:x.id,twoDirectoryId:x.two,status:x.status}),headers:{token:c.token}}).then(t=>t.json()).then(t=>{console.log(t),t.code==1?b.data=t.data.map(e=>(e.checked=e.status==1,e.checked_loading=!1,e)):t.code==3e3?(c.$patch({token:!1}),q.push("/login"),r.error({title:t.msg})):(b.data=[],r.error({title:t.msg})),b.loading=!1}).catch(t=>{r.error({title:"服务器错误",content:`${t}`})})},O=k({selectedRowKeys:[],onChange:(t,e)=>{console.log(t),O.selectedRowKeys=t}});let v=0;const T=ee(),d=k({open:!1,main_directory:[],id:void 0,yahooId:void 0,url:"",rate:"",note:"",option:{},selectedOptions:{},ads_list:[],loading:!1}),B=()=>{v=0,d.open=!0,J()},J=()=>{fetch(`${h}/Yahoo1/list`,{method:"POST",headers:{token:c.token}}).then(t=>t.json()).then(t=>{console.log(t),d.ads_list=t.data}).catch(t=>{r.error({title:"服务器错误",content:`${t}`})})},Y=()=>{d.loading=!0;let t=d.url.split(`
`);fetch(`${h}/Yahoo1/addYahooUrl`,{method:"POST",body:JSON.stringify({yahooId:d.yahooId,url:t[v],rate:d.rate,note:d.note}),headers:{token:c.token}}).then(e=>e.json()).then(e=>{console.log(e),e.code==1?(U.success(`${t[v]}添加成功`),t.length==v+1?(U.success("全部添加成功"),y(),T.value.resetFields(),d.open=!1,d.loading=!1):(v=v+1,Y())):r.error({title:e.msg})}).catch(e=>{r.error({title:"服务器错误",content:`${e}`})})},s=k({open:!1,main_directory:[],edit_id:0,yahooId:"",url:"",rate:"",note:"",status:1,selectedOptions:{},loading:!1}),D=t=>{console.log(t),s.edit_id=t.id,s.yahooId=t.yahoo_id,s.url=t.url,s.rate=t.rate,s.status=t.checked,s.note=t.note,s.open=!0},K=()=>{s.loading=!0,fetch(`${h}/Yahoo1/updateYahoo1Url`,{method:"POST",body:JSON.stringify({id:s.edit_id,yahooId:s.yahooId,rate:s.rate,status:s.status?1:2,note:s.note}),headers:{token:c.token}}).then(t=>t.json()).then(t=>{console.log(t),t.code==1?(U.success("更新成功"),y()):r.error({title:t.msg}),s.open=!1,s.loading=!1}).catch(t=>{r.error({title:"服务器错误",content:`${t}`})})},u=k({open:!1,rate:"",loading:!1}),V=()=>{console.log(u.rate),u.loading=!0,fetch(`${h}/sedo/updateSeDoUrlAllRate`,{method:"POST",body:JSON.stringify({id:O.selectedRowKeys,rate:u.rate}),headers:{token:c.token}}).then(t=>t.json()).then(t=>{console.log(t),t.code==1?(U.success(t.msg),y()):r.error({title:t.msg}),u.open=!1,u.loading=!1}).catch(t=>{r.error({title:"服务器错误",content:`${t}`})})},p=k({open:!1,status:!0,loading:!1}),L=()=>{console.log(p.status),p.loading=!0,fetch(`${h}/sedo/updateSeDoUrlAllStatus`,{method:"POST",body:JSON.stringify({id:O.selectedRowKeys,status:p.status?1:2}),headers:{token:c.token}}).then(t=>t.json()).then(t=>{console.log(t),t.code==1?(U.success(t.msg),y()):r.error({title:t.msg}),p.open=!1,p.loading=!1}).catch(t=>{r.error({title:"服务器错误",content:`${t}`})})},E=(t,e)=>{let w;t=="status"?w={id:e.id,rate:e.rate,status:e.checked?1:2,note:e.note}:w={id:e.id,rate:e.rate,status:e.status,note:e.note},fetch(`${h}/Yahoo1/updateYahoo1Url`,{method:"POST",body:JSON.stringify(w),headers:{token:c.token}}).then(f=>f.json()).then(f=>{console.log(f),f.code==1?(U.success(f.msg),y()):r.error({title:f.msg})}).catch(f=>{r.error({title:"服务器错误",content:`${f}`})})},M=[{title:"ID",dataIndex:"id",key:"id",align:"center"},{title:"yahoo_id",dataIndex:"yahoo_id",key:"yahoo_id",align:"center"},{title:"域名",dataIndex:"url",key:"url",align:"center"},{title:"权重",dataIndex:"rate",key:"rate",align:"center"},{title:"状态",dataIndex:"status",key:"status",align:"center"},{title:"备注",dataIndex:"note",key:"note",align:"center"},{title:"添加日期",dataIndex:"add_time",key:"add_time",align:"center"},{title:"更新日期",dataIndex:"update_time",key:"update_time",align:"center"},{title:"操作",key:"action",align:"center"}];return(t,e)=>{const w=ie,f=re,G=ue,I=oe,F=fe,z=me,H=pe,i=le,R=se,$=de,C=ae,P=r;return N(),te(ne,null,[m("div",ce,[m("div",null,[m("div",ge,[m("div",_e,[m("div",he,[e[17]||(e[17]=m("p",null,"状态：",-1)),o(f,{ref:"select",value:n(x).status,"onUpdate:value":e[0]||(e[0]=a=>n(x).status=a),style:{width:"200px"},onChange:y},{default:l(()=>[o(w,{value:"1"},{default:l(()=>e[15]||(e[15]=[_("激活")])),_:1}),o(w,{value:"2"},{default:l(()=>e[16]||(e[16]=[_("关闭")])),_:1})]),_:1},8,["value"])])]),m("div",null,[o(I,{type:"primary",onClick:B},{icon:l(()=>[o(G)]),default:l(()=>[e[18]||(e[18]=_(" 添加雅虎域名 "))]),_:1})])])]),o(H,{columns:M,"data-source":n(b).data,rowKey:"id",pagination:!1,loading:n(b).loading,bordered:"","row-selection":n(O)},{bodyCell:l(({column:a,record:g})=>[a.key==="status"?(N(),j(F,{key:0,checked:g.checked,"onUpdate:checked":S=>g.checked=S,loading:g.checked_loading,onChange:S=>E("status",g)},null,8,["checked","onUpdate:checked","loading","onChange"])):A("",!0),a.key==="rate"?(N(),j(z,{key:1,value:g.rate,"onUpdate:value":S=>g.rate=S,min:0,onPressEnter:S=>E("rate",g)},null,8,["value","onUpdate:value","onPressEnter"])):A("",!0),a.key==="action"?(N(),j(I,{key:2,type:"link",onClick:S=>D(g)},{default:l(()=>e[19]||(e[19]=[_(" 更新域名 ")])),_:2},1032,["onClick"])):A("",!0)]),_:1},8,["data-source","loading","row-selection"])]),o(P,{open:n(d).open,"onUpdate:open":e[5]||(e[5]=a=>n(d).open=a),title:"添加雅虎域名",footer:null,maskClosable:!1},{default:l(()=>[e[21]||(e[21]=m("div",{style:{height:"20px"}},null,-1)),o(C,{ref_key:"add_form",ref:T,model:n(d),onFinish:Y,"label-col":{span:4},"wrapper-col":{span:18}},{default:l(()=>[o(i,{label:"雅虎账户",name:"yahooId",rules:[{required:!0,message:"请选择雅虎账户"}]},{default:l(()=>[o(f,{value:n(d).yahooId,"onUpdate:value":e[1]||(e[1]=a=>n(d).yahooId=a),placeholder:"请选择雅虎账户",options:n(d).ads_list,"field-names":{label:"name",value:"id"}},null,8,["value","options"])]),_:1}),o(i,{label:"域名",name:"url",rules:[{required:!0,message:"请输入域名"}]},{default:l(()=>[o(R,{value:n(d).url,"onUpdate:value":e[2]||(e[2]=a=>n(d).url=a),placeholder:"每行填写一个域名",rows:6},null,8,["value"])]),_:1}),o(i,{label:"权重",name:"rate",rules:[{required:!0,message:"请输入权重"}]},{default:l(()=>[o($,{value:n(d).rate,"onUpdate:value":e[3]||(e[3]=a=>n(d).rate=a),placeholder:"权重"},null,8,["value"])]),_:1}),o(i,{label:"备注",name:"note"},{default:l(()=>[o(R,{value:n(d).note,"onUpdate:value":e[4]||(e[4]=a=>n(d).note=a),placeholder:"备注",rows:2},null,8,["value"])]),_:1}),o(i,{"wrapper-col":{offset:4,span:18}},{default:l(()=>[o(I,{type:"primary","html-type":"submit",loading:n(d).loading},{default:l(()=>e[20]||(e[20]=[_(" 提交 ")])),_:1},8,["loading"])]),_:1})]),_:1},8,["model"])]),_:1},8,["open"]),o(P,{open:n(s).open,"onUpdate:open":e[10]||(e[10]=a=>n(s).open=a),title:"更新雅虎域名",footer:null,maskClosable:!1},{default:l(()=>[e[23]||(e[23]=m("div",{style:{height:"20px"}},null,-1)),o(C,{model:n(s),onFinish:K,"label-col":{span:4},"wrapper-col":{span:18}},{default:l(()=>[o(i,{label:"域名",name:"url",rules:[{required:!0,message:"请输入域名"}]},{default:l(()=>[o($,{value:n(s).url,"onUpdate:value":e[6]||(e[6]=a=>n(s).url=a),placeholder:"域名",disabled:""},null,8,["value"])]),_:1}),o(i,{label:"权重",name:"rate",rules:[{required:!0,message:"请输入权重"}]},{default:l(()=>[o($,{value:n(s).rate,"onUpdate:value":e[7]||(e[7]=a=>n(s).rate=a),placeholder:"权重"},null,8,["value"])]),_:1}),o(i,{label:"状态",name:"status"},{default:l(()=>[o(F,{checked:n(s).status,"onUpdate:checked":e[8]||(e[8]=a=>n(s).status=a)},null,8,["checked"])]),_:1}),o(i,{label:"备注",name:"note"},{default:l(()=>[o(R,{value:n(s).note,"onUpdate:value":e[9]||(e[9]=a=>n(s).note=a),placeholder:"备注",rows:2},null,8,["value"])]),_:1}),o(i,{"wrapper-col":{offset:4,span:18}},{default:l(()=>[o(I,{type:"primary","html-type":"submit",loading:n(s).loading},{default:l(()=>e[22]||(e[22]=[_(" 提交 ")])),_:1},8,["loading"])]),_:1})]),_:1},8,["model"])]),_:1},8,["open"]),o(P,{open:n(u).open,"onUpdate:open":e[12]||(e[12]=a=>n(u).open=a),title:"批量修改权重",footer:null,maskClosable:!1},{default:l(()=>[e[25]||(e[25]=m("div",{style:{height:"20px"}},null,-1)),o(C,{model:n(u),onFinish:V,"label-col":{span:4},"wrapper-col":{span:18}},{default:l(()=>[o(i,{label:"权重",name:"rate",rules:[{required:!0,message:"请输入权重"}]},{default:l(()=>[o($,{value:n(u).rate,"onUpdate:value":e[11]||(e[11]=a=>n(u).rate=a),placeholder:"权重"},null,8,["value"])]),_:1}),o(i,{"wrapper-col":{offset:4,span:18}},{default:l(()=>[o(I,{type:"primary","html-type":"submit",loading:n(u).loading},{default:l(()=>e[24]||(e[24]=[_(" 提交 ")])),_:1},8,["loading"])]),_:1})]),_:1},8,["model"])]),_:1},8,["open"]),o(P,{open:n(p).open,"onUpdate:open":e[14]||(e[14]=a=>n(p).open=a),title:"批量修改状态",footer:null,maskClosable:!1},{default:l(()=>[e[27]||(e[27]=m("div",{style:{height:"20px"}},null,-1)),o(C,{model:n(p),onFinish:L,"label-col":{span:4},"wrapper-col":{span:18}},{default:l(()=>[o(i,{label:"状态",name:"status"},{default:l(()=>[o(F,{checked:n(p).status,"onUpdate:checked":e[13]||(e[13]=a=>n(p).status=a)},null,8,["checked"])]),_:1}),o(i,{"wrapper-col":{offset:4,span:18}},{default:l(()=>[o(I,{type:"primary","html-type":"submit",loading:n(p).loading},{default:l(()=>e[26]||(e[26]=[_(" 提交 ")])),_:1},8,["loading"])]),_:1})]),_:1},8,["model"])]),_:1},8,["open"])],64)}}},xe=Q(ye,[["__scopeId","data-v-a51df9bd"]]);export{xe as default};
