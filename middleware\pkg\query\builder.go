package query

import "gorm.io/gorm"

// Operator 查询操作符
type Operator string

const (
	OpGt        Operator = "gt"
	OpGte       Operator = "gte"
	OpLt        Operator = "lt"
	OpLte       Operator = "lte"
	OpEq        Operator = "eq"
	OpNeq       Operator = "neq"
	OpLike      Operator = "like"
	OpLeftLike  Operator = "left_like"
	OpRightLike Operator = "right_like"
	OpIn        Operator = "in"
	OpBetween   Operator = "between"
)

// Condition 查询条件
type Condition struct {
	Field string
	Op    Operator
	Value interface{}
}

// Builder 查询构建器
type Builder struct {
	db         *gorm.DB
	conditions []Condition
}

// NewBuilder 创建查询构建器
func NewBuilder(db *gorm.DB) *Builder {
	return &Builder{
		db:         db,
		conditions: make([]Condition, 0),
	}
}

// Where 添加查询条件
func (b *Builder) Where(field string, op Operator, value interface{}) *Builder {
	b.conditions = append(b.conditions, Condition{
		Field: field,
		Op:    op,
		Value: value,
	})
	return b
}

// Build 构建查询
func (b *Builder) Build() *gorm.DB {
	query := b.db

	for _, condition := range b.conditions {
		switch condition.Op {
		case OpGt:
			query = query.Where(condition.Field+" > ?", condition.Value)
		case OpGte:
			query = query.Where(condition.Field+" >= ?", condition.Value)
		case OpLt:
			query = query.Where(condition.Field+" < ?", condition.Value)
		case OpLte:
			query = query.Where(condition.Field+" <= ?", condition.Value)
		case OpEq:
			query = query.Where(condition.Field+" = ?", condition.Value)
		case OpNeq:
			query = query.Where(condition.Field+" != ?", condition.Value)
		case OpLike:
			query = query.Where(condition.Field+" LIKE ?", "%"+condition.Value.(string)+"%")
		case OpLeftLike:
			query = query.Where(condition.Field+" LIKE ?", "%"+condition.Value.(string))
		case OpRightLike:
			query = query.Where(condition.Field+" LIKE ?", condition.Value.(string)+"%")
		case OpIn:
			query = query.Where(condition.Field+" IN ?", condition.Value)
		case OpBetween:
			if values, ok := condition.Value.([]interface{}); ok && len(values) == 2 {
				query = query.Where(condition.Field+" BETWEEN ? AND ?", values[0], values[1])
			}
		}
	}

	return query
}

// BuildFromMap 从map构建查询条件
func BuildFromMap(db *gorm.DB, conditions map[string]interface{}) *gorm.DB {
	builder := NewBuilder(db)

	for field, condition := range conditions {
		if cond, ok := condition.(map[string]interface{}); ok {
			for op, value := range cond {
				builder.Where(field, Operator(op), value)
			}
		}
	}

	return builder.Build()
}

// BuildQuery 构建查询（通用方法）
func BuildQuery[T any](db *gorm.DB, model T, conditions map[string]interface{}) *gorm.DB {
	query := db.Model(model)
	return BuildFromMap(query, conditions)
}

// Page 分页查询（通用方法）
func Page[T any](db *gorm.DB, model T, conditions map[string]interface{}, page, size int) ([]T, int64, error) {
	var items []T
	var total int64

	query := BuildQuery(db, model, conditions)

	err := query.Count(&total).Error
	if err != nil {
		return nil, 0, err
	}

	err = query.Offset((page - 1) * size).Limit(size).Find(&items).Error
	if err != nil {
		return nil, 0, err
	}

	return items, total, nil
}

// List 列表查询（通用方法）
func List[T any](db *gorm.DB, model T, conditions map[string]interface{}) ([]T, error) {
	var items []T

	query := BuildQuery(db, model, conditions)

	err := query.Find(&items).Error
	if err != nil {
		return nil, err
	}

	return items, nil
}
