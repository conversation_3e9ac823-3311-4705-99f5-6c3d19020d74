package handler

import (
	"crypto/sha1"
	"database/sql"
	"encoding/json"
	"fmt"
	"math/rand"
	"net/http"
	"rsoc-system-go/config"
	"rsoc-system-go/model"
	"rsoc-system-go/store"
	"rsoc-system-go/tools/facebook"
	"strings"
	"time"

	"github.com/gin-gonic/gin"
	"github.com/go-redis/redis/v8"
	"gorm.io/gorm"
)

// RSOCStart 处理开始相关的请求
type RSOCStart struct {
	DB     *gorm.DB
	Redis  *redis.Client
	Config *config.Config
}

// StartRequest 开始请求的参数结构
type StartRequest struct {
	TaskID     string `form:"subclick"`
	Traffic    string `form:"s"`
	CID        string `form:"fbclid"`
	CampaignID string `form:"campaignid"`
	AdsetID    string `form:"adsetid"`
	AdID       string `form:"adid"`
	CreativeID string `form:"creativeid"`
	Placement  string `form:"placement"`
}

// ViewportRequest 视口请求的参数结构
type ViewportRequest struct {
	Width        int    `json:"width"`
	Height       int    `json:"height"`
	ScreenWidth  int    `json:"screenwidth"`
	ScreenHeight int    `json:"screenheight"`
	ClickID      string `json:"clickId"`
	Type         string `json:"type"`
}

// TrafficData 流量数据结构
type TrafficData struct {
	CampaignID string `json:"campaignid"`
	AdsetID    string `json:"adsetid"`
	AdID       string `json:"adid"`
	CreativeID string `json:"creativeid"`
	CID        string `json:"cid"`
	Placement  string `json:"placement"`
}

// NewRSOCStart 创建新的RSOCStart实例
func NewRSOCStart(config *config.Config) *RSOCStart {
	return &RSOCStart{
		DB:     store.DB,
		Redis:  store.GetRedisManager().GetDefaultRedisClient(),
		Config: config,
	}
}

// Index 处理开始请求
func (h *RSOCStart) Index(c *gin.Context) {
	var req StartRequest
	if err := c.ShouldBindQuery(&req); err != nil {
		c.JSON(http.StatusBadRequest, gin.H{"error": err.Error()})
		return
	}

	// 获取用户IP和请求头信息
	userIP := c.ClientIP()
	userAgent := c.GetHeader("User-Agent")
	lang := c.GetHeader("Accept-Language")
	referer := c.GetHeader("Referer")

	// 生成点击ID
	clickID := h.generateClickID(req.CampaignID, userIP, userAgent, req.CID)

	// 查询任务
	var task model.Task
	result := h.DB.Where("task_id = ? AND status = ?", req.TaskID, 1).First(&task)
	if result.Error != nil {
		c.Redirect(http.StatusFound, "")
		return
	}

	// 检查是否需要自动化处理
	isAuto := h.shouldAutoProcess(req, userAgent, userIP)
	dbName := "auto_task"
	if !isAuto {
		dbName = "auto_task_not"
	}

	// 准备流量数据
	trafficData := TrafficData{
		CampaignID: req.CampaignID,
		AdsetID:    req.AdsetID,
		AdID:       req.AdID,
		CreativeID: req.CreativeID,
		CID:        req.CID,
		Placement:  req.Placement,
	}
	trafficDataJSON, _ := json.Marshal(trafficData)

	// 创建自动任务记录
	autoTask := model.AutoTask{
		TaskID:      sql.NullString{String: req.TaskID, Valid: true},
		ClickID:     sql.NullString{String: clickID, Valid: true},
		UserAgent:   sql.NullString{String: userAgent, Valid: true},
		Lang:        sql.NullString{String: lang, Valid: true},
		UserIP:      sql.NullString{String: userIP, Valid: true},
		Ref:         sql.NullString{String: referer, Valid: true},
		AddTime:     sql.NullString{String: time.Now().Format("2006-01-02 15:04:05"), Valid: true},
		Date:        sql.NullString{String: time.Now().Format("2006-01-02"), Valid: true},
		CampaignID:  sql.NullString{String: req.CampaignID, Valid: true},
		TrafficData: sql.NullString{String: string(trafficDataJSON), Valid: true},
		Traffic:     sql.NullString{String: req.Traffic, Valid: true},
		TrafficURL:  sql.NullString{String: c.Request.URL.String(), Valid: true},
		ClickURL:    sql.NullString{String: task.BlackURL.String, Valid: true},
	}

	if err := h.DB.Table(dbName).Create(&autoTask).Error; err != nil {
		c.JSON(http.StatusInternalServerError, gin.H{"error": err.Error()})
		return
	}

	// 发送Facebook像素事件
	err := h.sendFacebookPixel(&task, &req, userIP, userAgent)
	if err != nil {
		c.JSON(http.StatusInternalServerError, gin.H{"error": err.Error()})
		return
	}

	// 返回HTML响应
	c.Header("Content-Type", "text/html")
	html := h.ReturnHTML(isAuto, clickID, task.BlackURL.String, task.IsGlobal.Int64)
	c.String(http.StatusOK, html)
}

// Viewport 处理视口更新请求
func (h *RSOCStart) Viewport(c *gin.Context) {
	var req ViewportRequest
	if err := c.ShouldBindJSON(&req); err != nil {
		c.JSON(http.StatusBadRequest, gin.H{"error": err.Error()})
		return
	}

	viewport := map[string]interface{}{
		"width":        req.Width,
		"height":       req.Height,
		"screenwidth":  req.ScreenWidth,
		"screenheight": req.ScreenHeight,
	}
	viewportJSON, _ := json.Marshal(viewport)

	dbName := "auto_task"
	if req.Type != "run" {
		dbName = "auto_task_not"
	}

	if err := h.DB.Table(dbName).Where("click_id = ?", req.ClickID).
		Update("viewport", string(viewportJSON)).Error; err != nil {
		c.JSON(http.StatusInternalServerError, gin.H{"error": err.Error()})
		return
	}

	c.JSON(http.StatusOK, gin.H{"code": 1, "msg": "ok"})
}

// ReturnHTML 返回HTML响应
func (h *RSOCStart) ReturnHTML(isAuto bool, clickID, blankURL string, isGlobal int64) string {
	redirectJS := ""
	if blankURL != "" {
		redirectJS = fmt.Sprintf("window.location.href = '%s';", blankURL)
	}

	if isGlobal == 1 {
		return fmt.Sprintf("<script>%s</script>", redirectJS)
	}

	html := `<!DOCTYPE html>
<html xmlns="http://www.w3.org/1999/xhtml">
<head>
<meta http-equiv="Content-Type" content="text/html; charset=utf-8" />
<meta name="robots" content="noindex,nofollow">
<meta name="robots" content="nofollow">
</head>
<body>
<script>
const width = window.innerWidth;
const height = window.innerHeight;
const screenWidth = screen.width;
const screenHeight = screen.height;
const clickId = '%s';
const type = '%s';
const data = {
    width: width,
    height: height,
    screenwidth: screenWidth,
    screenheight: screenHeight,
    clickId: clickId,
    type: type
};
fetch('/start/viewport', {
    method: 'POST',
    headers: {
        'Content-Type': 'application/json'
    },
    body: JSON.stringify(data)
})
.then(response => response.json())
.then(result => {
    console.log('Result:', result);
    %s
})
.catch(error => {
    console.error('Error:', error);
    %s
});
</script>
</body>
</html>`

	taskType := "no"
	if isAuto {
		taskType = "run"
	}

	return fmt.Sprintf(html, clickID, taskType, redirectJS, redirectJS)
}

// generateClickID 生成点击ID
func (h *RSOCStart) generateClickID(campaignID, userIP, userAgent, cid string) string {
	// 生成随机字符串
	randStr := func(n int) string {
		const letters = "abcdefghijklmnopqrstuvwxyzABCDEFGHIJKLMNOPQRSTUVWXYZ"
		b := make([]byte, n)
		for i := range b {
			b[i] = letters[rand.Intn(len(letters))]
		}
		return string(b)
	}

	// 组合字符串并计算SHA1
	str := randStr(10) + time.Now().String() + campaignID + userIP + userAgent + cid
	hasher := sha1.New()
	hasher.Write([]byte(str))
	return fmt.Sprintf("%x", hasher.Sum(nil))
}

// shouldAutoProcess 判断是否需要自动处理
func (h *RSOCStart) shouldAutoProcess(req StartRequest, userAgent, userIP string) bool {
	// 检查基本条件
	if req.CID == "" || req.CampaignID == "0" || req.AdsetID == "0" || req.AdID == "0" {
		return false
	}

	// 检查User-Agent
	userAgentLower := strings.ToLower(userAgent)
	if strings.Contains(userAgentLower, "facebook") || !strings.Contains(userAgentLower, "fb") {
		return false
	}

	return true
}

// sendFacebookPixel 发送Facebook像素事件
func (h *RSOCStart) sendFacebookPixel(task *model.Task, req *StartRequest, userIP, userAgent string) error {
	if task.FbPixelID.String == "" || task.FbPixelAPIToken.String == "" {
		return nil
	}

	// 创建Facebook API客户端
	api := facebook.NewAPI(task.FbPixelAPIToken.String)

	// 创建事件请求
	eventReq := &facebook.EventRequest{
		PixelID: task.FbPixelID.String,
		Events: []*facebook.Event{
			{
				EventName:      "PageView",
				EventTime:      time.Now().Unix(),
				EventSourceURL: task.TrafficURL.String,
				UserData: &facebook.UserData{
					ClientIPAddress: userIP,
					ClientUserAgent: userAgent,
				},
				ActionSource: facebook.ActionSourceWebsite,
			},
		},
	}

	// 执行请求
	resp, err := eventReq.Execute(api)
	if err != nil {
		return err
	}
	defer resp.Body.Close()

	if resp.StatusCode != http.StatusOK {
		return fmt.Errorf("Facebook pixel request failed with status: %d", resp.StatusCode)
	}

	return nil
}
