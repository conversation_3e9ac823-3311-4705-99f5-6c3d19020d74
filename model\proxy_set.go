package model

// ProxySet 代理设置表
type ProxySet struct {
	ID        int    `json:"id" gorm:"column:id;primaryKey;autoIncrement"`
	Note      string `json:"note" gorm:"column:note;type:varchar(255)"`
	Label     string `json:"label" gorm:"column:label;type:varchar(255)"`
	ProxyType string `json:"proxy_type" gorm:"column:proxy_type;type:varchar(255);default:http"`
	ProxyURL  string `json:"proxy_url" gorm:"column:proxy_url;type:varchar(255)"`
	ProxyPort string `json:"proxy_port" gorm:"column:proxy_port;type:varchar(255)"`
	ProxyUser string `json:"proxy_user" gorm:"column:proxy_user;type:varchar(255)"`
	ProxyPass string `json:"proxy_pass" gorm:"column:proxy_pass;type:varchar(255)"`
	Status    int    `json:"status" gorm:"column:status"`
	UserID    int    `json:"userId" gorm:"column:userId"`
	AddTime   string `json:"add_time" gorm:"column:add_time;type:datetime"`
	Rate      int    `json:"rate" gorm:"column:rate"`
}

// TableName 指定表名
func (p *ProxySet) TableName() string {
	return "proxy_set"
}
