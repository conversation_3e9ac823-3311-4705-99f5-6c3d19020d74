#!/bin/bash

# Google 报告同步 API 测试脚本

BASE_URL="http://localhost:8080"
SECURITY_KEY="your_security_key_here"

echo "=== Google 报告同步 API 测试 ==="

# 测试1: 同步指定日期的报告数据
echo "1. 测试同步指定日期的报告数据..."
TODAY=$(date +%Y-%m-%d)
curl -X GET "$BASE_URL/google/syncReportData" \
  -G \
  -d "security_key=$SECURITY_KEY" \
  -d "data_date=$TODAY" \
  -d "limit=10" \
  -d "page=1" \
  -w "\n状态码: %{http_code}\n\n"

# 测试2: 同步昨天的数据
echo "2. 测试同步昨天的数据..."
YESTERDAY=$(date -d "yesterday" +%Y-%m-%d)
curl -X GET "$BASE_URL/google/syncReportData" \
  -G \
  -d "security_key=$SECURITY_KEY" \
  -d "data_date=$YESTERDAY" \
  -w "\n状态码: %{http_code}\n\n"

# 测试3: 按日期范围同步（最近3天）
echo "3. 测试按日期范围同步（最近3天）..."
START_DATE=$(date -d "3 days ago" +%Y-%m-%d)
END_DATE=$(date +%Y-%m-%d)
curl -X GET "$BASE_URL/google/syncReportDataRange" \
  -G \
  -d "security_key=$SECURITY_KEY" \
  -d "start_date=$START_DATE" \
  -d "end_date=$END_DATE" \
  -w "\n状态码: %{http_code}\n\n"

# 测试4: 自动同步最近7天的数据
echo "4. 测试自动同步最近7天的数据..."
curl -X GET "$BASE_URL/google/autoSyncReportData" \
  -G \
  -d "security_key=$SECURITY_KEY" \
  -w "\n状态码: %{http_code}\n\n"

# 测试5: 参数验证测试（缺少security_key）
echo "5. 测试参数验证（缺少security_key）..."
curl -X GET "$BASE_URL/google/syncReportData" \
  -G \
  -d "data_date=$TODAY" \
  -w "\n状态码: %{http_code}\n\n"

# 测试6: 参数验证测试（错误的日期格式）
echo "6. 测试参数验证（错误的日期格式）..."
curl -X GET "$BASE_URL/google/syncReportData" \
  -G \
  -d "security_key=$SECURITY_KEY" \
  -d "data_date=2025/01/01" \
  -w "\n状态码: %{http_code}\n\n"

# 测试7: 测试大批量同步（最近30天）
echo "7. 测试大批量同步（最近30天）..."
START_DATE_30=$(date -d "30 days ago" +%Y-%m-%d)
curl -X GET "$BASE_URL/google/syncReportDataRange" \
  -G \
  -d "security_key=$SECURITY_KEY" \
  -d "start_date=$START_DATE_30" \
  -d "end_date=$END_DATE" \
  -w "\n状态码: %{http_code}\n\n"

echo "=== 测试完成 ==="

echo ""
echo "=== 数据库验证查询 ==="
echo "可以使用以下 SQL 查询验证同步结果："
echo ""
echo "-- 查看今天同步的数据"
echo "SELECT campaign_id, date, impressions, clicks, spend, oracle_price, update_time"
echo "FROM facebook_insights"
echo "WHERE date = '$TODAY'"
echo "ORDER BY update_time DESC;"
echo ""
echo "-- 查看最近7天的同步统计"
echo "SELECT date, COUNT(*) as record_count, SUM(spend) as total_revenue"
echo "FROM facebook_insights"
echo "WHERE date >= DATE_SUB(CURDATE(), INTERVAL 7 DAY)"
echo "GROUP BY date"
echo "ORDER BY date DESC;"
echo ""
echo "-- 查看特定广告系列的数据"
echo "SELECT * FROM facebook_insights"
echo "WHERE campaign_id = 'Apply for credit cards'"
echo "ORDER BY date DESC;"
