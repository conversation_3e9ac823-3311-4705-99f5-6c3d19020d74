import{_ as T,u as V,o as A,r as h,a as E,M as r,j as I,c as M,b as u,d as t,w as l,B as Y,e as a,k as J,f as U,h as _,m as L,n as R,F as D,g as K,I as z,p as G,s as C}from"./index-DlVegDiC.js";import{P as H}from"./PlusOutlined-Cg2o2XQN.js";import{_ as Q}from"./index-1uCBjWky.js";import"./index-CSU5nP3m.js";const W={class:"main"},X={class:"filter"},Z={__name:"account",setup(ee){const g="",c=V(),S=E();A(()=>{b()});const p=h({data:[],loading:!1}),b=()=>{p.loading=!0,fetch(`${g}/Yahoo1/list`,{method:"POST",headers:{token:c.token}}).then(e=>e.json()).then(e=>{console.log(e),e.code==1?p.data=e.data:e.code==3e3?(c.$patch({token:!1}),S.push("/login"),r.error({title:e.msg})):(p.data=[],r.error({title:e.msg})),p.loading=!1}).catch(e=>{r.error({title:"服务器错误",content:`${e}`})})},k=I(),d=h({open:!1,pubid:"",channel:"",note:"",loading:!1}),$=()=>{d.open=!0},F=()=>{d.loading=!0,fetch(`${g}/Yahoo1/add`,{method:"POST",body:JSON.stringify({pubid:d.pubid,channel:d.channel,name:d.note}),headers:{token:c.token}}).then(e=>e.json()).then(e=>{console.log(e),e.code==1?(C.success("添加成功"),b(),k.value.resetFields()):r.error({title:e.msg}),d.open=!1,d.loading=!1}).catch(e=>{r.error({title:"服务器错误",content:`${e}`})})},y=I(),o=h({open:!1,id:0,pubid:"",channel:"",note:"",loading:!1}),N=e=>{console.log(e),o.id=e.id,o.pubid=e.pubid,o.channel=e.channel,o.note=e.name,o.open=!0},O=()=>{o.loading=!0,fetch(`${g}/Yahoo1/updateNote`,{method:"POST",body:JSON.stringify({id:o.id,pubid:o.pubid,channel:o.channel,name:o.note}),headers:{token:c.token}}).then(e=>e.json()).then(e=>{console.log(e),e.code==1?(C.success("修改成功"),b(),y.value.resetFields()):r.error({title:e.msg}),o.open=!1,o.loading=!1}).catch(e=>{r.error({title:"服务器错误",content:`${e}`})})},P=[{title:"ID",dataIndex:"id",key:"id",align:"center"},{title:"pubid",dataIndex:"pubid",key:"pubid",align:"center"},{title:"channel",dataIndex:"channel",key:"channel",align:"center"},{title:"备注",dataIndex:"name",key:"name",align:"center"},{title:"添加日期",dataIndex:"add_time",key:"add_time",align:"center"},{title:"操作",key:"action",align:"center"}];return(e,n)=>{const q=H,m=Y,B=Q,f=z,i=K,v=G,x=D,w=r;return U(),M(J,null,[u("div",W,[u("div",null,[u("div",X,[n[9]||(n[9]=u("div",{class:"filter_item"},null,-1)),u("div",null,[t(m,{type:"primary",onClick:$},{icon:l(()=>[t(q)]),default:l(()=>[n[8]||(n[8]=_(" 添加雅虎账户 "))]),_:1})])])]),t(B,{columns:P,"data-source":a(p).data,rowKey:"id",pagination:!1,loading:a(p).loading,bordered:""},{bodyCell:l(({column:s,record:j})=>[s.key==="action"?(U(),L(m,{key:0,type:"link",onClick:ne=>N(j)},{default:l(()=>n[10]||(n[10]=[_(" 更新账户 ")])),_:2},1032,["onClick"])):R("",!0)]),_:1},8,["data-source","loading"])]),t(w,{open:a(d).open,"onUpdate:open":n[3]||(n[3]=s=>a(d).open=s),title:"添加雅虎账户",footer:null,maskClosable:!1},{default:l(()=>[n[12]||(n[12]=u("div",{style:{height:"20px"}},null,-1)),t(x,{ref_key:"add_form",ref:k,model:a(d),onFinish:F,"label-col":{span:4},"wrapper-col":{span:18}},{default:l(()=>[t(i,{label:"pubid",name:"pubid",rules:[{required:!0,message:"请输入pubid"}]},{default:l(()=>[t(f,{value:a(d).pubid,"onUpdate:value":n[0]||(n[0]=s=>a(d).pubid=s),placeholder:"pubid"},null,8,["value"])]),_:1}),t(i,{label:"channel",name:"channel",rules:[{required:!0,message:"请输入channel"}]},{default:l(()=>[t(f,{value:a(d).channel,"onUpdate:value":n[1]||(n[1]=s=>a(d).channel=s),placeholder:"channel"},null,8,["value"])]),_:1}),t(i,{label:"备注",name:"note",rules:[{required:!0,message:"请输入备注"}]},{default:l(()=>[t(v,{value:a(d).note,"onUpdate:value":n[2]||(n[2]=s=>a(d).note=s),placeholder:"备注",rows:2},null,8,["value"])]),_:1}),t(i,{"wrapper-col":{offset:4,span:18}},{default:l(()=>[t(m,{type:"primary","html-type":"submit",loading:a(d).loading},{default:l(()=>n[11]||(n[11]=[_(" 提交 ")])),_:1},8,["loading"])]),_:1})]),_:1},8,["model"])]),_:1},8,["open"]),t(w,{open:a(o).open,"onUpdate:open":n[7]||(n[7]=s=>a(o).open=s),title:"更新雅虎账户",footer:null,maskClosable:!1},{default:l(()=>[n[14]||(n[14]=u("div",{style:{height:"20px"}},null,-1)),t(x,{ref_key:"edit_form",ref:y,model:a(o),onFinish:O,"label-col":{span:4},"wrapper-col":{span:18}},{default:l(()=>[t(i,{label:"pubid",name:"pubid",rules:[{required:!0,message:"请输入pubid"}]},{default:l(()=>[t(f,{value:a(o).pubid,"onUpdate:value":n[4]||(n[4]=s=>a(o).pubid=s),placeholder:"pubid"},null,8,["value"])]),_:1}),t(i,{label:"channel",name:"channel",rules:[{required:!0,message:"请输入channel"}]},{default:l(()=>[t(f,{value:a(o).channel,"onUpdate:value":n[5]||(n[5]=s=>a(o).channel=s),placeholder:"channel"},null,8,["value"])]),_:1}),t(i,{label:"备注",name:"note",rules:[{required:!0,message:"请输入备注"}]},{default:l(()=>[t(v,{value:a(o).note,"onUpdate:value":n[6]||(n[6]=s=>a(o).note=s),placeholder:"备注",rows:2},null,8,["value"])]),_:1}),t(i,{"wrapper-col":{offset:4,span:18}},{default:l(()=>[t(m,{type:"primary","html-type":"submit",loading:a(o).loading},{default:l(()=>n[13]||(n[13]=[_(" 提交 ")])),_:1},8,["loading"])]),_:1})]),_:1},8,["model"])]),_:1},8,["open"])],64)}}},de=T(Z,[["__scopeId","data-v-7e2f090c"]]);export{de as default};
