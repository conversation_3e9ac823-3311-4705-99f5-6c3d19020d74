package model

import (
	"time"
)

// GoogleReport Google收益报告模型
type GoogleReport struct {
	ID                      uint      `json:"id" gorm:"primaryKey"`
	CampaignName            string    `json:"campaign_name" gorm:"size:200;not null"`
	Platform                string    `json:"platform" gorm:"size:50;not null"`
	Country                 string    `json:"country" gorm:"size:50;not null"`
	Hour                    int       `json:"hour" gorm:"not null"`
	RelatedLinksRequests    int64     `json:"related_links_requests"`
	RelatedLinksImpressions int64     `json:"related_links_impressions"`
	RelatedLinksClicks      int64     `json:"related_links_clicks"`
	RelatedLinksRpm         float64   `json:"related_links_rpm"`
	AdRequests              int64     `json:"ad_requests"`
	MatchedAdRequests       int64     `json:"matched_ad_requests"`
	AdImpressions           int64     `json:"ad_impressions"`
	Impressions             int64     `json:"impressions"`
	Clicks                  int64     `json:"clicks"`
	Ctr                     float64   `json:"ctr"`
	AdCtr                   float64   `json:"ad_ctr"`
	AdRpm                   float64   `json:"ad_rpm"`
	Cr                      float64   `json:"cr"`
	Revenue                 float64   `json:"revenue"`
	DataDate                string    `json:"data_date" gorm:"size:10;not null"`
	CreatedAt               time.Time `json:"created_at"`
	UpdatedAt               time.Time `json:"updated_at"`
}

// GoogleReportRequest 收益报告请求参数
type GoogleReportRequest struct {
	Page     int    `json:"page" form:"page"`
	PageSize int    `json:"page_size" form:"page_size"`
	DataDate string `json:"data_date" form:"data_date" binding:"required"`
}
