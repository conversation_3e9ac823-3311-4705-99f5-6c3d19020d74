package service

import "fmt"

// SedoReqV3GuideText 返回使用req/v3库的Sedo内容服务使用指南
func SedoReqV3GuideText() string {
	return `
# Sedo内容服务API使用指南 (req/v3实现)

## 概述

这是基于github.com/imroc/req/v3库实现的Sedo内容服务API客户端。相比标准库实现，req/v3库提供了以下优势：

- **链式API设计**：简洁、流畅的API调用方式
- **自动重试**：内置智能重试机制处理临时网络错误
- **超时控制**：精细的请求超时管理
- **JSON处理**：内置的JSON编解码支持
- **中间件支持**：可扩展的请求和响应处理中间件
- **文件上传简化**：简化的文件上传API
- **调试支持**：内置请求/响应日志和调试功能

## 安装

确保你的项目中已添加以下依赖：

- github.com/imroc/req/v3
- github.com/sirupsen/logrus (用于日志记录)

## 配置

### 环境变量

服务可通过以下环境变量进行配置：

| 环境变量 | 描述 | 默认值 |
|---------|------|--------|
| SEDO_API_BASE_URL | API基础URL | https://api.sedotmp.com |
| SEDO_API_TOKEN | Bearer令牌 | 无 |
| SEDO_TOKEN_ENDPOINT | OAuth令牌端点 | https://auth.sedotmp.com/oauth/token |
| SEDO_CLIENT_ID | OAuth客户端ID | 无 |
| SEDO_CLIENT_SECRET | OAuth客户端密钥 | 无 |
| SEDO_AUDIENCE | OAuth目标受众 | https://api.sedotmp.com/ |
| SEDO_GRANT_TYPE | OAuth授权类型 | client_credentials |
| SEDO_USE_OAUTH | 是否使用OAuth | false |
| LOG_LEVEL | 日志级别 | info |

## 初始化服务

### 方法1: 使用单例模式获取服务实例

最简单的方式是使用单例模式获取服务实例：

` + "```go" + `
// 获取服务实例
contentService := GetSedoContentService()
` + "```" + `

### 方法2: 手动创建服务实例

如果你需要更多控制，可以手动创建所有服务组件：

` + "```go" + `
// 创建HTTP客户端
httpClient := GetSedoHTTPClient()
// 也可以使用 NewSedoHTTPClient() 创建新实例

// 创建所有服务
articleService := NewSedoArticleService(httpClient)
categoryService := NewSedoCategoryService(httpClient)
domainService := NewSedoDomainService(httpClient)
mediaService := NewSedoMediaService(httpClient)

// 创建内容服务
contentService := NewSedoContentService(
    articleService,
    categoryService,
    domainService,
    mediaService,
    httpClient,
)
` + "```" + `

## 认证方式

### 方法1: 使用Bearer令牌认证

` + "```go" + `
// 设置Bearer令牌
contentService.SetBearerToken("your-api-token")
` + "```" + `

### 方法2: 使用OAuth认证（简化版）

` + "```go" + `
// 启用OAuth并设置凭据
contentService.UseOAuth(true)
contentService.SetOAuthCredentials("your-client-id", "your-client-secret")

// 或使用便捷方法
contentService.InitializeWithOAuth("your-client-id", "your-client-secret")
` + "```" + `

### 方法3: 使用OAuth认证（完整配置）

` + "```go" + `
// 设置完整OAuth配置
contentService.SetOAuthConfig(
    "https://auth.sedotmp.com/oauth/token",
    "your-client-id",
    "your-client-secret",
    "https://api.sedotmp.com/",
    "client_credentials",
)

// 或使用便捷方法
contentService.InitializeWithFullOAuth(
    "https://auth.sedotmp.com/oauth/token",
    "your-client-id",
    "your-client-secret",
    "https://api.sedotmp.com/",
    "client_credentials",
)
` + "```" + `

## 请求示例

### 检查API连接

` + "```go" + `
// 创建上下文
ctx, cancel := context.WithTimeout(context.Background(), 30*time.Second)
defer cancel()

// 检查连接
if err := contentService.CheckConnection(ctx); err != nil {
    // 处理错误
    log.Fatalf("API连接失败: %v", err)
} else {
    log.Println("API连接成功!")
}
` + "```" + `

### 获取分类列表

` + "```go" + `
// 获取第一页，每页10条
categories, page, err := contentService.GetCategories(ctx, 0, 10, "")
if err != nil {
    // 处理错误
    log.Fatalf("获取分类失败: %v", err)
} else {
    log.Printf("获取到%d个分类, 总计%d个", len(categories), page.TotalCount)
    
    // 处理分类数据
    for _, category := range categories {
        log.Printf("分类: ID=%s, 标题=%s", category.ID, category.Title)
    }
}
` + "```" + `

### 创建文章

` + "```go" + `
// 创建文章请求
articleRequest := &model.SedoCreateArticleRequest{
    Title:      "测试文章标题",
    Excerpt:    "这是一篇测试文章的摘要。",
    Text:       "这是一篇测试文章的内容。这里是正文部分。",
    CategoryID: "category-id", // 有效的分类ID
    Tags:       []string{"测试", "示例", "API"},
    Country:    "CN",
    Locale:     "zh-CN",
}

// 创建文章
article, err := contentService.CreateArticle(ctx, articleRequest)
if err != nil {
    // 处理错误
    log.Fatalf("创建文章失败: %v", err)
} else {
    log.Printf("成功创建文章: ID=%s, 标题=%s", article.ID, article.Title)
}
` + "```" + `

### 更新文章

` + "```go" + `
// 更新文章请求
updateRequest := &model.SedoUpdateArticleRequest{
    Title:   "更新后的测试文章标题",
    Excerpt: "这是更新后的文章摘要。",
    Text:    "这是更新后的文章内容。添加了一些新内容。",
    Tags:    []string{"更新", "测试", "API"},
    Country: "CN",
    Locale:  "zh-CN",
}

// 更新文章
updatedArticle, err := contentService.UpdateArticle(ctx, "article-id", updateRequest)
if err != nil {
    // 处理错误
    log.Fatalf("更新文章失败: %v", err)
} else {
    log.Printf("成功更新文章: ID=%s, 标题=%s", updatedArticle.ID, updatedArticle.Title)
}
` + "```" + `

### 生成文章

` + "```go" + `
// 生成文章请求
generateRequest := &model.SedoGenerateArticleRequest{
    Topics:     []string{"Go语言编程", "API开发"},
    CategoryID: "category-id", // 有效的分类ID
    Country:    "CN",
    Locale:     "zh-CN",
    AutoPublish: &model.SedoAutoPublish{
        DomainName:  "example.com", // 有效的域名
        PublishDate: time.Now().Add(24 * time.Hour),
    },
    GenerateImage: &struct {
        Enabled     bool   ` + "`json:\"enabled\"`" + `
        Description string ` + "`json:\"description\"`" + `
    }{
        Enabled:     true,
        Description: "Go编程语言图片",
    },
}

// 生成文章（同步模式）
article, err := contentService.GenerateArticle(ctx, generateRequest, false, "")
if err != nil {
    // 处理错误
    log.Fatalf("生成文章失败: %v", err)
} else {
    log.Printf("成功生成文章: ID=%s, 标题=%s", article.ID, article.Title)
}
` + "```" + `

### 上传媒体文件

` + "```go" + `
// 上传媒体文件
media, err := contentService.UploadMediaFile(ctx, "path/to/image.jpg")
if err != nil {
    // 处理错误
    log.Fatalf("上传媒体文件失败: %v", err)
} else {
    log.Printf("成功上传媒体文件: ID=%s, URL=%s", media.ID, media.URL)
}
` + "```" + `

### 下载媒体文件

` + "```go" + `
// 下载媒体文件
err := contentService.DownloadMediaFile(ctx, "media-id", "path/to/save/image.jpg")
if err != nil {
    // 处理错误
    log.Fatalf("下载媒体文件失败: %v", err)
} else {
    log.Printf("成功下载媒体文件")
}
` + "```" + `

## 完整示例

请参考以下文件查看完整的使用示例：

- service/sedo_content_service_example.go: 内容服务API使用示例
- service/sedo_oauth_example.go: OAuth认证使用示例

## 数据模型

请参考 model/sedo_content.go 文件查看所有数据模型定义。

## req/v3特有功能

### 自定义请求超时

` + "```go" + `
// 创建带有自定义超时的上下文
ctx, cancel := context.WithTimeout(context.Background(), 10*time.Second)
defer cancel()

// 使用自定义超时的上下文发送请求
categories, _, err := contentService.GetCategories(ctx, 0, 10, "")
` + "```" + `

### 启用请求调试

如果你使用的是自定义HTTP客户端，你可以启用调试功能：

` + "```go" + `
// 获取HTTP客户端
httpClient := GetSedoHTTPClient()

// 启用调试
httpClient.EnableDebug()
httpClient.EnableDumpAll()

// 使用此客户端创建内容服务
// ...
` + "```" + `

## 错误处理

所有API方法都返回具体的错误信息，建议根据错误类型进行处理：

` + "```go" + `
categories, _, err := contentService.GetCategories(ctx, 0, 10, "")
if err != nil {
    switch {
    case strings.Contains(err.Error(), "401"):
        // 处理认证错误
        log.Printf("认证失败，请检查凭据: %v", err)
    case strings.Contains(err.Error(), "超时"):
        // 处理超时错误
        log.Printf("请求超时，请稍后重试: %v", err)
    default:
        // 处理其他错误
        log.Printf("发生错误: %v", err)
    }
}
` + "```" + `

## 并发安全性

所有服务实例都是并发安全的，可以在多个goroutine中共享同一个服务实例。

## req/v3库相关资源

- GitHub仓库: https://github.com/imroc/req
- 官方文档: https://req.cool/zh/
`
}

// PrintSedoReqV3Guide 打印req/v3库Sedo内容服务使用指南
func PrintSedoReqV3Guide() {
	fmt.Println(SedoReqV3GuideText())
}
