package model

import (
	"time"
)

// Article 文章模型
type Article struct {
	ID        uint      `json:"id" gorm:"primaryKey"`
	Title     string    `json:"title" gorm:"size:200;not null"`
	Content   string    `json:"content" gorm:"type:text;not null"`
	AuthorID  uint      `json:"author_id" gorm:"not null"`
	Status    int       `json:"status" gorm:"default:0"` // 0:待审核 1:已通过 2:已拒绝
	CreatedAt time.Time `json:"created_at"`
	UpdatedAt time.Time `json:"updated_at"`
}

// ArticleListRequest 文章列表请求参数
type ArticleListRequest struct {
	Page           int    `json:"page" form:"page"`
	PageSize       int    `json:"page_size" form:"page_size"`
	Status         int    `json:"status" form:"status"`
	AuthorID       uint   `json:"author_id" form:"author_id"`
	Key            string `json:"key"`
	TwoDirectoryId int    `json:"two_directory_id"`
}

// ArticleCreateRequest 创建文章请求参数
type ArticleCreateRequest struct {
	Title   string `json:"title" binding:"required"`
	Content string `json:"content" binding:"required"`
}

// ArticleUpdateStatusRequest 更新文章状态请求参数
type ArticleUpdateStatusRequest struct {
	Status int    `json:"status" binding:"required"`
	Reason string `json:"reason"`
}
