package dao

import (
	"rsoc-system-go/model"

	"gorm.io/gorm"
)

type GoogleReportDAO struct {
	db *gorm.DB
}

func NewGoogleReportDAO(db *gorm.DB) *GoogleReportDAO {
	return &GoogleReportDAO{db: db}
}

// Create 创建收益报告
func (d *GoogleReportDAO) Create(report *model.GoogleReport) error {
	return d.db.Create(report).Error
}

// BatchCreate 批量创建收益报告
func (d *GoogleReportDAO) BatchCreate(reports []*model.GoogleReport) error {
	return d.db.Create(reports).Error
}

// List 获取收益报告列表
func (d *GoogleReportDAO) List(req *model.GoogleReportRequest) ([]model.GoogleReport, int64, error) {
	var reports []model.GoogleReport
	var total int64

	query := d.db.Model(&model.GoogleReport{}).Where("data_date = ?", req.DataDate)

	err := query.Count(&total).Error
	if err != nil {
		return nil, 0, err
	}

	if req.Page > 0 && req.PageSize > 0 {
		offset := (req.Page - 1) * req.PageSize
		query = query.Offset(offset).Limit(req.PageSize)
	}

	err = query.Order("hour ASC").Find(&reports).Error
	if err != nil {
		return nil, 0, err
	}

	return reports, total, nil
}

// DeleteByDate 删除指定日期的报告
func (d *GoogleReportDAO) DeleteByDate(date string) error {
	return d.db.Where("data_date = ?", date).Delete(&model.GoogleReport{}).Error
}
