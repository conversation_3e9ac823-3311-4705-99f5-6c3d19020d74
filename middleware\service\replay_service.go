package service

import (
	"context"
	"database/sql"
	"encoding/json"
	"errors"
	"log"
	"math/rand"
	"os"
	"rsoc-system-go/dao"
	"rsoc-system-go/middleware/pkg/redis"
	"rsoc-system-go/model"
	"rsoc-system-go/store"
	"rsoc-system-go/tools"
	"rsoc-system-go/tools/constants"
	"sort"
	"strconv"
	"sync"
	"time"
)

var (
	globalRand = rand.New(rand.NewSource(time.Now().UnixNano()))
	uaCache    = make(map[int][]string)
	uaCacheMux sync.RWMutex
)

type RePlayService struct {
	ReplayTaskDao         *dao.ReplayTaskDao
	AutoTaskAllDataOldDao *dao.AutoTaskAllDataOldDAO
}

func (s *RePlayService) RePlayLine(date string) ([]map[string]string, error) {
	var resultMap []map[string]string
	//redisManager := store.GetRedisManager()
	//err := redisManager.ConnectRedis("task")
	//client := redisManager.GetRedisClient("task")
	indexdbStr := os.Getenv("redisdb")
	indexdb, _ := strconv.Atoi(indexdbStr)
	client := redis.SelectDB(indexdb)
	//line, err := client.HGet(context.Background(), "task_line", date).Result()
	line, err := client.HGetAll(context.Background(), "task_line:"+date).Result()
	if line != nil {
		// 收集所有时间
		times := make([]string, 0, len(line))
		for key := range line {
			times = append(times, key)
		}
		// 按时间排序
		sort.Strings(times)

		// 按排序后的时间构建结果
		for _, key := range times {
			resultMap = append(resultMap, map[string]string{
				"addtime": key,
				"number":  line[key],
			})
		}
		return resultMap, nil
	}

	// 根据日期查询历史曲线
	//d := dao.NewAutoTaskAllDataOldDAO(store.DB)
	d := dao.NewAutoTaskAllDataOld("old")
	result, err := d.GetHourlyCountByTaskID(date)
	if err != nil {
		return nil, err
	}
	return result, nil
}

func (s *RePlayService) RePlayLineRedis(date string) []map[string]string {
	var resultMap []map[string]string
	// 根据日期查询历史曲线
	/*redisManager := store.GetRedisManager()
	err := redisManager.ConnectRedis("task")
	client := redisManager.GetRedisClient("task")*/
	indexdbStr := os.Getenv("redisdb")
	indexdb, _ := strconv.Atoi(indexdbStr)
	client := redis.SelectDB(indexdb)
	//client := redis.SelectDB(1)
	result, err := client.HGetAll(context.Background(), "task_line:"+date).Result()
	if err != nil {
		return nil
	}

	// 按时间分组统计number
	timeMap := make(map[string]int64)
	for key, val := range result {
		// 格式化时间为 HH:MM 格式
		t, err := time.Parse("15:04:05", key)
		if err != nil {
			continue
		}
		formattedTime := t.Format("15:04")

		num, _ := strconv.ParseInt(val, 10, 64)
		timeMap[formattedTime] += num
	}

	// 按时间排序并构建结果
	times := make([]string, 0, len(timeMap))
	for t := range timeMap {
		times = append(times, t)
	}
	sort.Strings(times)

	for _, t := range times {
		resultMap = append(resultMap, map[string]string{
			"addtime": t,
			"number":  strconv.FormatInt(timeMap[t], 10),
		})
	}
	return resultMap
}

func (s *RePlayService) RePlayTaskLineRedis(date string, task_id string) []map[string]string {
	var resultMap []map[string]string
	// 根据日期查询历史曲线
	/*redisManager := store.GetRedisManager()
	err := redisManager.ConnectRedis("task")
	client := redisManager.GetRedisClient("task")*/
	indexdbStr := os.Getenv("redisdb")
	indexdb, _ := strconv.Atoi(indexdbStr)
	client := redis.SelectDB(indexdb)
	//client := redis.SelectDB(1)
	result, err := client.HGetAll(context.Background(), "task:"+task_id).Result()
	if err != nil {
		return nil
	}

	// 按时间分组统计number
	timeMap := make(map[string]int64)
	for key, val := range result {
		// 格式化时间为 HH:MM 格式
		t, err := time.Parse("15:04:05", key)
		if err != nil {
			continue
		}
		formattedTime := t.Format("15:04")

		num, _ := strconv.ParseInt(val, 10, 64)
		timeMap[formattedTime] += num
	}

	// 按时间排序并构建结果
	times := make([]string, 0, len(timeMap))
	for t := range timeMap {
		times = append(times, t)
	}
	sort.Strings(times)

	for _, t := range times {
		resultMap = append(resultMap, map[string]string{
			"addtime": t,
			"number":  strconv.FormatInt(timeMap[t], 10),
		})
	}
	return resultMap
}

func (s *RePlayService) RePlayDataFormat(task *model.ReplayTask) error {

	number := task.TaskNumber
	autoTaskAll := dao.NewAutoTaskAllDataOldDAO(store.DB)
	autoTaskAllList, err := autoTaskAll.List(&model.AutoTaskAllDataOld{Date: task.TaskDate}, nil)
	if err != nil {
		return err
	}

	// 重放曲线
	record, err := s.RePlayLine(task.TaskDate)
	/*record, err := autoTaskAll.GetHourlyCountByTaskID(
		task.TaskDate,
	)*/
	if err != nil {
		log.Printf(" 查询记录失败: %v", err)
		return err
	}

	// 计算原始总数
	var totalOriginalNumber int64
	for _, r := range record {
		// TODO 如果需要从当前时间往后推算时间曲线则需要在这里执行时间的判断
		startTime := r["addtime"]
		startTimeSt, _ := time.Parse("15:04", startTime)
		timeOnly := startTimeSt.Format("15:04")

		// 如果任务创建日期大于当前日期,则不需要比较时间
		taskDate, _ := time.Parse("2006-01-02", task.TaskCreateDate)
		currentDate := time.Now().Format("2006-01-02")
		currentDateParsed, _ := time.Parse("2006-01-02", currentDate)

		if !taskDate.After(currentDateParsed) {
			currentTimeOnly := time.Now().Format("15:04")
			if timeOnly < currentTimeOnly {
				continue
			}
		}
		// TODO 结束
		n, _ := strconv.ParseInt(r["number"], 10, 64)
		totalOriginalNumber += n
	}

	// 计算每条记录应该生成的数量
	totalNewNumber := int64(0)
	recordNumbers := make(map[string]int)
	for _, r := range record {
		// TODO 如果需要从当前时间往后推算时间曲线则需要在这里执行时间的判断
		startTime := r["addtime"]
		startTimeSt, _ := time.Parse("15:04", startTime)
		timeOnly := startTimeSt.Format("15:04")
		//dateOnly := time.Now().Format(time.DateOnly)
		//startTimeFormat, _ := time.Parse("2006-01-02 15:04", dateOnly+" "+timeOnly)
		// 如果任务创建日期大于当前日期,则不需要比较时间
		taskDate, _ := time.Parse("2006-01-02", task.TaskCreateDate)
		currentDate := time.Now().Format("2006-01-02")
		currentDateParsed, _ := time.Parse("2006-01-02", currentDate)

		if !taskDate.After(currentDateParsed) {
			currentTimeOnly := time.Now().Format("15:04")
			if timeOnly < currentTimeOnly {
				continue
			}
		}
		// TODO 结束
		originalNumber, _ := strconv.ParseInt(r["number"], 10, 64)
		newNumber := int(float64(originalNumber) * float64(number) / float64(totalOriginalNumber))
		recordNumbers[r["addtime"]] = newNumber
		totalNewNumber += int64(newNumber)
	}

	// 如果总数不等于目标数量,将差值按比例重新分配到每条记录中
	if totalNewNumber != number {
		diff := int(number - totalNewNumber)
		remaining := diff

		// 按原始数量比例分配差值
		for t, n := range recordNumbers {
			proportion := float64(n) / float64(totalNewNumber)
			addition := int(float64(diff) * proportion)
			if remaining > 0 {
				if addition > remaining {
					addition = remaining
				}
				recordNumbers[t] += addition
				remaining -= addition
			}
		}

		// 如果还有剩余差值,随机分配到其他记录中
		if remaining > 0 {
			times := make([]string, 0)
			for t := range recordNumbers {
				times = append(times, t)
			}
			if len(times) == 0 {
				return errors.New("没有记录可以分配差值")
			}
			for remaining > 0 {
				randIndex := rand.Intn(len(times))
				recordNumbers[times[randIndex]]++
				remaining--
			}
		}
	}

	autoTaskDao := dao.NewAutoTaskDao(store.DB)
	for _, r := range record {
		startTime := r["addtime"]
		newNumber := recordNumbers[startTime]
		// TODO 直接按照正常时间生成
		startTimeSt, _ := time.Parse("15:04", startTime)
		timeOnly := startTimeSt.Format("15:04")
		//nowTimeOnly := time.Now().Format("15:04")
		//startTimeFormat, _ := time.Parse("2006-01-02 15:04", dateOnly+" "+timeOnly)
		// 如果任务创建日期大于当前日期,则不需要比较时间
		taskDate, _ := time.Parse("2006-01-02", task.TaskCreateDate)
		currentDate := time.Now().Format("2006-01-02")
		currentDateParsed, _ := time.Parse("2006-01-02", currentDate)

		if !taskDate.After(currentDateParsed) {
			currentTimeOnly := time.Now().Format("15:04")
			if timeOnly < currentTimeOnly {
				continue
			}
		}
		// TODO 结束
		// 根据新的数量生成对应数量的随机时间记录
		for i := 0; i < newNumber; i++ {
			// 在这一分钟内随机生成时间
			startTimeSt, _ := time.Parse("15:04", startTime)
			formattedDate := task.TaskCreateDate
			if formattedDate == "" {
				formattedDate = time.Now().Format(time.DateOnly)
			}
			formattedTime := startTimeSt.Format("15:04")
			randomTime, _ := time.Parse("2006-01-02 15:04", formattedDate+" "+formattedTime)

			// 随机生成0-59的秒数和毫秒数
			randSeconds := rand.Intn(60)
			randMilliseconds := rand.Intn(1000)

			// 添加随机秒数和毫秒数,确保每条记录时间都不同
			randomTime = randomTime.Add(time.Duration(randSeconds)*time.Second + time.Duration(randMilliseconds)*time.Millisecond)

			// 从autoTaskAllList随机选择一条记录
			/*if len(autoTaskAllList) == 0 {
				return errors.New("没有记录可以生成随机时间")
			}*/
			randomIndex := rand.Intn(len(autoTaskAllList))
			randomRecord := autoTaskAllList[randomIndex]

			// 解析trafficData JSON获取cid
			var trafficData map[string]interface{}
			var originalOrder []string

			// 使用json.RawMessage保持原始字符串
			var rawData map[string]json.RawMessage
			if err := json.Unmarshal([]byte(randomRecord.TrafficData), &rawData); err != nil {
				log.Printf("解析TrafficData失败: %v", err)
				continue
			}

			// 记录原始字段顺序
			for k := range rawData {
				originalOrder = append(originalOrder, k)
			}

			// 解析为实际数据
			if err := json.Unmarshal([]byte(randomRecord.TrafficData), &trafficData); err != nil {
				log.Printf("解析TrafficData失败: %v", err)
				continue
			}

			// 获取原始cid并生成新的cid
			trafficData["cid"], _ = tools.NewStringGenerator().GenerateString(116)

			// 按原始顺序重新构建JSON
			orderedMap := make(map[string]interface{})
			for _, key := range originalOrder {
				orderedMap[key] = trafficData[key]
			}

			// 重新生成JSON
			newTrafficData, err := json.Marshal(orderedMap)
			if err != nil {
				log.Printf("生成新的TrafficData失败: %v", err)
				continue
			}
			var ua map[string]string
			uar := rand.New(rand.NewSource(time.Now().UnixNano()))
			uaIndex := uar.Intn(82) + 1
			// 从缓存获取或更新UA列表
			/*uaCacheMux.RLock()
			cachedUAs, exists := uaCache[uaIndex]
			uaCacheMux.RUnlock()*/

			//if !exists || len(cachedUAs) < 10000 {
			// 获取更大的采样来填充缓存

			key := "ua" + strconv.Itoa(uaIndex)
			count := constants.UAFileCountMap[key]
			valr := rand.New(rand.NewSource(time.Now().UnixNano()))
			val := valr.Intn(count) + 1
			scan, err := tools.GetRandomFromRedis(key, strconv.Itoa(val))
			if err != nil {
				log.Printf("获取随机UserAgent失败: %v", err)
			} else {
				/*				uaCacheMux.Lock()
								uaCache[uaIndex] = append(cachedUAs, scan)
								uaCacheMux.Unlock()*/
				if scan == "" {
					log.Printf("获取随机UserAgent为空")
				}
				err = json.Unmarshal([]byte(scan), &ua)
			}
			/*	} else {
				// 从缓存中随机选择一个UA
				randomUAIndex := globalRand.Intn(len(cachedUAs))
				err = json.Unmarshal([]byte(cachedUAs[randomUAIndex]), &ua)
			}*/

			if err != nil {
				log.Printf("解析随机UserAgent失败: %v", err)
			}

			languages := map[string]float64{
				"en-US,en;q=0.9":                      0.85, // 85% 的概率
				"es-US,es;q=0.9,en-US;q=0.8,en;q=0.7": 0.10, // 10% 的概率
				"es-US,es-419;q=0.9,es;q=0.8":         0.05, // 5% 的概率
			}
			if task.TaskCreateDate == "" {
				task.TaskCreateDate = formattedDate
			}
			// 创建新的AutoTask记录,使用随机记录的部分值
			autoTask := &model.AutoTask{
				TaskID:      sql.NullString{String: task.TaskID, Valid: true},
				Status:      sql.NullInt32{Int32: 1, Valid: true},
				AddTime:     sql.NullString{String: randomTime.Format(time.DateTime), Valid: true},
				Date:        sql.NullString{String: task.TaskCreateDate, Valid: true},
				ClickID:     sql.NullString{String: tools.Generate40UUID(), Valid: true},
				TrafficData: sql.NullString{String: string(newTrafficData), Valid: true},
				IPCountry:   sql.NullString{String: randomRecord.IPCountry, Valid: true},
				Country:     sql.NullString{String: "US", Valid: true},
				Ref:         sql.NullString{String: "https://m.facebook.com", Valid: true},
				Traffic:     sql.NullString{String: "facebook", Valid: true},
				Viewport:    sql.NullString{String: ua["viewport"], Valid: true},
				UserAgent:   sql.NullString{String: ua["useragent"], Valid: true},
				Lang:        sql.NullString{String: tools.SelectLanguage(languages), Valid: true},
				UserIP:      sql.NullString{String: randomRecord.UserIP, Valid: true},
				CampaignID:  sql.NullString{String: randomRecord.CampaignID, Valid: true},
				TrafficURL:  sql.NullString{String: randomRecord.TrafficURL, Valid: true},
			}

			// 保存到数据库
			if err := autoTaskDao.Create(autoTask); err != nil {
				log.Printf("保存AutoTask失败: %v", err)
				continue
			}

			log.Printf("成功创建AutoTask记录,TaskID: %s, 时间: %v", task.TaskID, randomTime)
		}
	}
	rePlayTaskDao := dao.NewReplayTaskDao(store.DB)
	task.Flag = 1
	task.Status = 1
	err = rePlayTaskDao.Update(task)
	if err != nil {
		log.Println("err", err)
	}
	client := store.RedisClient
	_, err = client.HDel(context.Background(), "out_ua*", "*").Result()
	if err != nil {
		log.Println("redis del err:", err)
	}
	log.Printf("记录生成完成")
	return nil
}

// SyncTaskLine2Redis 将任务曲线同步到Redis中
func (s *RePlayService) SyncTaskLine2Redis(date string) error {
	/*redisManager := store.GetRedisManager()
	err := redisManager.ConnectRedis("task")
	if err != nil {
		log.Println("err", err)
	}
	client := redisManager.GetRedisClient("task")*/
	indexdbStr := os.Getenv("redisdb")
	indexdb, _ := strconv.Atoi(indexdbStr)
	client := redis.SelectDB(indexdb)
	//client := redis.SelectDB(1)
	ctx := context.Background()
	result, err := client.HGetAll(ctx, "task_line:"+date).Result()
	if err != nil {
		log.Println("err", err)
	}
	if len(result) > 0 {
		return nil
	}
	//oldDao := dao.NewAutoTaskAllDataOldDAO(store.DB)
	oldDao := dao.NewAutoTaskAllDataOld("old")
	// 获取所有任务
	tasks, err := oldDao.GetHourlyCountByTaskID(date)
	if err != nil {
		log.Println("err", err)
		return err
	}
	//bytes, err := json.Marshal(tasks)
	// 按时间排序tasks
	sort.Slice(tasks, func(i, j int) bool {
		return tasks[i]["addtime"] < tasks[j]["addtime"]
	})
	for _, item := range tasks {
		_, err = client.HSet(ctx, "task_line:"+date, item["addtime"], item["number"]).Result()
	}
	if err != nil {
		log.Println("err", err)
		return err
	}
	//log.Println("任务重放曲线同步成功")
	return nil
}

func (s *RePlayService) RePlayDataFormatByCreate(task *model.ReplayTask) error {
	number := task.TaskNumber
	// 重放曲线
	record, err := s.RePlayLine(task.TaskDate)
	if err != nil {
		log.Printf(" 查询记录失败: %v", err)
		return err
	}

	// 按时间排序record
	sort.Slice(record, func(i, j int) bool {
		return record[i]["addtime"] < record[j]["addtime"]
	})

	// 计算原始总数和记录每个时间点的原始数量
	var totalOriginalNumber int64
	originalNumbers := make(map[string]int64)
	for _, r := range record {
		n, _ := strconv.ParseInt(r["number"], 10, 64)
		totalOriginalNumber += n
		originalNumbers[r["addtime"]] = n
	}

	// 计算每条记录应该生成的数量
	recordNumbers := make(map[string]int)

	// 按原始数量排序，用于优先分配给数量大的记录
	type timeCount struct {
		time  string
		count int64
	}
	var sortedCounts []timeCount
	for t, n := range originalNumbers {
		sortedCounts = append(sortedCounts, timeCount{time: t, count: n})
	}
	sort.Slice(sortedCounts, func(i, j int) bool {
		return sortedCounts[i].count > sortedCounts[j].count
	})

	// 第一轮：计算精确的浮点数分配
	allocated := float64(0)
	exactNumbers := make(map[string]float64)

	for _, tc := range sortedCounts {
		proportion := float64(tc.count) / float64(totalOriginalNumber)
		exactNum := float64(number) * proportion
		exactNumbers[tc.time] = exactNum
		allocated += exactNum
	}

	// 第二轮：转换为整数并处理舍入误差
	totalAllocated := int64(0)
	for _, tc := range sortedCounts {
		if totalAllocated >= number {
			recordNumbers[tc.time] = 0
			continue
		}

		exactNum := exactNumbers[tc.time]
		intNum := int(exactNum)

		// 确保不会超过剩余可分配数量
		remaining := number - totalAllocated
		if int64(intNum) > remaining {
			intNum = int(remaining)
		}

		recordNumbers[tc.time] = intNum
		totalAllocated += int64(intNum)
	}

	// 如果还有未分配的数量，分配给最大的几个时间点
	if totalAllocated < number {
		remaining := int(number - totalAllocated)
		for i := 0; i < len(sortedCounts) && remaining > 0; i++ {
			recordNumbers[sortedCounts[i].time]++
			remaining--
		}
	}

	// 将record存到redis
	ctx := context.Background()
	/*redisManager := store.GetRedisManager()
	err = redisManager.ConnectRedis("task")
	client := redisManager.GetRedisClient("task")*/
	indexdbStr := os.Getenv("redisdb")
	indexdb, _ := strconv.Atoi(indexdbStr)
	client := redis.SelectDB(indexdb)
	//client := redis.SelectDB(1)
	redisKey := "task:" + task.TaskID
	fieldValues := make(map[string]interface{}, len(record))

	// 按时间顺序保存到Redis
	totalSaved := 0
	for _, r := range record {
		startTime := r["addtime"]
		newNumber := recordNumbers[startTime]
		if newNumber > 0 {
			fieldValues[startTime] = newNumber
			totalSaved += newNumber
			log.Println(startTime, "=====", newNumber)
		}
	}
	log.Printf("总分配数量: %d, 目标数量: %d", totalSaved, number)

	pipe := client.Pipeline()
	pipe.HMSet(ctx, redisKey, fieldValues)
	_, err = pipe.Exec(ctx)
	if err != nil {
		log.Println("redis set err:", err)
	}
	log.Printf("记录生成完成")
	return nil
}

// TODO 程序设置时间为 柏林时间
