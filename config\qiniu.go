package config

import (
	"bytes"
	"context"
	"fmt"
	"github.com/qiniu/go-sdk/v7/auth"
	"github.com/qiniu/go-sdk/v7/storage"
	"os"
)

var Bucket = os.Getenv("QiniuBucket")

type Qiniu struct {
}

func (q *Qiniu) AuthQiniu() string {
	accessKey := os.Getenv("QiniuAccessKey")
	secretKey := os.Getenv("QiniuSecretKey")
	bucket := os.Getenv("QiniuBucket")
	putPolicy := storage.PutPolicy{
		Scope: bucket,
	}
	mac := auth.New(accessKey, secretKey)
	upToken := putPolicy.UploadToken(mac)
	return upToken
}

func (q *Qiniu) UploadByLocalFile(key string, localFile string) (string, error) {
	token := q.AuthQiniu()
	cfg := storage.Config{}
	// 空间对应的机房
	cfg.Region = &storage.ZoneXinjiapo
	// 是否使用https域名
	cfg.UseHTTPS = false
	// 上传是否使用CDN上传加速
	cfg.UseCdnDomains = false
	formUploader := storage.NewFormUploader(&cfg)
	ret := storage.PutRet{}
	err := formUploader.PutFile(context.Background(), &ret, token, key, localFile, nil)
	if err != nil {
		fmt.Println(err)
		return "", err
	}
	fmt.Println(ret.Key, ret.Hash)
	return ret.Key, nil
}
func (q *Qiniu) UploadByBytes(key string, data []byte) (string, error) {
	token := q.AuthQiniu()
	cfg := storage.Config{}
	// 空间对应的机房
	cfg.Region = &storage.ZoneXinjiapo
	// 是否使用https域名
	cfg.UseHTTPS = false
	// 上传是否使用CDN上传加速
	cfg.UseCdnDomains = false
	formUploader := storage.NewFormUploader(&cfg)
	ret := storage.PutRet{}
	dataLen := int64(len(data))
	err := formUploader.Put(context.Background(), &ret, token, key, bytes.NewReader(data), dataLen, nil)
	if err != nil {
		fmt.Println(err)
		return "", err
	}
	fmt.Println(ret.Key, ret.Hash)
	return ret.Key, nil
}
