package handler

import (
	"fmt"
	"log"
	"net/http"
	"rsoc-system-go/config"
	"rsoc-system-go/dao"
	"rsoc-system-go/model"
	"rsoc-system-go/service"
	"strconv"
	"time"

	"github.com/gin-gonic/gin"
)

type RsocKey struct {
	dao             dao.RsocKeyDao
	postbackService *service.PostbackService
}

// Create 创建重放任务
func (h *RsocKey) Create(c *gin.Context) {
	var task model.RsocKey
	if err := c.ShouldBindJSON(&task); err != nil {
		c.JSON(http.StatusOK, config.Result{http.StatusBadRequest, err.Error(), nil})
		return
	}
	if task.PostBackUrl != "" {
		var postbackReq model.PostbackRequest
		postbackReq.DomainName = "http://" + task.PostBackUrl + ":8080/rsoc/postback"
		postbackReq.SecurityKey = task.Key
		postbackReq.Campaign = "campaign"
		postbackReq.ClickID = "click_id"
		postbackReq.Payout = "payout"
		postbackReq.Country = "country"
		postbackReq.Zip = "zip"
		postbackReq.OSType = "os_type"
		postbackReq.Browser = "browser"
		postbackReq.DeviceType = "device_type"
		postbackReq.DeviceBrand = "device_brand"
		postbackReq.S1 = "subid1"
		postbackReq.S2 = "subid2"
		postbackReq.S3 = "subid3"

		h.postbackService = service.GetPostbackService()
		domain, err := h.postbackService.SetPostbackDomain(c, &postbackReq)
		if err != nil {
			c.JSON(http.StatusOK, config.Result{http.StatusInternalServerError, err.Error(), nil})
			return
		}
		log.Print(domain)
	}
	task.CreateTime = time.Now().Format(time.DateTime)
	task.CreateAt = strconv.Itoa(c.GetInt("userId")) // 假设通过中间件设置了用户名
	task.UpdateAt = strconv.Itoa(c.GetInt("userId"))
	task.UpdateTime = time.Now().Format(time.DateTime)
	if err := h.dao.Create(&task); err != nil {
		c.JSON(http.StatusOK, config.Result{http.StatusInternalServerError, err.Error(), nil})
		return
	}

	c.JSON(http.StatusOK, config.Result{1, "成功", task})
}

// Update 更新重放任务
func (h *RsocKey) Update(c *gin.Context) {
	var task model.RsocKey
	if err := c.ShouldBindJSON(&task); err != nil {
		c.JSON(http.StatusOK, config.Result{http.StatusBadRequest, err.Error(), nil})
		return
	}
	if task.PostBackUrl != "" {
		var postbackReq model.PostbackRequest
		postbackReq.DomainName = "http://" + task.PostBackUrl + ":8080/rsoc/postback"
		postbackReq.SecurityKey = task.Key
		postbackReq.Campaign = "campaign"
		postbackReq.ClickID = "click_id"
		postbackReq.Payout = "payout"
		postbackReq.Country = "country"
		postbackReq.Zip = "zip"
		postbackReq.OSType = "os_type"
		postbackReq.Browser = "browser"
		postbackReq.DeviceType = "device_type"
		postbackReq.DeviceBrand = "device_brand"
		postbackReq.S1 = "subid1"
		postbackReq.S2 = "subid2"
		postbackReq.S3 = "subid3"
		h.postbackService = service.GetPostbackService()
		domain, err := h.postbackService.SetPostbackDomain(c, &postbackReq)
		if err != nil {
			c.JSON(http.StatusOK, config.Result{http.StatusInternalServerError, err.Error(), nil})
			return
		}
		log.Print(domain)
	}

	task.UpdateAt = strconv.Itoa(c.GetInt("userId"))

	if err := h.dao.Update(&task); err != nil {
		c.JSON(http.StatusOK, config.Result{http.StatusInternalServerError, err.Error(), nil})
		return
	}

	c.JSON(http.StatusOK, config.Result{1, "success", task})
}

// Delete 删除重放任题 同时删除已生成任务 删除本地redis数据
func (h *RsocKey) Delete(c *gin.Context) {
	param := make(map[string]interface{})
	if err := c.ShouldBindJSON(&param); err != nil {
		c.JSON(http.StatusOK, config.Result{http.StatusBadRequest, err.Error(), nil})
		return
	}

	// 检查id参数是否存在
	if param["id"] == nil {
		c.JSON(http.StatusOK, config.Result{http.StatusBadRequest, "id参数不能为空", nil})
		return
	}

	// 将interface{}类型的id转换为float64,再转为int
	taskIDFloat, ok := param["id"].(float64)
	if !ok {
		c.JSON(http.StatusOK, config.Result{http.StatusBadRequest, "id参数类型错误", nil})
		return
	}
	taskID := int(taskIDFloat)

	// 删除数据库中的任务记录
	if err := h.dao.Delete(taskID); err != nil {
		c.JSON(http.StatusOK, config.Result{http.StatusInternalServerError, err.Error(), nil})
		return
	}

	c.JSON(http.StatusOK, config.Result{1, "success", nil})
}

// Get 获取重放任务
func (h *RsocKey) Get(c *gin.Context) {
	id := c.Query("id")
	var taskID int
	if _, err := fmt.Sscanf(id, "%d", &taskID); err != nil {
		c.JSON(http.StatusOK, config.Result{http.StatusBadRequest, "invalid id", nil})
		return
	}

	task, err := h.dao.GetByID(taskID)
	if err != nil {
		c.JSON(http.StatusOK, config.Result{http.StatusInternalServerError, err.Error(), nil})
		return
	}

	c.JSON(http.StatusOK, config.Result{http.StatusOK, "success", task})
}

// List 获取重放任务列表
func (h *RsocKey) List(c *gin.Context) {
	var pagereq PageReq
	pagereq.Page = 1
	pagereq.Size = 999

	var task model.RsocKey
	if err := c.ShouldBindJSON(&task); err != nil {
		c.JSON(http.StatusOK, config.Result{http.StatusBadRequest, err.Error(), nil})
		return
	}
	userId, _ := c.Get("userId")
	task.CreateAt = strconv.Itoa(userId.(int))
	param := map[string]interface{}{
		"create_at": map[string]interface{}{
			"eq": task.CreateAt,
		},
		"key_type": map[string]interface{}{
			"eq": task.KeyType,
		},
	}
	tasks, total, err := h.dao.Page(&task, param, pagereq.Page, pagereq.Size)
	if err != nil {
		c.JSON(http.StatusOK, config.Result{http.StatusInternalServerError, err.Error(), nil})
		return
	}
	c.JSON(http.StatusOK, config.Result{1, "success", gin.H{
		"data":  tasks,
		"total": total,
		"page":  pagereq.Page,
		"size":  pagereq.Size,
	}})

}

// UpdateStatus 更新任务状态
func (h *RsocKey) UpdateStatus(c *gin.Context) {

	var req struct {
		Status int `json:"status"`
		Id     int `json:"id"`
	}
	if err := c.ShouldBindJSON(&req); err != nil {
		c.JSON(http.StatusBadRequest, gin.H{"error": err.Error()})
		return
	}
	if err := h.dao.UpdateStatus(req.Id, req.Status); err != nil {
		c.JSON(http.StatusInternalServerError, gin.H{"error": err.Error()})
		return
	}

	c.JSON(http.StatusOK, config.Result{1, "success", nil})
}
