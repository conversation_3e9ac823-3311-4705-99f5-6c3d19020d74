package system

import (
	"net/http"
	sysservice "rsoc-system-go/middleware/service"
	"rsoc-system-go/model/system"
	"strconv"

	"github.com/gin-gonic/gin"
)

type CronHandler struct {
	sysservice.CronService
}

// AdminCronCreateHandler 创建定时任务
func (h *CronHandler) AdminCronCreateHandler(c *gin.Context) {
	var cron system.CronEntity
	if err := c.Bind<PERSON>(&cron); err != nil {
		c.JSON(http.StatusBadRequest, gin.H{"code": 400, "msg": err.Error()})
		return
	}

	result, err := h.CreateCronTask(&cron)
	if err != nil {
		c.JSON(http.StatusInternalServerError, gin.H{"code": 500, "msg": err.Error()})
		return
	}
	c.JSON(http.StatusOK, gin.H{"code": 200, "data": result})
}

// AdminCronUpdateHandler 更新定时任务
func (h *CronHandler) AdminCronUpdateHandler(c *gin.Context) {
	var cron system.CronEntity
	if err := c.BindJSON(&cron); err != nil {
		c.JSON(http.StatusBadRequest, gin.H{"code": 400, "msg": err.Error()})
		return
	}

	result, err := h.UpdateCronTask(&cron)
	if err != nil {
		c.JSON(http.StatusInternalServerError, gin.H{"code": 500, "msg": err.Error()})
		return
	}
	c.JSON(http.StatusOK, gin.H{"code": 200, "data": result})
}

// AdminCronDeleteHandler 删除定时任务
func (h *CronHandler) AdminCronDeleteHandler(c *gin.Context) {
	id := c.Query("id")
	if id == "" {
		c.JSON(http.StatusBadRequest, gin.H{"code": 400, "msg": "ID is required"})
		return
	}

	idInt, err := strconv.ParseInt(id, 10, 64)
	if err != nil {
		c.JSON(http.StatusBadRequest, gin.H{"code": 400, "msg": "Invalid ID format"})
		return
	}

	err = h.DeleteCronTask(idInt)
	if err != nil {
		c.JSON(http.StatusInternalServerError, gin.H{"code": 500, "msg": err.Error()})
		return
	}
	c.JSON(http.StatusOK, gin.H{"code": 200, "msg": "删除成功"})
}

// AdminCronGetHandler 获取单个定时任务
func (h *CronHandler) AdminCronGetHandler(c *gin.Context) {
	id := c.Query("id")
	if id == "" {
		c.JSON(http.StatusBadRequest, gin.H{"code": 400, "msg": "ID is required"})
		return
	}

	idInt, err := strconv.ParseInt(id, 10, 64)
	if err != nil {
		c.JSON(http.StatusBadRequest, gin.H{"code": 400, "msg": "Invalid ID format"})
		return
	}

	cron := &system.CronEntity{Id: idInt}
	result, err := h.GetCronByID(cron)
	if err != nil {
		c.JSON(http.StatusInternalServerError, gin.H{"code": 500, "msg": err.Error()})
		return
	}
	c.JSON(http.StatusOK, gin.H{"code": 200, "data": result})
}

// AdminCronChangeStatusHandler 更改定时任务状态
func (h *CronHandler) AdminCronChangeStatusHandler(c *gin.Context) {
	var cron system.CronEntity
	if err := c.BindJSON(&cron); err != nil {
		c.JSON(http.StatusBadRequest, gin.H{"code": 400, "msg": err.Error()})
		return
	}

	result, err := h.UpdateCronTaskStatus(&cron)
	if err != nil {
		c.JSON(http.StatusInternalServerError, gin.H{"code": 500, "msg": err.Error()})
		return
	}
	c.JSON(http.StatusOK, gin.H{"code": 200, "data": result})
}
