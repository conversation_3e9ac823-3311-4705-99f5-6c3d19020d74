package store

import (
	"fmt"
	"log"
	"sync"

	"gorm.io/gorm"
)

// DBType 数据库类型
type DBType string

// DBInstance 数据库实例标识
type DBInstance string

const (
	MySQL      DBType = "mysql"
	PostgreSQL DBType = "postgres"
)

// DBConfig 数据库配置
type DBConfig struct {
	Type     DBType
	Instance DBInstance // 数据库实例标识
	DSN      string
}

// dbManager 数据库管理器
type dbManager struct {
	mu        sync.RWMutex
	current   DBConfig
	configs   map[DBInstance]DBConfig
	clients   map[DBInstance]*gorm.DB
	defaultDB DBInstance
}

var (
	manager *dbManager
	once    sync.Once
)

// GetDBManager 获取数据库管理器单例
func GetDBManager() *dbManager {
	once.Do(func() {
		manager = &dbManager{
			configs:   make(map[DBInstance]DBConfig),
			clients:   make(map[DBInstance]*gorm.DB),
			defaultDB: DBInstance("mysql_default"), // 默认实例
		}
	})
	return manager
}

// RegisterDB 注册数据库配置
func (m *dbManager) RegisterDB(config DBConfig) {
	m.mu.Lock()
	defer m.mu.Unlock()

	if config.Instance == "" {
		// 如果未指定实例名，使用类型作为默认实例名
		config.Instance = DBInstance(string(config.Type) + "_default")
	}

	m.configs[config.Instance] = config
}

// SetDefaultDB 设置默认数据库实例
func (m *dbManager) SetDefaultDB(instance DBInstance) {
	m.mu.Lock()
	defer m.mu.Unlock()
	m.defaultDB = instance
}

// GetCurrentDB 获取当前使用的数据库配置
func (m *dbManager) GetCurrentDB() DBConfig {
	m.mu.RLock()
	defer m.mu.RUnlock()
	return m.current
}

// SetDB 设置数据库连接
func (m *dbManager) SetDB(instance DBInstance, db *gorm.DB) error {
	m.mu.Lock()
	defer m.mu.Unlock()

	if db == nil {
		return fmt.Errorf("database connection is nil")
	}

	if config, exists := m.configs[instance]; exists {
		m.clients[instance] = db
		m.current = config
		return nil
	}
	return fmt.Errorf("database instance %s not registered", instance)
}

// GetDB 获取数据库连接
func (m *dbManager) GetDB(instance DBInstance) (*gorm.DB, error) {
	m.mu.RLock()
	defer m.mu.RUnlock()

	if db, exists := m.clients[instance]; exists {
		return db, nil
	}
	return nil, fmt.Errorf("database instance %s not found", instance)
}

// GetDefaultDBConnection 获取默认数据库连接
func (m *dbManager) GetDefaultDBConnection() (*gorm.DB, error) {
	return m.GetDB(m.defaultDB)
}

// GetAllInstances 获取所有已注册的数据库实例
func (m *dbManager) GetAllInstances() []DBInstance {
	m.mu.RLock()
	defer m.mu.RUnlock()

	instances := make([]DBInstance, 0, len(m.configs))
	for instance := range m.configs {
		instances = append(instances, instance)
	}
	return instances
}

// GetInstancesByType 获取指定类型的所有数据库实例
func (m *dbManager) GetInstancesByType(dbType DBType) []DBInstance {
	m.mu.RLock()
	defer m.mu.RUnlock()

	instances := make([]DBInstance, 0)
	for instance, config := range m.configs {
		if config.Type == dbType {
			instances = append(instances, instance)
		}
	}
	return instances
}

// CloseAll 关闭所有数据库连接
func (m *dbManager) CloseAll() {
	m.mu.Lock()
	defer m.mu.Unlock()

	for instance, db := range m.clients {
		if sqlDB, err := db.DB(); err == nil {
			if err := sqlDB.Close(); err != nil {
				log.Printf("Failed to close database instance %s: %v", instance, err)
			}
		}
	}
	m.clients = make(map[DBInstance]*gorm.DB)
}
