package model

// AdTechAPIResponse API通用响应结构
type AdTechAPIResponse struct {
	Msg  string      `json:"msg"`  // 消息
	Code int         `json:"code"` // 状态码
	Time int64       `json:"time"` // 时间戳
	Data interface{} `json:"data"` // 数据，类型不固定
}

// AdTechArticle AdTech文章模型
type AdTechArticle struct {
	Topics     []string `json:"topics"`     // 主题
	Type       string   `json:"type"`       // 类型，固定为"CreateArticle"
	Country    string   `json:"country"`    // 国家
	Locale     string   `json:"locale"`     // 语言区域
	Title      string   `json:"title"`      // 标题
	Excerpt    string   `json:"excerpt"`    // 摘要
	CategoryID string   `json:"categoryId"` // 分类ID
}

// AdTechTrackingSettings AdTech跟踪设置
type AdTechTrackingSettings struct {
	Type                    string `json:"type"`                    // 跟踪设置类型
	S2sMetaToken            string `json:"s2sMetaToken"`            // Meta令牌
	S2sMetaPixelId          string `json:"s2sMetaPixelId"`          // Meta像素ID
	S2sMetaLandingPageEvent string `json:"s2sMetaLandingPageEvent"` // 落地页事件
	S2sMetaClickEvent       string `json:"s2sMetaClickEvent"`       // 点击事件
	S2sMetaSearchEvent      string `json:"s2sMetaSearchEvent"`      // 搜索事件
}

// AdTechTrackingData AdTech跟踪数据
type AdTechTrackingData struct {
	TrafficSource    string                 `json:"trafficSource"`    // 流量来源
	TrackingMethod   string                 `json:"trackingMethod"`   // 跟踪方法
	TrackingSettings AdTechTrackingSettings `json:"trackingSettings"` // 跟踪设置
}

// AdTechCampaign AdTech广告系列
type AdTechCampaign struct {
	Name         string             `json:"name"`         // 名称
	Type         string             `json:"type"`         // 类型，固定为"CreateCampaign"
	TrackingData AdTechTrackingData `json:"trackingData"` // 跟踪数据
}

// AdTechCreateCampaignRequest AdTech创建广告系列请求
type AdTechCreateCampaignRequest struct {
	PublishDomainName string         `json:"publishDomainName"` // 发布域名
	Article           AdTechArticle  `json:"article"`           // 文章
	Campaign          AdTechCampaign `json:"campaign"`          // 广告系列
}

// AdTechCreateCampaignResponse AdTech创建广告系列响应
type AdTechCreateCampaignResponse struct {
	Success      bool   `json:"success"`      // 是否成功
	Message      string `json:"message"`      // 消息
	ResourceID   string `json:"resourceId"`   // 资源ID
	ResourceType string `json:"resourceType"` // 资源类型
	// 其他可能的响应字段
}

// AdTechCampaignDetail API返回的广告系列详细信息
type AdTechCampaignDetail struct {
	ID                     string                        `json:"id"`                               // 广告系列ID
	PublishDomainName      string                        `json:"publishDomainName"`                // 发布域名
	Article                AdTechArticleDetail           `json:"article"`                          // 文章详情
	Campaign               AdTechCampaignInfo            `json:"campaign"`                         // 广告系列信息
	Status                 string                        `json:"status"`                           // 状态
	ProcessingErrorDetails *AdTechProcessingErrorDetails `json:"processingErrorDetails,omitempty"` // 处理错误详情，可能为空
	CreatedDate            string                        `json:"createdDate"`                      // 创建日期
	LastModifiedDate       string                        `json:"lastModifiedDate"`                 // 最后修改日期
	TrackingUrl            string                        `json:"trackingUrl,omitempty"`            // 落地页URL，可能为空
}

// AdTechArticleDetail 文章详情
type AdTechArticleDetail struct {
	Country       string          `json:"country"`       // 国家
	Locale        string          `json:"locale"`        // 语言区域
	FeaturedImage map[string]bool `json:"featuredImage"` // 特色图片
	Title         string          `json:"title"`         // 标题
	Excerpt       string          `json:"excerpt"`       // 摘要
	Topics        []string        `json:"topics"`        // 主题
}

// AdTechCampaignInfo 广告系列信息
type AdTechCampaignInfo struct {
	Name         string                   `json:"name"`         // 名称
	TrackingData AdTechTrackingDataDetail `json:"trackingData"` // 跟踪数据
	Id           int                      `json:"id"`
}

// AdTechTrackingDataDetail 广告跟踪数据详情
type AdTechTrackingDataDetail struct {
	TrafficSource    string                       `json:"trafficSource"`    // 流量来源
	TrackingSettings AdTechTrackingSettingsDetail `json:"trackingSettings"` // 跟踪设置
	TrackingMethod   string                       `json:"trackingMethod"`   // 跟踪方法
}

// AdTechTrackingSettingsDetail 跟踪设置详情
type AdTechTrackingSettingsDetail struct {
	Type                    string `json:"type"`                         // 跟踪设置类型
	S2sMetaLandingPageEvent string `json:"s2sMetaLandingPageEvent"`      // 落地页事件
	S2sMetaToken            string `json:"s2sMetaToken,omitempty"`       // Meta令牌，可能为空
	S2sMetaPixelId          string `json:"s2sMetaPixelId,omitempty"`     // Meta像素ID，可能为空
	S2sMetaClickEvent       string `json:"s2sMetaClickEvent,omitempty"`  // 点击事件，可能为空
	S2sMetaSearchEvent      string `json:"s2sMetaSearchEvent,omitempty"` // 搜索事件，可能为空
}

// AdTechProcessingErrorDetails 处理错误详情
type AdTechProcessingErrorDetails struct {
	Type       string `json:"type"`       // 错误类型
	Title      string `json:"title"`      // 错误标题
	Status     int    `json:"status"`     // 错误状态码
	Detail     string `json:"detail"`     // 错误详情
	Suggestion string `json:"suggestion"` // 建议解决方案
}

// AdTechCampaignViewResponse AdTech查看广告系列响应
type AdTechCampaignViewResponse struct {
	Success bool                  `json:"success"`          // 是否成功
	Message string                `json:"message"`          // 消息
	Detail  *AdTechCampaignDetail `json:"detail,omitempty"` // 详细信息，可能为空

	// 保留原始字段以保持向下兼容
	CampaignID  string `json:"campaignId,omitempty"`  // 广告系列ID
	Status      string `json:"status,omitempty"`      // 状态
	TrackingUrl string `json:"trackingUrl,omitempty"` // 投放链接
}

// AdTechDomain AdTech域名信息
type AdTechDomain struct {
	Id     string `json:"id"`
	Domain string `json:"domain"` // 域名
	Url    string `json:"url"`    // 域名URL
}

// AdTechDomainsResponse AdTech获取域名列表响应
type AdTechDomainsResponse struct {
	Msg  string         `json:"msg"`  // 消息
	Code int            `json:"code"` // 状态码
	Time int64          `json:"time"` // 时间戳
	Data []AdTechDomain `json:"data"` // 域名数据
}
