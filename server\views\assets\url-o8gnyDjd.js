import{_ as ie,u as re,o as ue,r as U,a as pe,M as i,j as me,c as A,b as m,d as n,w as a,e as o,B as ce,k as q,f as w,C as G,m as D,h as y,t as z,n as E,F as fe,g as ge,p as _e,I as ye,s as x}from"./index-DlVegDiC.js";import{S as he,a as ke}from"./index-CSU5nP3m.js";import{P as ve}from"./PlusOutlined-Cg2o2XQN.js";import{_ as be}from"./index-B8PO_1fg.js";import{_ as Se}from"./index-D_v6jNwB.js";import{_ as we}from"./index-Dj7iX41A.js";import{_ as Ue}from"./index-1uCBjWky.js";import{_ as $e}from"./index-DXcpAzs8.js";const Oe={class:"main"},Ce={class:"filter"},xe={class:"filter_box"},De={class:"filter_item"},Ie={class:"filter_item"},Ne={class:"filter_item"},Pe={__name:"url",setup(Te){const h="",c=re(),T=pe();ue(()=>{H(),Q()});const k=U({id:void 0,two:void 0,status:"1"}),H=()=>{fetch(`${h}/SeDo/list`,{method:"POST",headers:{token:c.token}}).then(t=>t.json()).then(t=>{console.log(t),t.code==1?r.sedo_list=t.data:t.code==3e3?(c.$patch({token:!1}),T.push("/login"),i.error({title:t.msg})):i.error({title:t.msg})}).catch(t=>{i.error({title:"服务器错误",content:`${t}`})})},Q=()=>{fetch(`${h}/TwoDirectory/Alllist`,{method:"POST",headers:{token:c.token}}).then(t=>t.json()).then(t=>{console.log(t),t.code==1?r.two_list=t.data:t.code==3e3?(c.$patch({token:!1}),T.push("/login"),i.error({title:t.msg})):i.error({title:t.msg})}).catch(t=>{i.error({title:"服务器错误",content:`${t}`})})},r=U({data:[],loading:!1,sedo_list:[],two_list:[],main_list:[]}),v=()=>{r.loading=!0,fetch(`${h}/SeDo/urlList`,{method:"POST",body:JSON.stringify({sedoId:k.id,twoDirectoryId:k.two,status:k.status}),headers:{token:c.token}}).then(t=>t.json()).then(t=>{console.log(t),t.code==1?r.data=t.data.map(e=>(e.checked=e.status==1,e.checked_loading=!1,e)):t.code==3e3?(c.$patch({token:!1}),T.push("/login"),i.error({title:t.msg})):(r.data=[],i.error({title:t.msg})),r.loading=!1}).catch(t=>{i.error({title:"服务器错误",content:`${t}`})})},$=U({selectedRowKeys:[],onChange:(t,e)=>{console.log(t),$.selectedRowKeys=t}}),F=()=>{fetch(`${h}/MainDirectory/list`,{method:"POST",body:JSON.stringify({directoryStatus:1,page:1,limit:200}),headers:{token:c.token}}).then(t=>t.json()).then(t=>{console.log(t),r.main_list=t.data.data.map(e=>(e.isLeaf=!1,e))}).catch(t=>{i.error({title:"服务器错误",content:`${t}`})})},J=t=>{const e=t[t.length-1];e.loading=!0,console.log(e),fetch(`${h}/TwoDirectory/list`,{method:"POST",body:JSON.stringify({mianId:e.id,directoryStatus:1,page:1,limit:200}),headers:{token:c.token}}).then(p=>p.json()).then(p=>{console.log(p),e.loading=!1,e.children=p.data.data,r.main_list=[...r.main_list]}).catch(p=>{i.error({title:"服务器错误",content:`${p}`})})};let O=0;const K=me(),d=U({open:!1,main_directory:[],id:void 0,url:"",rate:"",note:"",option:{},selectedOptions:{},loading:!1}),W=()=>{O=0,d.open=!0,F()},X=(t,e)=>{console.log(e),d.option=e},Y=(t,e)=>{d.selectedOptions=e},L=()=>{if(!d.main_directory[1])return i.error({title:"请选择关键词二级目录"}),!1;d.loading=!0;let t=d.url.split(`
`);fetch(`${h}/SeDo/addSeDoUrl`,{method:"POST",body:JSON.stringify({twoDirectoryId:d.main_directory[1],twoDirectoryName:d.selectedOptions[1].name,sedoId:d.id,key:d.option.key,url:t[O],rate:d.rate,note:d.note}),headers:{token:c.token}}).then(e=>e.json()).then(e=>{console.log(e),e.code==1?(x.success(`${t[O]}添加成功`),t.length==O+1?(x.success("全部添加成功"),v(),K.value.resetFields(),d.open=!1,d.loading=!1):(O=O+1,L())):i.error({title:e.msg})}).catch(e=>{i.error({title:"服务器错误",content:`${e}`})})},s=U({open:!1,main_directory:[],edit_id:0,url:"",rate:"",note:"",status:1,selectedOptions:{},loading:!1}),Z=t=>{console.log(t),s.edit_id=t.id,s.url=t.url,s.rate=t.rate,s.status=t.checked,s.note=t.note,s.open=!0,F()},ee=(t,e)=>{s.selectedOptions=e},te=()=>{if(!s.main_directory[1])return i.error({title:"请选择关键词二级目录"}),!1;s.loading=!0,fetch(`${h}/SeDo/updateSeDoUrl`,{method:"POST",body:JSON.stringify({id:s.edit_id,twoDirectoryId:s.main_directory[1],twoDirectoryName:s.selectedOptions[1].name,rate:s.rate,status:s.status?1:2,note:s.note}),headers:{token:c.token}}).then(t=>t.json()).then(t=>{console.log(t),t.code==1?(x.success("修改成功"),v()):i.error({title:t.msg}),s.open=!1,s.loading=!1}).catch(t=>{i.error({title:"服务器错误",content:`${t}`})})},g=U({open:!1,rate:"",loading:!1}),oe=()=>{console.log(g.rate),g.loading=!0,fetch(`${h}/sedo/updateSeDoUrlAllRate`,{method:"POST",body:JSON.stringify({id:$.selectedRowKeys,rate:g.rate}),headers:{token:c.token}}).then(t=>t.json()).then(t=>{console.log(t),t.code==1?(x.success(t.msg),v()):i.error({title:t.msg}),g.open=!1,g.loading=!1}).catch(t=>{i.error({title:"服务器错误",content:`${t}`})})},_=U({open:!1,status:!0,loading:!1}),ne=()=>{console.log(_.status),_.loading=!0,fetch(`${h}/sedo/updateSeDoUrlAllStatus`,{method:"POST",body:JSON.stringify({id:$.selectedRowKeys,status:_.status?1:2}),headers:{token:c.token}}).then(t=>t.json()).then(t=>{console.log(t),t.code==1?(x.success(t.msg),v()):i.error({title:t.msg}),_.open=!1,_.loading=!1}).catch(t=>{i.error({title:"服务器错误",content:`${t}`})})},B=(t,e)=>{let p;t=="status"?p={id:e.id,rate:e.rate,status:e.checked?1:2}:p={id:e.id,rate:e.rate,status:e.status},fetch(`${h}/SeDo/updateSeDoUrlRate`,{method:"POST",body:JSON.stringify(p),headers:{token:c.token}}).then(f=>f.json()).then(f=>{console.log(f),f.code==1?(x.success(f.msg),v()):i.error({title:f.msg})}).catch(f=>{i.error({title:"服务器错误",content:`${f}`})})},le=[{title:"ID",dataIndex:"id",key:"id",align:"center"},{title:"二级目录",dataIndex:"twoDirectoryName",key:"twoDirectoryName",align:"center"},{title:"域名",dataIndex:"url",key:"url",align:"center"},{title:"Key",dataIndex:"key",key:"key",align:"center"},{title:"价格",dataIndex:"price",key:"price",align:"center"},{title:"权重",dataIndex:"rate",key:"rate",align:"center"},{title:"状态",dataIndex:"status",key:"status",align:"center"},{title:"备注",dataIndex:"note",key:"note",align:"center"},{title:"添加日期",dataIndex:"add_time",key:"add_time",align:"center"},{title:"更新日期",dataIndex:"update_time",key:"update_time",align:"center"},{title:"操作",key:"action",align:"center"}];return(t,e)=>{const p=ke,f=he,b=ce,M=we,ae=ve,R=$e,se=be,de=Ue,V=Se,u=ge,j=_e,I=ye,N=fe,P=i;return w(),A(q,null,[m("div",Oe,[m("div",null,[m("div",Ce,[m("div",xe,[m("div",De,[e[21]||(e[21]=m("p",null,"Sedo账号：",-1)),n(f,{value:o(k).id,"onUpdate:value":e[0]||(e[0]=l=>o(k).id=l),style:{width:"300px"},onChange:v,placeholder:"请选择Sedo账号查看数据"},{default:a(()=>[(w(!0),A(q,null,G(o(r).sedo_list,l=>(w(),D(p,{value:l.id},{default:a(()=>[y(z(l.note),1)]),_:2},1032,["value"]))),256))]),_:1},8,["value"])]),m("div",Ie,[e[22]||(e[22]=m("p",null,"二级目录：",-1)),n(f,{value:o(k).two,"onUpdate:value":e[1]||(e[1]=l=>o(k).two=l),style:{width:"300px"},onChange:v,placeholder:"请选择二级目录查看数据"},{default:a(()=>[(w(!0),A(q,null,G(o(r).two_list,l=>(w(),D(p,{value:l.id},{default:a(()=>[y(z(l.name),1)]),_:2},1032,["value"]))),256))]),_:1},8,["value"])]),m("div",Ne,[n(f,{ref:"select",value:o(k).status,"onUpdate:value":e[2]||(e[2]=l=>o(k).status=l),style:{width:"200px"},onChange:v},{default:a(()=>[n(p,{value:"1"},{default:a(()=>e[23]||(e[23]=[y("激活")])),_:1}),n(p,{value:"2"},{default:a(()=>e[24]||(e[24]=[y("关闭")])),_:1})]),_:1},8,["value"])])]),m("div",null,[n(b,{onClick:e[3]||(e[3]=l=>o(g).open=!0),disabled:o($).selectedRowKeys.length==0},{default:a(()=>e[25]||(e[25]=[y(" 批量修改权重 ")])),_:1},8,["disabled"]),n(M,{type:"vertical"}),n(b,{onClick:e[4]||(e[4]=l=>o(_).open=!0),disabled:o($).selectedRowKeys.length==0},{default:a(()=>e[26]||(e[26]=[y(" 批量修改状态 ")])),_:1},8,["disabled"]),n(M,{type:"vertical"}),n(b,{type:"primary",onClick:W},{icon:a(()=>[n(ae)]),default:a(()=>[e[27]||(e[27]=y(" 添加Sedo域名 "))]),_:1})])])]),n(de,{columns:le,"data-source":o(r).data,rowKey:"id",pagination:!1,loading:o(r).loading,bordered:"","row-selection":o($)},{bodyCell:a(({column:l,record:S})=>[l.key==="status"?(w(),D(R,{key:0,checked:S.checked,"onUpdate:checked":C=>S.checked=C,loading:S.checked_loading,onChange:C=>B("status",S)},null,8,["checked","onUpdate:checked","loading","onChange"])):E("",!0),l.key==="rate"?(w(),D(se,{key:1,value:S.rate,"onUpdate:value":C=>S.rate=C,min:0,onPressEnter:C=>B("rate",S)},null,8,["value","onUpdate:value","onPressEnter"])):E("",!0),l.key==="action"?(w(),D(b,{key:2,type:"link",onClick:C=>Z(S)},{default:a(()=>e[28]||(e[28]=[y("修改")])),_:2},1032,["onClick"])):E("",!0)]),_:1},8,["data-source","loading","row-selection"])]),n(P,{open:o(d).open,"onUpdate:open":e[10]||(e[10]=l=>o(d).open=l),title:"添加Sedo域名",footer:null,maskClosable:!1},{default:a(()=>[e[30]||(e[30]=m("div",{style:{height:"20px"}},null,-1)),n(N,{ref_key:"add_form",ref:K,model:o(d),onFinish:L,"label-col":{span:4},"wrapper-col":{span:18}},{default:a(()=>[n(u,{label:"关键词",name:"main_directory",rules:[{required:!0,message:"请选择关键词"}]},{default:a(()=>[n(V,{value:o(d).main_directory,"onUpdate:value":e[5]||(e[5]=l=>o(d).main_directory=l),options:o(r).main_list,"load-data":J,placeholder:"请选择关键词","change-on-select":"","field-names":{label:"name",value:"id"},onChange:Y},null,8,["value","options"])]),_:1}),n(u,{label:"Sedo账号",name:"id",rules:[{required:!0,message:"请选择Sedo账号"}]},{default:a(()=>[n(f,{value:o(d).id,"onUpdate:value":e[6]||(e[6]=l=>o(d).id=l),placeholder:"请选择Sedo账号",onChange:X,options:o(r).sedo_list,"field-names":{label:"note",value:"id"}},null,8,["value","options"])]),_:1}),n(u,{label:"域名",name:"url",rules:[{required:!0,message:"请输入域名"}]},{default:a(()=>[n(j,{value:o(d).url,"onUpdate:value":e[7]||(e[7]=l=>o(d).url=l),placeholder:"每行填写一个域名",rows:6},null,8,["value"])]),_:1}),n(u,{label:"权重",name:"rate",rules:[{required:!0,message:"请输入权重"}]},{default:a(()=>[n(I,{value:o(d).rate,"onUpdate:value":e[8]||(e[8]=l=>o(d).rate=l),placeholder:"权重"},null,8,["value"])]),_:1}),n(u,{label:"备注",name:"note"},{default:a(()=>[n(j,{value:o(d).note,"onUpdate:value":e[9]||(e[9]=l=>o(d).note=l),placeholder:"备注",rows:2},null,8,["value"])]),_:1}),n(u,{"wrapper-col":{offset:4,span:18}},{default:a(()=>[n(b,{type:"primary","html-type":"submit",loading:o(d).loading},{default:a(()=>e[29]||(e[29]=[y(" 提交 ")])),_:1},8,["loading"])]),_:1})]),_:1},8,["model"])]),_:1},8,["open"]),n(P,{open:o(s).open,"onUpdate:open":e[16]||(e[16]=l=>o(s).open=l),title:"编辑Sedo域名",footer:null,maskClosable:!1},{default:a(()=>[e[32]||(e[32]=m("div",{style:{height:"20px"}},null,-1)),n(N,{model:o(s),onFinish:te,"label-col":{span:4},"wrapper-col":{span:18}},{default:a(()=>[n(u,{label:"关键词",name:"main_directory",rules:[{required:!0,message:"请选择关键词"}]},{default:a(()=>[n(V,{value:o(s).main_directory,"onUpdate:value":e[11]||(e[11]=l=>o(s).main_directory=l),options:o(r).main_list,"load-data":J,placeholder:"请选择关键词","change-on-select":"","field-names":{label:"name",value:"id"},onChange:ee},null,8,["value","options"])]),_:1}),n(u,{label:"域名",name:"url",rules:[{required:!0,message:"请输入域名"}]},{default:a(()=>[n(I,{value:o(s).url,"onUpdate:value":e[12]||(e[12]=l=>o(s).url=l),placeholder:"域名",disabled:""},null,8,["value"])]),_:1}),n(u,{label:"权重",name:"rate",rules:[{required:!0,message:"请输入权重"}]},{default:a(()=>[n(I,{value:o(s).rate,"onUpdate:value":e[13]||(e[13]=l=>o(s).rate=l),placeholder:"权重"},null,8,["value"])]),_:1}),n(u,{label:"状态",name:"status"},{default:a(()=>[n(R,{checked:o(s).status,"onUpdate:checked":e[14]||(e[14]=l=>o(s).status=l)},null,8,["checked"])]),_:1}),n(u,{label:"备注",name:"note"},{default:a(()=>[n(j,{value:o(s).note,"onUpdate:value":e[15]||(e[15]=l=>o(s).note=l),placeholder:"备注",rows:2},null,8,["value"])]),_:1}),n(u,{"wrapper-col":{offset:4,span:18}},{default:a(()=>[n(b,{type:"primary","html-type":"submit",loading:o(s).loading},{default:a(()=>e[31]||(e[31]=[y(" 提交 ")])),_:1},8,["loading"])]),_:1})]),_:1},8,["model"])]),_:1},8,["open"]),n(P,{open:o(g).open,"onUpdate:open":e[18]||(e[18]=l=>o(g).open=l),title:"批量修改权重",footer:null,maskClosable:!1},{default:a(()=>[e[34]||(e[34]=m("div",{style:{height:"20px"}},null,-1)),n(N,{model:o(g),onFinish:oe,"label-col":{span:4},"wrapper-col":{span:18}},{default:a(()=>[n(u,{label:"权重",name:"rate",rules:[{required:!0,message:"请输入权重"}]},{default:a(()=>[n(I,{value:o(g).rate,"onUpdate:value":e[17]||(e[17]=l=>o(g).rate=l),placeholder:"权重"},null,8,["value"])]),_:1}),n(u,{"wrapper-col":{offset:4,span:18}},{default:a(()=>[n(b,{type:"primary","html-type":"submit",loading:o(g).loading},{default:a(()=>e[33]||(e[33]=[y(" 提交 ")])),_:1},8,["loading"])]),_:1})]),_:1},8,["model"])]),_:1},8,["open"]),n(P,{open:o(_).open,"onUpdate:open":e[20]||(e[20]=l=>o(_).open=l),title:"批量修改状态",footer:null,maskClosable:!1},{default:a(()=>[e[36]||(e[36]=m("div",{style:{height:"20px"}},null,-1)),n(N,{model:o(_),onFinish:ne,"label-col":{span:4},"wrapper-col":{span:18}},{default:a(()=>[n(u,{label:"状态",name:"status"},{default:a(()=>[n(R,{checked:o(_).status,"onUpdate:checked":e[19]||(e[19]=l=>o(_).status=l)},null,8,["checked"])]),_:1}),n(u,{"wrapper-col":{offset:4,span:18}},{default:a(()=>[n(b,{type:"primary","html-type":"submit",loading:o(_).loading},{default:a(()=>e[35]||(e[35]=[y(" 提交 ")])),_:1},8,["loading"])]),_:1})]),_:1},8,["model"])]),_:1},8,["open"])],64)}}},Le=ie(Pe,[["__scopeId","data-v-3688db7f"]]);export{Le as default};
