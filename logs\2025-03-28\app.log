[GIN-debug] [WARNING] Creating an Engine instance with the Logger and Recovery middleware already attached.

[GIN-debug] [WARNING] Running in "debug" mode. Switch to "release" mode in production.
 - using env:	export GIN_MODE=release
 - using code:	gin.SetMode(gin.ReleaseMode)

2025/03/28 04:12:38 mysql.go:42: [mysql] Initializing MySQL instance: default
2025/03/28 04:12:38 mysql.go:43: [mysql] DSN: root:root@tcp(127.0.0.1:3306)/rosc?charset=utf8mb4&parseTime=true&loc=Local
[GIN-debug] GET    /swagger/*any             --> github.com/swaggo/gin-swagger.CustomWrapHandler.func1 (5 handlers)
[GIN-debug] GET    /assets/*filepath         --> github.com/gin-gonic/gin.(*RouterGroup).createStaticHandler.func1 (5 handlers)
[GIN-debug] HEAD   /assets/*filepath         --> github.com/gin-gonic/gin.(*RouterGroup).createStaticHandler.func1 (5 handlers)
[GIN-debug] GET    /                         --> replay-go/server.LoadTmpl.func1 (5 handlers)
2025/03/28 04:12:38 main.go:52: 守护进程启动失败: daemon: Non-POSIX OS is not supported
2025/03/28 04:12:38 main.go:60: 守护进程启动成功
2025/03/28 04:12:38 main.go:92: 服务器已启动在端口 8080
2025/03/28 04:12:38 redis.go:125: [redis] Main Redis instance connected successfully
2025/03/28 04:12:38 redis.go:153: [redis] Additional Redis instance local connected successfully
2025/03/28 04:12:38 redis.go:153: [redis] Additional Redis instance task connected successfully
[GIN-debug] [WARNING] Creating an Engine instance with the Logger and Recovery middleware already attached.

[GIN-debug] [WARNING] Running in "debug" mode. Switch to "release" mode in production.
 - using env:	export GIN_MODE=release
 - using code:	gin.SetMode(gin.ReleaseMode)

[GIN-debug] GET    /swagger/*any             --> github.com/swaggo/gin-swagger.CustomWrapHandler.func1 (5 handlers)
2025/03/28 04:18:29 mysql.go:42: [mysql] Initializing MySQL instance: default
2025/03/28 04:18:29 mysql.go:43: [mysql] DSN: root:root@tcp(127.0.0.1:3306)/rosc?charset=utf8mb4&parseTime=true&loc=Local
[GIN-debug] GET    /assets/*filepath         --> github.com/gin-gonic/gin.(*RouterGroup).createStaticHandler.func1 (5 handlers)
[GIN-debug] HEAD   /assets/*filepath         --> github.com/gin-gonic/gin.(*RouterGroup).createStaticHandler.func1 (5 handlers)
[GIN-debug] GET    /                         --> rosc-system-go/server.LoadTmpl.func1 (5 handlers)
2025/03/28 04:18:29 main.go:52: 守护进程启动失败: daemon: Non-POSIX OS is not supported
2025/03/28 04:18:29 main.go:60: 守护进程启动成功
2025/03/28 04:18:29 main.go:92: 服务器已启动在端口 8080
2025/03/28 04:18:29 redis.go:125: [redis] Main Redis instance connected successfully
2025/03/28 04:21:26 mysql.go:41: [mysql] Initializing MySQL instance: default
2025/03/28 04:21:26 mysql.go:42: [mysql] DSN: root:root@tcp(127.0.0.1:3306)/rosc?charset=utf8mb4&parseTime=true&loc=Local
2025/03/28 04:21:26 redis.go:125: [redis] Main Redis instance connected successfully
2025/03/28 04:21:26 mysql.go:93: [mysql] MySQL instance default connected successfully
2025/03/28 04:21:26 mysql.go:99: [mysql] Default MySQL instance set successfully
[GIN-debug] [WARNING] Creating an Engine instance with the Logger and Recovery middleware already attached.

[GIN-debug] [WARNING] Running in "debug" mode. Switch to "release" mode in production.
 - using env:	export GIN_MODE=release
 - using code:	gin.SetMode(gin.ReleaseMode)

[GIN-debug] GET    /swagger/*any             --> github.com/swaggo/gin-swagger.CustomWrapHandler.func1 (5 handlers)
[GIN-debug] GET    /assets/*filepath         --> github.com/gin-gonic/gin.(*RouterGroup).createStaticHandler.func1 (5 handlers)
[GIN-debug] HEAD   /assets/*filepath         --> github.com/gin-gonic/gin.(*RouterGroup).createStaticHandler.func1 (5 handlers)
[GIN-debug] GET    /                         --> rosc-system-go/server.LoadTmpl.func1 (5 handlers)
2025/03/28 04:21:26 main.go:52: 守护进程启动失败: daemon: Non-POSIX OS is not supported
2025/03/28 04:21:26 main.go:60: 守护进程启动成功
2025/03/28 04:21:26 main.go:92: 服务器已启动在端口 8080
2025/03/28 04:22:11 main.go:96: 正在关闭服务器...
2025/03/28 04:22:11 main.go:103: 正在停止调度器...
2025/03/28 04:22:11 main.go:105: 调度器已停止
2025/03/28 04:22:11 main.go:112: 服务器已成功关闭
2025/03/28 04:22:21 mysql.go:41: [mysql] Initializing MySQL instance: default
2025/03/28 04:22:21 mysql.go:42: [mysql] DSN: root:root@tcp(127.0.0.1:3306)/rosc?charset=utf8mb4&parseTime=true&loc=Local
2025/03/28 04:22:21 redis.go:125: [redis] Main Redis instance connected successfully
2025/03/28 04:22:21 mysql.go:93: [mysql] MySQL instance default connected successfully
2025/03/28 04:22:21 mysql.go:99: [mysql] Default MySQL instance set successfully
[GIN-debug] [WARNING] Creating an Engine instance with the Logger and Recovery middleware already attached.

[GIN-debug] [WARNING] Running in "debug" mode. Switch to "release" mode in production.
 - using env:	export GIN_MODE=release
 - using code:	gin.SetMode(gin.ReleaseMode)

[GIN-debug] GET    /swagger/*any             --> github.com/swaggo/gin-swagger.CustomWrapHandler.func1 (5 handlers)
[GIN-debug] GET    /assets/*filepath         --> github.com/gin-gonic/gin.(*RouterGroup).createStaticHandler.func1 (5 handlers)
[GIN-debug] HEAD   /assets/*filepath         --> github.com/gin-gonic/gin.(*RouterGroup).createStaticHandler.func1 (5 handlers)
[GIN-debug] GET    /                         --> rosc-system-go/server.LoadTmpl.func1 (5 handlers)
2025/03/28 04:22:21 main.go:52: 守护进程启动失败: daemon: Non-POSIX OS is not supported
2025/03/28 04:22:21 main.go:60: 守护进程启动成功
2025/03/28 04:22:21 main.go:92: 服务器已启动在端口 8080
2025/03/28 04:22:37 main.go:96: 正在关闭服务器...
2025/03/28 04:22:37 main.go:103: 正在停止调度器...
2025/03/28 04:22:37 main.go:105: 调度器已停止
2025/03/28 04:22:37 main.go:112: 服务器已成功关闭
2025/03/28 04:22:47 mysql.go:41: [mysql] Initializing MySQL instance: default
2025/03/28 04:22:47 mysql.go:42: [mysql] DSN: root:root@tcp(127.0.0.1:3306)/rosc?charset=utf8mb4&parseTime=true&loc=Local
2025/03/28 04:22:47 redis.go:125: [redis] Main Redis instance connected successfully
2025/03/28 04:22:47 mysql.go:93: [mysql] MySQL instance default connected successfully
2025/03/28 04:22:47 mysql.go:99: [mysql] Default MySQL instance set successfully
[GIN-debug] [WARNING] Creating an Engine instance with the Logger and Recovery middleware already attached.

[GIN-debug] [WARNING] Running in "debug" mode. Switch to "release" mode in production.
 - using env:	export GIN_MODE=release
 - using code:	gin.SetMode(gin.ReleaseMode)

[GIN-debug] GET    /swagger/*any             --> github.com/swaggo/gin-swagger.CustomWrapHandler.func1 (5 handlers)
[GIN-debug] GET    /assets/*filepath         --> github.com/gin-gonic/gin.(*RouterGroup).createStaticHandler.func1 (5 handlers)
[GIN-debug] HEAD   /assets/*filepath         --> github.com/gin-gonic/gin.(*RouterGroup).createStaticHandler.func1 (5 handlers)
[GIN-debug] GET    /                         --> rosc-system-go/server.LoadTmpl.func1 (5 handlers)
2025/03/28 04:22:47 main.go:52: 守护进程启动失败: daemon: Non-POSIX OS is not supported
2025/03/28 04:22:47 main.go:60: 守护进程启动成功
2025/03/28 04:22:47 main.go:92: 服务器已启动在端口 8080
2025/03/28 04:22:51 main.go:96: 正在关闭服务器...
2025/03/28 04:22:51 main.go:103: 正在停止调度器...
2025/03/28 04:22:51 main.go:105: 调度器已停止
2025/03/28 04:22:51 main.go:112: 服务器已成功关闭
2025/03/28 07:59:30 mysql.go:41: [mysql] Initializing MySQL instance: default
2025/03/28 07:59:30 mysql.go:42: [mysql] DSN: root:root@tcp(127.0.0.1:3306)/rosc?charset=utf8mb4&parseTime=true&loc=Local
2025/03/28 07:59:30 redis.go:125: [redis] Main Redis instance connected successfully
2025/03/28 07:59:30 mysql.go:93: [mysql] MySQL instance default connected successfully
2025/03/28 07:59:30 mysql.go:99: [mysql] Default MySQL instance set successfully
[GIN-debug] [WARNING] Creating an Engine instance with the Logger and Recovery middleware already attached.

[GIN-debug] [WARNING] Running in "debug" mode. Switch to "release" mode in production.
 - using env:	export GIN_MODE=release
 - using code:	gin.SetMode(gin.ReleaseMode)

[GIN-debug] GET    /swagger/*any             --> github.com/swaggo/gin-swagger.CustomWrapHandler.func1 (5 handlers)
[GIN-debug] GET    /assets/*filepath         --> github.com/gin-gonic/gin.(*RouterGroup).createStaticHandler.func1 (5 handlers)
[GIN-debug] HEAD   /assets/*filepath         --> github.com/gin-gonic/gin.(*RouterGroup).createStaticHandler.func1 (5 handlers)
[GIN-debug] GET    /                         --> rosc-system-go/server.LoadTmpl.func1 (5 handlers)
2025/03/28 07:59:30 main.go:52: 守护进程启动失败: daemon: Non-POSIX OS is not supported
2025/03/28 07:59:30 main.go:60: 守护进程启动成功
2025/03/28 07:59:30 main.go:92: 服务器已启动在端口 8080
2025/03/28 08:18:02 main.go:96: 正在关闭服务器...
2025/03/28 08:18:02 main.go:103: 正在停止调度器...
2025/03/28 08:18:02 main.go:105: 调度器已停止
2025/03/28 08:18:02 main.go:112: 服务器已成功关闭
