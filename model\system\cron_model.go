package system

import (
	"rsoc-system-go/middleware/pkg/pagination"
)

// CronEntity 定时任务实体
type CronEntity struct {
	Id          int64  `json:"id" gorm:"primaryKey;autoIncrement;comment:id"`
	TaskName    string `json:"taskName" gorm:"comment:任务名称"`
	TaskGroup   string `json:"taskGroup" gorm:"comment:任务组"`
	CronExpr    string `json:"cronExpr" gorm:"comment:cron表达式"`
	JobClass    string `json:"jobClass" gorm:"comment:任务类"`
	Description string `json:"description" gorm:"comment:描述"`
	Status      int    `json:"status" gorm:"comment:状态 1:启用 2:禁用"`
	CreateTime  string `json:"createTime" gorm:"comment:创建时间"`
	UpdateTime  string `json:"updateTime" gorm:"comment:更新时间"`
}

// TableName 表名
func (c *CronEntity) TableName() string {
	return "admin_cron"
}

type CronRequest struct {
	*pagination.PageRequest
	CronEntity
}
