package jobs

import (
	"context"
	"log"
	"rsoc-system-go/service"
)

const GoogleArticleSyncJobClass = "google_article_sync_job"

func GoogleArticleSyncJobFunc(ctx context.Context) error {
	log.Println("开始执行GoogleArticle同步任务")

	syncService := service.GetGoogleArticleSyncService()
	result, err := syncService.SyncGoogleArticles(ctx)
	if err != nil {
		log.Printf("GoogleArticle同步任务执行失败: %v", err)
		return err
	}

	log.Printf("GoogleArticle同步任务执行完成，总计: %d, 已同步: %d, 失败: %d, 跳过: %d",
		result.Total, result.Synced, result.Failed, result.Skipped)
	return nil
}
