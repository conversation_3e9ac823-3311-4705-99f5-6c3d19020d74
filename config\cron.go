package config

import (
	"log"
	"os"
	"rsoc-system-go/middleware/jobs"
	"rsoc-system-go/middleware/service"
	"rsoc-system-go/store"
)

var cronName = "cron"

func init() {
	go InitCron()
}

func InitCron() {
	if os.Getenv("cron") == "false" {
		store.WaitForConnections()
		return
	}
	log.Printf("[%s] cron init", cronName)

	// 等待所有数据库连接就绪
	log.Printf("[%s] Waiting for database connections...", cronName)
	store.WaitForConnections()
	log.Printf("[%s] All database connections are ready", cronName)

	// 注册定时任务
	cronJob := jobs.Job{}
	cronJob.RegisterJob()

	// 初始化定时任务
	cronService := &service.CronService{}
	if err := cronService.InitCronTasks(); err != nil {
		log.Printf("[%s] Failed to initialize cron tasks: %v", cronName, err)
	}
}
