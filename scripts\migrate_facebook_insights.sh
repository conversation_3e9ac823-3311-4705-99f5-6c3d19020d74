#!/bin/bash

# Facebook Insights 表结构迁移脚本
# 用途：添加扩展字段以支持新的JSON数据格式
# 版本：1.0
# 创建时间：2025-01-19

set -e  # 遇到错误立即退出

# 颜色定义
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# 日志函数
log_info() {
    echo -e "${BLUE}[INFO]${NC} $1"
}

log_success() {
    echo -e "${GREEN}[SUCCESS]${NC} $1"
}

log_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

log_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

# 检查MySQL连接
check_mysql_connection() {
    log_info "检查MySQL连接..."
    
    if [ -z "$MYSQL_URL" ]; then
        log_error "MYSQL_URL 环境变量未设置"
        echo "请设置 MYSQL_URL 环境变量，格式如下："
        echo "export MYSQL_URL='user:password@tcp(host:port)/database?charset=utf8mb4&parseTime=True&loc=Local'"
        exit 1
    fi
    
    # 解析MySQL URL
    MYSQL_PARAMS=$(echo $MYSQL_URL | sed 's/.*@tcp(\([^)]*\))\/\([^?]*\).*/\1 \2/')
    MYSQL_HOST_PORT=$(echo $MYSQL_PARAMS | cut -d' ' -f1)
    MYSQL_DATABASE=$(echo $MYSQL_PARAMS | cut -d' ' -f2)
    MYSQL_HOST=$(echo $MYSQL_HOST_PORT | cut -d':' -f1)
    MYSQL_PORT=$(echo $MYSQL_HOST_PORT | cut -d':' -f2)
    
    # 从URL中提取用户名和密码
    MYSQL_USER=$(echo $MYSQL_URL | sed 's/\([^:]*\):.*/\1/')
    MYSQL_PASSWORD=$(echo $MYSQL_URL | sed 's/[^:]*:\([^@]*\)@.*/\1/')
    
    log_info "连接信息: $MYSQL_HOST:$MYSQL_PORT/$MYSQL_DATABASE"
    
    # 测试连接
    if ! mysql -h"$MYSQL_HOST" -P"$MYSQL_PORT" -u"$MYSQL_USER" -p"$MYSQL_PASSWORD" -e "SELECT 1;" "$MYSQL_DATABASE" >/dev/null 2>&1; then
        log_error "无法连接到MySQL数据库"
        exit 1
    fi
    
    log_success "MySQL连接正常"
}

# 备份表结构
backup_table_structure() {
    log_info "备份当前表结构..."
    
    BACKUP_DIR="./backups"
    mkdir -p "$BACKUP_DIR"
    
    BACKUP_FILE="$BACKUP_DIR/facebook_insights_structure_$(date +%Y%m%d_%H%M%S).sql"
    
    mysqldump -h"$MYSQL_HOST" -P"$MYSQL_PORT" -u"$MYSQL_USER" -p"$MYSQL_PASSWORD" \
        --no-data --routines --triggers "$MYSQL_DATABASE" facebook_insights > "$BACKUP_FILE"
    
    log_success "表结构已备份到: $BACKUP_FILE"
}

# 执行迁移
execute_migration() {
    log_info "执行数据库迁移..."
    
    MIGRATION_FILE="./migrations/001_add_facebook_insights_extended_fields.sql"
    
    if [ ! -f "$MIGRATION_FILE" ]; then
        log_error "迁移文件不存在: $MIGRATION_FILE"
        exit 1
    fi
    
    log_info "执行迁移文件: $MIGRATION_FILE"
    
    if mysql -h"$MYSQL_HOST" -P"$MYSQL_PORT" -u"$MYSQL_USER" -p"$MYSQL_PASSWORD" "$MYSQL_DATABASE" < "$MIGRATION_FILE"; then
        log_success "迁移执行成功"
    else
        log_error "迁移执行失败"
        exit 1
    fi
}

# 验证迁移结果
verify_migration() {
    log_info "验证迁移结果..."
    
    # 检查新字段是否存在
    NEW_FIELDS=("campaign_name" "platform" "country" "hour" "related_links_requests" "related_links_impressions" "related_links_clicks" "related_links_rpm" "ad_requests" "matched_ad_requests" "ad_impressions" "ctr" "ad_ctr" "ad_rpm" "cr" "revenue" "create_time")
    
    for field in "${NEW_FIELDS[@]}"; do
        if mysql -h"$MYSQL_HOST" -P"$MYSQL_PORT" -u"$MYSQL_USER" -p"$MYSQL_PASSWORD" "$MYSQL_DATABASE" \
           -e "SELECT COLUMN_NAME FROM INFORMATION_SCHEMA.COLUMNS WHERE TABLE_SCHEMA='$MYSQL_DATABASE' AND TABLE_NAME='facebook_insights' AND COLUMN_NAME='$field';" | grep -q "$field"; then
            log_success "字段 $field 已成功添加"
        else
            log_error "字段 $field 添加失败"
            exit 1
        fi
    done
    
    log_success "所有新字段验证通过"
}

# 显示使用帮助
show_help() {
    echo "Facebook Insights 表结构迁移脚本"
    echo ""
    echo "用法: $0 [选项]"
    echo ""
    echo "选项:"
    echo "  migrate     执行迁移（默认）"
    echo "  rollback    回滚迁移"
    echo "  verify      仅验证迁移结果"
    echo "  help        显示此帮助信息"
    echo ""
    echo "环境变量:"
    echo "  MYSQL_URL   MySQL连接字符串（必需）"
    echo ""
    echo "示例:"
    echo "  export MYSQL_URL='user:pass@tcp(localhost:3306)/dbname?charset=utf8mb4&parseTime=True&loc=Local'"
    echo "  $0 migrate"
}

# 执行回滚
execute_rollback() {
    log_warning "准备执行回滚操作..."
    log_warning "此操作将删除所有扩展字段中的数据！"
    
    read -p "确认执行回滚？(yes/no): " confirm
    if [ "$confirm" != "yes" ]; then
        log_info "回滚操作已取消"
        exit 0
    fi
    
    ROLLBACK_FILE="./migrations/001_rollback_facebook_insights_extended_fields.sql"
    
    if [ ! -f "$ROLLBACK_FILE" ]; then
        log_error "回滚文件不存在: $ROLLBACK_FILE"
        exit 1
    fi
    
    log_info "执行回滚文件: $ROLLBACK_FILE"
    
    if mysql -h"$MYSQL_HOST" -P"$MYSQL_PORT" -u"$MYSQL_USER" -p"$MYSQL_PASSWORD" "$MYSQL_DATABASE" < "$ROLLBACK_FILE"; then
        log_success "回滚执行成功"
    else
        log_error "回滚执行失败"
        exit 1
    fi
}

# 主函数
main() {
    case "${1:-migrate}" in
        "migrate")
            check_mysql_connection
            backup_table_structure
            execute_migration
            verify_migration
            log_success "迁移完成！"
            ;;
        "rollback")
            check_mysql_connection
            execute_rollback
            log_success "回滚完成！"
            ;;
        "verify")
            check_mysql_connection
            verify_migration
            ;;
        "help"|"-h"|"--help")
            show_help
            ;;
        *)
            log_error "未知选项: $1"
            show_help
            exit 1
            ;;
    esac
}

# 执行主函数
main "$@"
