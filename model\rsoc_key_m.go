package model

import "time"

type RsocKey struct {
	ID       int    `json:"id" gorm:"primaryKey;column:id;autoIncrement"`
	UserID   int    `json:"userId" gorm:"column:userId;type:int(11)"`
	ClientId string `json:"client_id" gorm:"column:client_id;type:varchar(255)"`
	Key      string `json:"key" gorm:"column:key;type:varchar(255)"`
	Status   int    `json:"status" gorm:"column:status;type:int(11)"`
	// 1国内 2海外
	KeyType     int    `json:"key_type" gorm:"column:key_type;type:int(11);comment:1 国内 2海外"`
	Note        string `json:"note" gorm:"column:note;type:text"`
	PostBackUrl string `json:"postback_url" gorm:"column:postback_url;type:varchar(255)"`
	CreateAt    string `json:"create_at" gorm:"column:create_at;type:int(11)"`
	UpdateAt    string `json:"update_at" gorm:"column:update_at;type:int(11)"`
	CreateTime  string `json:"create_time" gorm:"column:create_time;type:datetime"`
	UpdateTime  string `json:"update_time" gorm:"column:update_time;type:datetime"`
}

func (p *RsocKey) TableName() string {
	p.UpdateTime = time.Now().Format(time.DateTime)
	return "rosc_key"
}
