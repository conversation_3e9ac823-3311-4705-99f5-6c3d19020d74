package dao

import (
	"rsoc-system-go/model"

	"gorm.io/gorm"
)

type GoogleArticleDAO struct {
	db *gorm.DB
}

func NewGoogleArticleDAO(db *gorm.DB) *GoogleArticleDAO {
	return &GoogleArticleDAO{db: db}
}

// Create 创建Google文章
func (d *GoogleArticleDAO) Create(article *model.GoogleArticle) error {
	return d.db.Create(article).Error
}

// BatchCreate 批量创建Google文章
func (d *GoogleArticleDAO) BatchCreate(articles []*model.GoogleArticle) error {
	return d.db.Create(articles).Error
}

// GetByID 根据ID获取Google文章
func (d *GoogleArticleDAO) GetByID(id uint) (*model.GoogleArticle, error) {
	var article model.GoogleArticle
	err := d.db.First(&article, id).Error
	if err != nil {
		return nil, err
	}
	return &article, nil
}

// List 获取Google文章列表
func (d *GoogleArticleDAO) List(req *model.GoogleArticleListRequest) ([]model.GoogleArticle, int64, error) {
	var articles []model.GoogleArticle
	var total int64

	query := d.db.Model(&model.GoogleArticle{})

	//if req.Status != 0 {
	query = query.Where("status = ?", req.Status)
	//}
	if req.Key != "" {
		query = query.Where("`key` = ? ", req.Key)
	}
	if req.RsocId != 0 {
		query = query.Where("`rsoc_id` = ? ", req.RsocId)
	}
	if req.TwoDirectoryId != 0 {
		query = query.Where("two_directory_id = ? ", req.TwoDirectoryId)
	}
	if req.CreatedBy != 0 {
		query = query.Where("created_by = ? ", req.CreatedBy)
	}
	if req.Type != 0 {
		query = query.Where("`type` = ? ", req.Type)
	}

	err := query.Count(&total).Error
	if err != nil {
		return nil, 0, err
	}

	if req.Page > 0 && req.PageSize > 0 {
		offset := (req.Page - 1) * req.PageSize
		query = query.Offset(offset).Limit(req.PageSize)
	}

	err = query.Order("created_at DESC").Find(&articles).Error
	if err != nil {
		return nil, 0, err
	}

	return articles, total, nil
}

// UpdateStatus 更新Google文章状态
func (d *GoogleArticleDAO) UpdateStatus(id uint, status int, reviewResult, resultReason string, campaign int) error {
	updates := map[string]interface{}{
		"status":        status,
		"review_result": reviewResult,
		"result_reason": resultReason,
		"campaign":      campaign,
	}
	return d.db.Model(&model.GoogleArticle{}).Where("id = ?", id).Updates(updates).Error
}

// GetByFinalURL 根据最终URL获取Google文章
func (d *GoogleArticleDAO) GetByFinalURL(finalURL string) (*model.GoogleArticle, error) {
	var article model.GoogleArticle
	err := d.db.Where("final_url = ?", finalURL).First(&article).Error
	if err != nil {
		return nil, err
	}
	return &article, nil
}

// GetByCampaignName 根据活动名称获取Google文章
func (d *GoogleArticleDAO) GetByCampaignName(campaignName string) (*model.GoogleArticle, error) {
	var article model.GoogleArticle
	err := d.db.Where("campaign_name = ?", campaignName).First(&article).Error
	if err != nil {
		return nil, err
	}
	return &article, nil
}

// Updates 更新Google文章
func (d *GoogleArticleDAO) Updates(article *model.GoogleArticle) error {
	return d.db.Updates(article).Error
}

// Update 更新Google文章
func (d *GoogleArticleDAO) Update(article *model.GoogleArticle) error {
	return d.db.Model(&model.GoogleArticle{}).Where("id", article.ID).Update("status", 0).Error
}

// GetByCampaignNames 根据多个活动名称批量获取Google文章
func (d *GoogleArticleDAO) GetByCampaignNames(campaignNames []string) (map[string]*model.GoogleArticle, error) {
	var articles []model.GoogleArticle
	err := d.db.Where("campaign_name IN ?", campaignNames).Find(&articles).Error
	if err != nil {
		return nil, err
	}

	// 转换为map便于查找
	result := make(map[string]*model.GoogleArticle)
	for i := range articles {
		result[articles[i].CampaignName] = &articles[i]
	}
	return result, nil
}

// BatchUpdate 批量更新Google文章
func (d *GoogleArticleDAO) BatchUpdate(articles []*model.GoogleArticle) error {
	if len(articles) == 0 {
		return nil
	}
	return d.db.Save(articles).Error
}

// FindByCampaignID 根据广告系列ID查找Google文章
func (d *GoogleArticleDAO) FindByCampaignID(campaignID string) ([]*model.GoogleArticle, error) {
	var articles []*model.GoogleArticle
	err := d.db.Where("campaign_id = ?", campaignID).Find(&articles).Error
	if err != nil {
		return nil, err
	}
	return articles, nil
}

// FindByStatus 根据状态查找Google文章
func (d *GoogleArticleDAO) FindByStatus(status int) ([]*model.GoogleArticle, error) {
	var articles []*model.GoogleArticle
	err := d.db.Where("status = ?", status).Find(&articles).Error
	if err != nil {
		return nil, err
	}
	return articles, nil
}
