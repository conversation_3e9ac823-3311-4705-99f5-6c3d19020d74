package constants

// UAFileCount 定义了每个UA目录下的文件数量
const (
	UA01FileCount = 100000
	UA02FileCount = 84519
	UA08FileCount = 1914
	UA12FileCount = 4748
	UA16FileCount = 49863
	FileCount     = 50000
	UA82FileCount = 16797
)

// UAFileCountMap 提供了通过UA名称查找文件数量的映射
var UAFileCountMap = map[string]int{
	"ua1":  UA01FileCount,
	"ua2":  UA02FileCount,
	"ua3":  FileCount,
	"ua4":  FileCount,
	"ua5":  FileCount,
	"ua6":  FileCount,
	"ua7":  FileCount,
	"ua8":  UA08FileCount,
	"ua9":  FileCount,
	"ua10": FileCount,
	"ua11": FileCount,
	"ua12": UA12FileCount,
	"ua13": FileCount,
	"ua14": FileCount,
	"ua15": FileCount,
	"ua16": UA16FileCount,
	"ua17": FileCount,
	"ua18": FileCount,
	"ua19": FileCount,
	"ua20": FileCount,
	"ua21": FileCount,
	"ua22": FileCount,
	"ua23": FileCount,
	"ua24": FileCount,
	"ua25": FileCount,
	"ua26": FileCount,
	"ua27": FileCount,
	"ua28": FileCount,
	"ua29": FileCount,
	"ua30": FileCount,
	"ua31": FileCount,
	"ua32": FileCount,
	"ua33": FileCount,
	"ua34": FileCount,
	"ua35": FileCount,
	"ua36": FileCount,
	"ua37": FileCount,
	"ua38": FileCount,
	"ua39": FileCount,
	"ua40": FileCount,
	"ua41": FileCount,
	"ua42": FileCount,
	"ua43": FileCount,
	"ua44": FileCount,
	"ua45": FileCount,
	"ua46": FileCount,
	"ua47": FileCount,
	"ua48": FileCount,
	"ua49": FileCount,
	"ua50": FileCount,
	"ua51": FileCount,
	"ua52": FileCount,
	"ua53": FileCount,
	"ua54": FileCount,
	"ua55": FileCount,
	"ua56": FileCount,
	"ua57": FileCount,
	"ua58": FileCount,
	"ua59": FileCount,
	"ua60": FileCount,
	"ua61": FileCount,
	"ua62": FileCount,
	"ua63": FileCount,
	"ua64": FileCount,
	"ua65": FileCount,
	"ua66": FileCount,
	"ua67": FileCount,
	"ua68": FileCount,
	"ua69": FileCount,
	"ua70": FileCount,
	"ua71": FileCount,
	"ua72": FileCount,
	"ua73": FileCount,
	"ua74": FileCount,
	"ua75": FileCount,
	"ua76": FileCount,
	"ua77": FileCount,
	"ua78": FileCount,
	"ua79": FileCount,
	"ua80": FileCount,
	"ua81": FileCount,
	"ua82": UA82FileCount,
}
