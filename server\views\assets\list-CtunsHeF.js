import{_ as ve,u as xe,o as Se,r as _,a as we,M as s,j as q,c as z,b as c,d as a,w as o,B as Ce,e as n,k as G,f as O,h as k,n as R,F as $e,g as Ie,p as Oe,m as Te,s as S,D as Pe,i as N}from"./index-DlVegDiC.js";import{P as De}from"./PlusOutlined-Cg2o2XQN.js";import{S as Ye}from"./index-CSU5nP3m.js";import{D as ze}from"./dayjs-BF6WblrD.js";import{_ as Ne,a as Ue,S as Fe}from"./index-C0YopvWv.js";import{_ as je}from"./index-1uCBjWky.js";import{_ as Me}from"./index-BrFZluVG.js";import{_ as Le}from"./index-B8PO_1fg.js";const Je={class:"main"},Ae={class:"filter"},Be={key:0,style:{color:"#1890ff"}},Ve={key:1,style:{color:"#f50"}},qe={class:"info_review"},Ge={class:"filter"},Re={class:"filter_box"},Ee={class:"filter_item"},Ke={style:{display:"flex","align-items":"center"}},He={__name:"list",setup(Qe){const U="",h="",p=xe(),F=we();Se(()=>{b()});const w=_({data:[],loading:!1}),C=_({total:0,current:1,pageSize:10,position:["bottomCenter"],showSizeChanger:!0,pageSizeOptions:["10","20","30"],showTotal:e=>`共 ${e} 项`}),Z=e=>{console.log(e),fetch(`${h}/replay/updateStatus`,{method:"POST",body:JSON.stringify({id:e.id,status:2}),headers:{token:p.token}}).then(t=>t.json()).then(t=>{console.log(t),t.code==1?(S.success("停止成功"),b()):s.error({title:t.msg})}).catch(t=>{s.error({title:"服务器错误",content:`${t}`})})},ee=e=>{console.log(e),fetch(`${h}/replay/line?date=${N(e).format("YYYY-MM-DD")}`,{method:"GET",headers:{token:p.token}}).then(t=>t.json()).then(t=>{console.log(t),t.code==1&&(t.data==null?(S.error("没有数据"),i.replay_line=""):t.data.length==0&&(S.error("没有数据"),i.replay_line=""))})},b=()=>{w.loading=!0,fetch(`${h}/replay/list`,{method:"POST",body:JSON.stringify({page:C.current,limit:C.pageSize}),headers:{token:p.token}}).then(e=>e.json()).then(e=>{console.log(e),e.code==1?(w.data=e.data.data,C.total=Number(e.data.total)):e.code==3e3?(p.$patch({token:!1}),F.push("/login"),s.error({title:e.msg})):(w.data=[],s.error({title:e.msg})),w.loading=!1}).catch(e=>{s.error({title:"服务器错误",content:`${e}`})})},te=e=>{C.current=e.current,C.pageSize=e.pageSize,b()},E=q(),i=_({open:!1,loading:!1,result:{text:"",show:!1},task_id:"",task_data_id:"",repeat:"1",name:"",replay_number:"",replay_line:"",task_create_date:"",note:"",proxy_id:""}),f=_({proxy_list:[],task_list:[],task_loading:!1,proxy_loading:!1}),ae=()=>{i.open=!0,H()},ne=()=>{var e;i.loading=!0,fetch(`${h}/replay/replay`,{method:"POST",body:JSON.stringify({task_id:i.task_id,task_data_id:(e=f.task_list.find(t=>t.task_id===i.task_id))==null?void 0:e.id,repeat:i.repeat,name:i.name,task_number:i.replay_number,task_date:N(i.replay_line).format("YYYY-MM-DD"),task_create_date:N(i.task_create_date).format("YYYY-MM-DD"),note:i.note,proxy_set_id:i.proxy_id}),headers:{token:p.token}}).then(t=>t.json()).then(t=>{console.log(t),t.code==1?(S.success("添加成功"),b(),E.value.resetFields()):s.error({title:t.msg}),i.open=!1,i.loading=!1}).catch(t=>{s.error({title:"服务器错误",content:`${t}`})})},u=_({open:!1,values:{},task_id:0}),r=_({open:!1,values:{},task_id:0}),j=q(),T=q(),d=_({open:!1,values:{},task_id:0,loading:!1,proxy_id:""}),oe=()=>{r.loading=!0,fetch(`${h}/replay/updateNumber`,{method:"POST",body:JSON.stringify({id:r.values.id,task_number:r.number}),headers:{token:p.token}}).then(e=>e.json()).then(e=>{e.code==1?(S.success("更新成功"),b(),j.value.resetFields(),r.loading=!1,r.open=!1):(s.error({title:e.msg}),r.loading=!1,r.open=!1,j.value.resetFields())}).catch(e=>{s.error({title:"服务器错误",content:`${e}`})})},le=e=>{console.log(e),r.open=!0,r.values=e,r.task_id=e.task_id,r.task_number=e.task_number,j.value.setFieldsValue({task_id:e.task_id})},ie=e=>{console.log(e),H(),d.open=!0,d.values=e,d.task_id=e.task_id,d.proxy_id=e.proxy_set_id,Pe(()=>{T.value&&T.value.setFieldsValue({proxy_id:e.proxy_set_id})})},se=()=>{console.log(d.values),d.loading=!0,fetch(`${h}/replay/updateProxy`,{method:"POST",body:JSON.stringify({id:d.values.id,proxy_set_id:d.proxy_id}),headers:{token:p.token}}).then(e=>e.json()).then(e=>{console.log(e),e.code==1?(S.success("更新成功"),b(),T.value.resetFields(),d.loading=!1,d.open=!1):(s.error({title:e.msg}),d.loading=!1,d.open=!1,T.value.resetFields())}).catch(e=>{s.error({title:"服务器错误",content:`${e}`})})},de=e=>{u.open=!0,console.log(e),u.values=e,v.current=1,M()},$=_({data:[],loading:!1}),v=_({total:0,current:1,pageSize:10,position:["bottomCenter"],showSizeChanger:!0,pageSizeOptions:["10","20","30"],showTotal:e=>`共 ${e} 项`}),re=()=>{console.log(N(u.values.replayDate).format("YYYY-MM-DD")),M()},M=()=>{$.loading=!0,fetch(`${U}/Task/autoTask`,{method:"POST",body:JSON.stringify({date:N(u.values.replayDate).format("YYYY-MM-DD"),task_id:u.values.task_id,page:v.current,limit:v.pageSize,directoryStatus:1}),headers:{token:p.token}}).then(e=>e.json()).then(e=>{console.log(e),e.code==1?($.data=e.data.data,v.total=Number(e.data.total)):e.code==3e3?(p.$patch({token:!1}),F.push("/login"),s.error({title:e.msg})):($.data=[],s.error({title:e.msg})),$.loading=!1}).catch(e=>{s.error({title:"服务器错误",content:`${e}`})})},pe=e=>{v.current=e.current,v.pageSize=e.pageSize,M()},P=_({open:!1,click_id:0}),ue=e=>{P.open=!0,console.log(e),P.click_id=e.click_id,x.current=1,K()},I=_({data:[],loading:!1}),x=_({total:0,current:1,pageSize:10,position:["bottomCenter"],showSizeChanger:!0,pageSizeOptions:["10","20","30"],showTotal:e=>`共 ${e} 项`}),K=()=>{I.loading=!0,fetch(`${U}/Task/getAutoTaskLog`,{method:"POST",body:JSON.stringify({click_id:P.click_id,page:x.current,limit:x.pageSize,directoryStatus:1}),headers:{token:p.token}}).then(e=>e.json()).then(e=>{console.log(e),e.code==1?(I.data=e.data.data,x.total=Number(e.data.total)):e.code==3e3?(p.$patch({token:!1}),F.push("/login"),s.error({title:e.msg})):(I.data=[],s.error({title:e.msg})),I.loading=!1}).catch(e=>{s.error({title:"服务器错误",content:`${e}`})})},_e=e=>{x.current=e.current,x.pageSize=e.pageSize,K()},ce=e=>{fetch(`${h}/replay/del`,{method:"POST",body:JSON.stringify({id:e.id,status:2}),headers:{token:p.token}}).then(t=>t.json()).then(t=>{console.log(t),t.code==1?(S.success(t.msg),b()):s.error({title:t.msg})}).catch(t=>{s.error({title:"服务器错误",content:`${t}`})})},H=()=>{fetch(`${U}/proxy/list`,{method:"POST",body:JSON.stringify({directoryStatus:1}),headers:{token:p.token}}).then(e=>e.json()).then(e=>{console.log(e),f.proxy_list=e.data}).catch(e=>{s.error({title:"服务器错误",content:`${e}`})})},fe=()=>{f.task_loading=!0,fetch(`${U}/Task/list`,{method:"POST",body:JSON.stringify({page:1,limit:1e3,status:1}),headers:{token:p.token}}).then(e=>e.json()).then(e=>{console.log(e),e.code==1?f.task_list=e.data.data:e.code==3e3?(p.$patch({token:!1}),F.push("/login"),s.error({title:e.msg})):(f.task_list=[],s.error({title:e.msg})),f.task_loading=!1}).catch(e=>{s.error({title:"服务器错误",content:`${e}`}),f.task_loading=!1})},ge=[{title:"ID",dataIndex:"id",key:"id",align:"center"},{title:"任务id",dataIndex:"task_id",key:"task_id",align:"center"},{title:"任务数据id",dataIndex:"task_data_id",key:"task_data_id",align:"center"},{title:"代理id",dataIndex:"proxy_set_id",key:"proxy_set_id",align:"center"},{title:"数量",dataIndex:"task_number",key:"task_number",align:"center"},{title:"状态",dataIndex:"status",key:"status",align:"center"},{title:"重放曲线",dataIndex:"task_date",key:"task_date",align:"center"},{title:"备注",dataIndex:"note",key:"note",align:"center"},{title:"生成日期",dataIndex:"task_create_date",key:"task_create_date",align:"center"},{title:"操作",key:"action",align:"center"}],me=[{title:"ID",dataIndex:"id",key:"id",align:"center",width:70,fixed:"left"},{title:"日期",dataIndex:"add_time",key:"add_time",align:"center",width:150},{title:"链接",dataIndex:"traffic_url",key:"traffic_url",align:"center",width:200,ellipsis:!0},{title:"浏览器参数",dataIndex:"viewport",key:"viewport",align:"center",width:200,ellipsis:!0},{title:"UserAgent",dataIndex:"useragent",key:"useragent",align:"center",width:200,ellipsis:!0},{title:"语言",dataIndex:"lang",key:"lang",align:"center",width:100,ellipsis:!0},{title:"IP",dataIndex:"user_ip",key:"user_ip",align:"center",width:200,ellipsis:!0},{title:"操作",key:"action",align:"center",fixed:"right",width:80}],ke=[{title:"ID",dataIndex:"id",key:"id",align:"center",width:70,fixed:"left"},{title:"日期",dataIndex:"add_time",key:"add_time",align:"center",width:150},{title:"信息",dataIndex:"note",key:"note",align:"center",width:200,ellipsis:!0},{title:"详情",dataIndex:"info",key:"info",align:"center",width:200,ellipsis:!0}];return(e,t)=>{const ye=De,g=Ce,Q=Me,L=je,J=Ye,m=Ie,W=Le,X=ze,he=Oe,A=$e,D=s,B=Fe,V=Ue,be=Ne;return O(),z(G,null,[c("div",Je,[c("div",null,[c("div",Ae,[t[14]||(t[14]=c("div",{class:"filter_item"},null,-1)),c("div",null,[a(g,{type:"primary",onClick:ae},{icon:o(()=>[a(ye)]),default:o(()=>[t[13]||(t[13]=k(" 添加任务 "))]),_:1})])])]),a(L,{columns:ge,"data-source":n(w).data,rowKey:"id",pagination:n(C),loading:n(w).loading,onChange:te,bordered:""},{bodyCell:o(({column:l,record:y})=>[l.key==="status"?(O(),z(G,{key:0},[y.status==1?(O(),z("p",Be,"启用")):(O(),z("p",Ve,"禁用"))],64)):R("",!0),l.key==="action"?(O(),z(G,{key:1},[a(Q,{title:"停止任务？",onConfirm:Y=>Z(y)},{default:o(()=>[a(g,{type:"link",danger:""},{default:o(()=>t[15]||(t[15]=[k("停止")])),_:1})]),_:2},1032,["onConfirm"]),a(g,{type:"link",onClick:Y=>le(y)},{default:o(()=>t[16]||(t[16]=[k("更新数量")])),_:2},1032,["onClick"]),a(g,{type:"link",onClick:Y=>ie(y)},{default:o(()=>t[17]||(t[17]=[k("更新代理")])),_:2},1032,["onClick"]),a(g,{type:"link",onClick:Y=>de(y)},{default:o(()=>t[18]||(t[18]=[k("任务详情")])),_:2},1032,["onClick"]),a(Q,{title:"确认删除？",onConfirm:Y=>ce(y)},{default:o(()=>[a(g,{type:"link",danger:""},{default:o(()=>t[19]||(t[19]=[k("删除")])),_:1})]),_:2},1032,["onConfirm"])],64)):R("",!0)]),_:1},8,["data-source","pagination","loading"])]),a(D,{open:n(i).open,"onUpdate:open":t[5]||(t[5]=l=>n(i).open=l),title:"添加重放",footer:null,maskClosable:!1,width:600},{default:o(()=>[t[21]||(t[21]=c("div",{style:{height:"20px"}},null,-1)),a(A,{ref_key:"add_form",ref:E,model:n(i),onFinish:ne,"label-col":{span:6},"wrapper-col":{span:16}},{default:o(()=>[a(m,{label:"选择任务",name:"task_id",rules:[{required:!0,message:"请选择任务"}]},{default:o(()=>[a(J,{value:n(i).task_id,"onUpdate:value":t[0]||(t[0]=l=>n(i).task_id=l),placeholder:"请选择任务",options:n(f).task_list,"field-names":{label:"id",value:"task_id"},loading:n(f).task_loading,onFocus:fe},null,8,["value","options","loading"])]),_:1}),a(m,{label:"代理",name:"proxy_id",rules:[{required:!0,message:"请选择代理"}]},{default:o(()=>[a(J,{value:n(i).proxy_id,"onUpdate:value":t[1]||(t[1]=l=>n(i).proxy_id=l),placeholder:"请选择代理",options:n(f).proxy_list,"field-names":{label:"label",value:"id"},onChange:e.handleProxyChange},null,8,["value","options","onChange"])]),_:1}),a(m,{label:"重放数量",name:"replay_number",rules:[{required:!0,message:"请输入重放数量"}]},{default:o(()=>[a(W,{value:n(i).replay_number,"onUpdate:value":t[2]||(t[2]=l=>n(i).replay_number=l),placeholder:"请输入重放数量"},null,8,["value"])]),_:1}),a(m,{label:"重放曲线",name:"replay_line",rules:[{required:!0,message:"请选择重放曲线"}]},{default:o(()=>[a(X,{value:n(i).replay_line,"onUpdate:value":t[3]||(t[3]=l=>n(i).replay_line=l),onChange:ee,placeholder:"请选择重放曲线"},null,8,["value"])]),_:1}),a(m,{label:"备注",name:"note"},{default:o(()=>[a(he,{value:n(i).note,"onUpdate:value":t[4]||(t[4]=l=>n(i).note=l),placeholder:"备注",rows:2},null,8,["value"])]),_:1}),a(m,{"wrapper-col":{offset:6,span:16}},{default:o(()=>[a(g,{type:"primary","html-type":"submit",loading:n(i).loading},{default:o(()=>t[20]||(t[20]=[k(" 提交 ")])),_:1},8,["loading"])]),_:1})]),_:1},8,["model"])]),_:1},8,["open"]),a(D,{open:n(r).open,"onUpdate:open":t[7]||(t[7]=l=>n(r).open=l),title:"更新数量",footer:null,maskClosable:!1},{default:o(()=>[a(A,{ref_key:"update_number_form",ref:j,model:n(r),onFinish:oe},{default:o(()=>[a(m,{label:"数量",name:"number"},{default:o(()=>[a(W,{value:n(r).number,"onUpdate:value":t[6]||(t[6]=l=>n(r).number=l),placeholder:"请输入数量"},null,8,["value"])]),_:1}),a(m,{"wrapper-col":{offset:6,span:16}},{default:o(()=>[a(g,{type:"primary","html-type":"submit",loading:n(r).loading},{default:o(()=>t[22]||(t[22]=[k(" 提交 ")])),_:1},8,["loading"])]),_:1})]),_:1},8,["model"])]),_:1},8,["open"]),a(D,{open:n(d).open,"onUpdate:open":t[9]||(t[9]=l=>n(d).open=l),title:"更新代理",footer:null,maskClosable:!1},{default:o(()=>[a(A,{ref_key:"update_proxy_form",ref:T,model:n(d),onFinish:se},{default:o(()=>[a(m,{label:"代理",name:"proxy_id",rules:[{required:!0,message:"请选择代理"}]},{default:o(()=>[a(J,{value:n(d).proxy_id,"onUpdate:value":t[8]||(t[8]=l=>n(d).proxy_id=l),placeholder:"请选择代理",options:n(f).proxy_list,"field-names":{label:"label",value:"id"}},null,8,["value","options"])]),_:1}),a(m,{"wrapper-col":{offset:6,span:16}},{default:o(()=>[a(g,{type:"primary","html-type":"submit",loading:n(d).loading},{default:o(()=>t[23]||(t[23]=[k(" 提交 ")])),_:1},8,["loading"])]),_:1})]),_:1},8,["model"])]),_:1},8,["open"]),a(D,{open:n(u).open,"onUpdate:open":t[11]||(t[11]=l=>n(u).open=l),title:"任务详情",footer:null,maskClosable:!1,width:900},{default:o(()=>[c("div",qe,[a(be,null,{default:o(()=>[a(V,{span:4},{default:o(()=>[a(B,{title:"任务总数",value:n(u).values.all_num},null,8,["value"])]),_:1}),a(V,{span:4},{default:o(()=>[a(B,{title:"打开数量",value:n(u).values.all_view},null,8,["value"])]),_:1}),a(V,{span:4},{default:o(()=>[a(B,{title:"点击数量",value:n(u).values.all_click1},null,8,["value"])]),_:1})]),_:1})]),c("div",Ge,[c("div",Re,[c("div",Ee,[t[24]||(t[24]=c("p",null,"选择日期：",-1)),c("div",Ke,[a(X,{onChange:re,value:n(u).values.replayDate,"onUpdate:value":t[10]||(t[10]=l=>n(u).values.replayDate=l)},null,8,["value"])])])])]),a(L,{columns:me,"data-source":n($).data,rowKey:"id",pagination:n(v),loading:n($).loading,onChange:pe,bordered:"",scroll:{x:1500,y:400},size:"small"},{bodyCell:o(({column:l,record:y})=>[l.key==="action"?(O(),Te(g,{key:0,type:"link",onClick:Y=>ue(y)},{default:o(()=>t[25]||(t[25]=[k(" 查看日志 ")])),_:2},1032,["onClick"])):R("",!0)]),_:1},8,["data-source","pagination","loading"])]),_:1},8,["open"]),a(D,{open:n(P).open,"onUpdate:open":t[12]||(t[12]=l=>n(P).open=l),title:"任务日志",footer:null,maskClosable:!1,width:900},{default:o(()=>[a(L,{columns:ke,"data-source":n(I).data,rowKey:"id",pagination:n(x),loading:n(I).loading,onChange:_e,bordered:"",size:"small"},null,8,["data-source","pagination","loading"])]),_:1},8,["open"])],64)}}},lt=ve(He,[["__scopeId","data-v-73ba7dca"]]);export{lt as default};
