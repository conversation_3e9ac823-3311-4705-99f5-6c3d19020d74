import{_ as ke,u as ye,o as ve,r as p,a as be,M as s,j as xe,c as F,b as g,d as n,w as i,B as Se,e as o,k as B,f,h as T,m as b,n as y,aw as we,t as Y,q as G,v as M,F as Ie,g as $e,I as Ce,p as Ue,s as R}from"./index-DlVegDiC.js";import{P as Pe}from"./PlusOutlined-Cg2o2XQN.js";import{R as Te}from"./index-CCw7iZyQ.js";import{P as Oe}from"./Paragraph-DEgIVM3w.js";import{S as ze}from"./index-CSU5nP3m.js";import{_ as Ne,a as Le,S as Ee}from"./index-C0YopvWv.js";import{_ as je}from"./index-1uCBjWky.js";import{_ as qe}from"./index-B8PO_1fg.js";import{_ as <PERSON>}from"./index-D_v6jNwB.js";const Je={class:"main"},Ae={class:"filter"},Fe={style:{"word-break":"break-all"}},Be={class:"info_review"},Ye={__name:"task",setup(Ge){const m="",u=ye(),N=be();ve(()=>{O()});const x=p({data:[],loading:!1}),C=p({total:0,current:1,pageSize:10,position:["bottomCenter"],showSizeChanger:!0,pageSizeOptions:["10","20","30"],showTotal:e=>`共 ${e} 项`}),O=()=>{x.loading=!0,fetch(`${m}/Yahoo1/taskList`,{method:"POST",body:JSON.stringify({page:C.current,limit:C.pageSize,directoryStatus:1}),headers:{token:u.token}}).then(e=>e.json()).then(e=>{console.log(e),e.code==1?x.data=e.data.data:e.code==3e3?(u.$patch({token:!1}),N.push("/login"),s.error({title:e.msg})):(x.data=[],s.error({title:e.msg})),x.loading=!1}).catch(e=>{s.error({title:"服务器错误",content:`${e}`})})},V=e=>{C.current=e.current,C.pageSize=e.pageSize,O()},D=xe(),a=p({open:!1,loading:!1,result:{text:"",show:!1},main_directory:[],main_directory_options:[],fbId:void 0,yahoo_account:{},yahoo_url:{},accountId:[],traffic:void 0,traffic_url:void 0,fb_pixel_id:void 0,fb_pixel_api_token:void 0,platform:void 0,platform_id:void 0,hold_rate:void 0,seach_rate:void 0,click1_rate:void 0,click2_rate:void 0,click3_rate:void 0,proxy_id:void 0,proxy_name:void 0,country:"",sedo_task:"",ref:"https://m.facebook.com",note:"",is_global:!1}),k=p({main_list:[],ads_list:[],ads_url_list:[],sedo_list:[],pixel_list:[],proxy_list:[],fb_ads_list:[]}),K=()=>{a.open=!0,Q(),X(),te()},H=(e,t)=>{console.log(t),a.main_directory_options=t},Q=()=>{fetch(`${m}/MainDirectory/list`,{method:"POST",body:JSON.stringify({directoryStatus:1,page:1,limit:200}),headers:{token:u.token}}).then(e=>e.json()).then(e=>{console.log(e),k.main_list=e.data.data.map(t=>(t.isLeaf=!1,t))}).catch(e=>{s.error({title:"服务器错误",content:`${e}`})})},W=e=>{const t=e[e.length-1];t.loading=!0,console.log(t),fetch(`${m}/TwoDirectory/list`,{method:"POST",body:JSON.stringify({mianId:t.id,directoryStatus:1,page:1,limit:200}),headers:{token:u.token}}).then(d=>d.json()).then(d=>{console.log(d),t.loading=!1,t.children=d.data.data,k.main_list=[...k.main_list]}).catch(d=>{s.error({title:"服务器错误",content:`${d}`})})},X=()=>{fetch(`${m}/Yahoo1/list`,{method:"POST",headers:{token:u.token}}).then(e=>e.json()).then(e=>{console.log(e),k.ads_list=e.data}).catch(e=>{s.error({title:"服务器错误",content:`${e}`})})},Z=(e,t)=>{console.log(t),a.yahoo_account=t},ee=(e,t)=>{console.log(t),a.yahoo_url=t},te=()=>{fetch(`${m}/Yahoo1/urlList`,{method:"POST",body:JSON.stringify({page:1,limit:200}),headers:{token:u.token}}).then(e=>e.json()).then(e=>{console.log(e),k.ads_url_list=e.data}).catch(e=>{s.error({title:"服务器错误",content:`${e}`})})},ae=()=>{if(!a.main_directory[1])return s.error({title:"请选择关键词二级目录"}),!1;a.loading=!0,fetch(`${m}/Yahoo1/addTask`,{method:"POST",body:JSON.stringify({two_directory:a.main_directory_options[1].name,two_directory_id:a.main_directory[1],yahoo_pubid:a.yahoo_account.pubid,yahoo_channel:a.yahoo_account.channel,yahoo_id:a.yahoo_account.id,yahoo_url:a.yahoo_url.url,sedo_task:a.sedo_task,seach_rate:a.seach_rate,click1_rate:a.click1_rate,click2_rate:a.click2_rate,click3_rate:a.click3_rate,note:a.note}),headers:{token:u.token}}).then(e=>e.json()).then(e=>{console.log(e),e.code==1?(R.success("添加成功"),O(),D.value.resetFields(),a.open=!1):s.error({title:e.msg}),a.loading=!1}).catch(e=>{s.error({title:"服务器错误",content:`${e}`})})},S=p({open:!1,values:{},task_id:0}),w=p({data:[],loading:!1}),I=p({total:0,current:1,pageSize:10,position:["bottomCenter"],showSizeChanger:!0,pageSizeOptions:["10","20","30"],showTotal:e=>`共 ${e} 项`}),oe=()=>{w.loading=!0,fetch(`${m}/Task/autoTask`,{method:"POST",body:JSON.stringify({task_id:S.values.task_id,page:I.current,limit:I.pageSize,directoryStatus:1}),headers:{token:u.token}}).then(e=>e.json()).then(e=>{console.log(e),e.code==1?(w.data=e.data.data,I.total=Number(e.data.total)):e.code==3e3?(u.$patch({token:!1}),N.push("/login"),s.error({title:e.msg})):(w.data=[],s.error({title:e.msg})),w.loading=!1}).catch(e=>{s.error({title:"服务器错误",content:`${e}`})})},ne=e=>{I.current=e.current,I.pageSize=e.pageSize,oe()},U=p({open:!1,click_id:0}),le=e=>{U.open=!0,console.log(e),U.click_id=e.click_id,v.current=1,J()},$=p({data:[],loading:!1}),v=p({total:0,current:1,pageSize:10,position:["bottomCenter"],showSizeChanger:!0,pageSizeOptions:["10","20","30"],showTotal:e=>`共 ${e} 项`}),J=()=>{$.loading=!0,fetch(`${m}/Task/getAutoTaskLog`,{method:"POST",body:JSON.stringify({click_id:U.click_id,page:v.current,limit:v.pageSize,directoryStatus:1}),headers:{token:u.token}}).then(e=>e.json()).then(e=>{console.log(e),e.code==1?($.data=e.data.data,v.total=Number(e.data.total)):e.code==3e3?(u.$patch({token:!1}),N.push("/login"),s.error({title:e.msg})):($.data=[],s.error({title:e.msg})),$.loading=!1}).catch(e=>{s.error({title:"服务器错误",content:`${e}`})})},ie=e=>{v.current=e.current,v.pageSize=e.pageSize,J()},P=(e,t)=>{console.log(e),console.log(t),fetch(`${m}/Yahoo1/updateTask`,{method:"POST",body:JSON.stringify({id:t.id,[e]:t[e]}),headers:{token:u.token}}).then(d=>d.json()).then(d=>{console.log(d),d.code==1?(R.success(d.msg),O()):s.error({title:d.msg})}).catch(d=>{s.error({title:"服务器错误",content:`${d}`})})},se=[{title:"ID",dataIndex:"id",key:"id",align:"center"},{title:"二级目录",dataIndex:"two_directory",key:"two_directory",align:"center"},{title:"yahoo_pubid",dataIndex:"yahoo_pubid",key:"yahoo_pubid",align:"center"},{title:"yahoo_channel",dataIndex:"yahoo_channel",key:"yahoo_channel",align:"center"},{title:"yahoo_id",dataIndex:"yahoo_id",key:"yahoo_id",align:"center"},{title:"yahoo_url",dataIndex:"yahoo_url",key:"yahoo_url",align:"center"},{title:"搜索比例",dataIndex:"seach_rate",key:"seach_rate",align:"center"},{title:"点广告概率",dataIndex:"click1_rate",key:"click1_rate",align:"center"},{title:"二次点广告概率",dataIndex:"click2_rate",key:"click2_rate",align:"center"},{title:"三次点广告概率",dataIndex:"click3_rate",key:"click3_rate",align:"center"},{title:"操作",key:"action",align:"center"}],re=[{title:"ID",dataIndex:"id",key:"id",align:"center",width:70,fixed:"left"},{title:"日期",dataIndex:"add_time",key:"add_time",align:"center",width:150},{title:"链接",dataIndex:"traffic_url",key:"traffic_url",align:"center",width:200,ellipsis:!0},{title:"浏览器参数",dataIndex:"viewport",key:"viewport",align:"center",width:200,ellipsis:!0},{title:"UserAgent",dataIndex:"useragent",key:"useragent",align:"center",width:200,ellipsis:!0},{title:"语言",dataIndex:"lang",key:"lang",align:"center",width:100,ellipsis:!0},{title:"IP",dataIndex:"user_ip",key:"user_ip",align:"center",width:200,ellipsis:!0},{title:"操作",key:"action",align:"center",fixed:"right",width:80}],de=[{title:"ID",dataIndex:"id",key:"id",align:"center",width:70,fixed:"left"},{title:"日期",dataIndex:"add_time",key:"add_time",align:"center",width:150},{title:"信息",dataIndex:"note",key:"note",align:"center",width:200,ellipsis:!0},{title:"详情",dataIndex:"info",key:"info",align:"center",width:200,ellipsis:!0}];return(e,t)=>{const d=Pe,z=Se,h=qe,ce=we,L=je,ue=Te,_e=Oe,pe=De,_=$e,A=ze,ge=Ce,me=Ue,he=Ie,E=s,j=Ee,q=Le,fe=Ne;return f(),F(B,null,[g("div",Je,[g("div",null,[g("div",Ae,[t[13]||(t[13]=g("div",{class:"filter_item"},null,-1)),g("div",null,[n(z,{type:"primary",onClick:K},{icon:i(()=>[n(d)]),default:i(()=>[t[12]||(t[12]=T(" 添加雅虎任务 "))]),_:1})])])]),n(L,{columns:se,"data-source":o(x).data,rowKey:"id",pagination:o(C),loading:o(x).loading,onChange:V,bordered:""},{bodyCell:i(({column:l,record:r})=>[l.key==="hold_rate"?(f(),b(h,{key:0,value:r.hold_rate,"onUpdate:value":c=>r.hold_rate=c,min:1,max:100,onPressEnter:c=>P("hold_rate",r)},null,8,["value","onUpdate:value","onPressEnter"])):y("",!0),l.key==="seach_rate"?(f(),b(h,{key:1,value:r.seach_rate,"onUpdate:value":c=>r.seach_rate=c,min:1,max:100,onPressEnter:c=>P("seach_rate",r)},null,8,["value","onUpdate:value","onPressEnter"])):y("",!0),l.key==="click1_rate"?(f(),b(h,{key:2,value:r.click1_rate,"onUpdate:value":c=>r.click1_rate=c,min:1,max:100,onPressEnter:c=>P("click1_rate",r)},null,8,["value","onUpdate:value","onPressEnter"])):y("",!0),l.key==="click2_rate"?(f(),b(h,{key:3,value:r.click2_rate,"onUpdate:value":c=>r.click2_rate=c,min:1,max:100,onPressEnter:c=>P("click2_rate",r)},null,8,["value","onUpdate:value","onPressEnter"])):y("",!0),l.key==="click3_rate"?(f(),b(h,{key:4,value:r.click3_rate,"onUpdate:value":c=>r.click3_rate=c,min:1,max:100,onPressEnter:c=>P("click3_rate",r)},null,8,["value","onUpdate:value","onPressEnter"])):y("",!0),l.key==="put_url"?(f(),b(ce,{key:5},{title:i(()=>[g("div",Fe,Y(r.put_url),1)]),default:i(()=>[n(z,{type:"link"},{default:i(()=>t[14]||(t[14]=[T("查看投放链接")])),_:1})]),_:2},1024)):y("",!0),l.key==="action"?(f(),F(B,{key:6},[],64)):y("",!0)]),_:1},8,["data-source","pagination","loading"])]),n(E,{open:o(a).open,"onUpdate:open":t[9]||(t[9]=l=>o(a).open=l),title:"添加雅虎任务",footer:null,maskClosable:!1,width:600},{default:i(()=>[t[16]||(t[16]=g("div",{style:{height:"20px"}},null,-1)),G(g("div",null,[n(ue,{status:"success",title:"生成的广告链接",class:"result_icon"}),n(_e,{copyable:"",class:"result_text"},{default:i(()=>[T(Y(o(a).result.text),1)]),_:1})],512),[[M,o(a).result.show]]),G(g("div",null,[n(he,{ref_key:"add_form",ref:D,model:o(a),onFinish:ae,"label-col":{span:6},"wrapper-col":{span:16}},{default:i(()=>[n(_,{label:"关键词",name:"main_directory",rules:[{required:!0,message:"请选择关键词"}]},{default:i(()=>[n(pe,{value:o(a).main_directory,"onUpdate:value":t[0]||(t[0]=l=>o(a).main_directory=l),options:o(k).main_list,"load-data":W,placeholder:"请选择关键词","change-on-select":"","field-names":{label:"name",value:"id"},onChange:H},null,8,["value","options"])]),_:1}),n(_,{label:"雅虎账户",name:"fbId",rules:[{required:!0,message:"请选择雅虎账户"}]},{default:i(()=>[n(A,{value:o(a).fbId,"onUpdate:value":t[1]||(t[1]=l=>o(a).fbId=l),placeholder:"请选择雅虎账户",options:o(k).ads_list,"field-names":{label:"name",value:"id"},onChange:Z},null,8,["value","options"])]),_:1}),n(_,{label:"雅虎域名",name:"traffic_url",rules:[{required:!0,message:"请选择雅虎域名"}]},{default:i(()=>[n(A,{value:o(a).traffic_url,"onUpdate:value":t[2]||(t[2]=l=>o(a).traffic_url=l),placeholder:"请选择雅虎域名",options:o(k).ads_url_list,"field-names":{label:"url",value:"url"},onChange:ee},null,8,["value","options"])]),_:1}),n(_,{label:"搜索概率",name:"seach_rate",rules:[{required:!0,message:"请输入搜索概率"}]},{default:i(()=>[n(h,{value:o(a).seach_rate,"onUpdate:value":t[3]||(t[3]=l=>o(a).seach_rate=l),"addon-after":"%",placeholder:"请输入搜索概率",min:1,max:100},null,8,["value"])]),_:1}),n(_,{label:"点广告概率",name:"click1_rate",rules:[{required:!0,message:"请输入点广告概率"}]},{default:i(()=>[n(h,{value:o(a).click1_rate,"onUpdate:value":t[4]||(t[4]=l=>o(a).click1_rate=l),"addon-after":"%",placeholder:"请输入点广告概率",min:1,max:100},null,8,["value"])]),_:1}),n(_,{label:"二次点广告概率",name:"click2_rate",rules:[{required:!0,message:"请输入二次点广告概率"}]},{default:i(()=>[n(h,{value:o(a).click2_rate,"onUpdate:value":t[5]||(t[5]=l=>o(a).click2_rate=l),"addon-after":"%",placeholder:"请输入二次点广告概率",min:1,max:100},null,8,["value"])]),_:1}),n(_,{label:"三次点广告概率",name:"click3_rate",rules:[{required:!0,message:"请输入三次点广告概率"}]},{default:i(()=>[n(h,{value:o(a).click3_rate,"onUpdate:value":t[6]||(t[6]=l=>o(a).click3_rate=l),"addon-after":"%",placeholder:"请输入三次点广告概率",min:1,max:100},null,8,["value"])]),_:1}),n(_,{label:"sedo_task",name:"sedo_task",rules:[{required:!0,message:"请输入sedo_task"}]},{default:i(()=>[n(ge,{value:o(a).sedo_task,"onUpdate:value":t[7]||(t[7]=l=>o(a).sedo_task=l),placeholder:"选择跟哪个sedo任务绑定"},null,8,["value"])]),_:1}),n(_,{label:"备注",name:"note"},{default:i(()=>[n(me,{value:o(a).note,"onUpdate:value":t[8]||(t[8]=l=>o(a).note=l),placeholder:"备注",rows:2},null,8,["value"])]),_:1}),n(_,{"wrapper-col":{offset:6,span:16}},{default:i(()=>[n(z,{type:"primary","html-type":"submit",loading:o(a).loading},{default:i(()=>t[15]||(t[15]=[T(" 提交 ")])),_:1},8,["loading"])]),_:1})]),_:1},8,["model"])],512),[[M,!o(a).result.show]])]),_:1},8,["open"]),n(E,{open:o(S).open,"onUpdate:open":t[10]||(t[10]=l=>o(S).open=l),title:"任务详情",footer:null,maskClosable:!1,width:900},{default:i(()=>[g("div",Be,[n(fe,null,{default:i(()=>[n(q,{span:4},{default:i(()=>[n(j,{title:"任务总数",value:o(S).values.all_num},null,8,["value"])]),_:1}),n(q,{span:4},{default:i(()=>[n(j,{title:"打开数量",value:o(S).values.all_view},null,8,["value"])]),_:1}),n(q,{span:4},{default:i(()=>[n(j,{title:"点击数量",value:o(S).values.all_click1},null,8,["value"])]),_:1})]),_:1})]),n(L,{columns:re,"data-source":o(w).data,rowKey:"id",pagination:o(I),loading:o(w).loading,onChange:ne,bordered:"",scroll:{x:1500,y:400},size:"small"},{bodyCell:i(({column:l,record:r})=>[l.key==="action"?(f(),b(z,{key:0,type:"link",onClick:c=>le(r)},{default:i(()=>t[17]||(t[17]=[T(" 查看日志 ")])),_:2},1032,["onClick"])):y("",!0)]),_:1},8,["data-source","pagination","loading"])]),_:1},8,["open"]),n(E,{open:o(U).open,"onUpdate:open":t[11]||(t[11]=l=>o(U).open=l),title:"任务日志",footer:null,maskClosable:!1,width:900},{default:i(()=>[n(L,{columns:de,"data-source":o($).data,rowKey:"id",pagination:o(v),loading:o($).loading,onChange:ie,bordered:"",size:"small"},null,8,["data-source","pagination","loading"])]),_:1},8,["open"])],64)}}},et=ke(Ye,[["__scopeId","data-v-a647cadc"]]);export{et as default};
