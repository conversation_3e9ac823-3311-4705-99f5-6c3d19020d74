import{K as T,d as g,a0 as B,E as j,G as O,x as a,bc as V,am as F,L as G,J as W,bd as J,H as Y,P as E,at as Z,be as P,b8 as Q,a3 as tt,au as X,j as L,o as et,bf as nt,Z as ot,y as at,bg as it}from"./index-DlVegDiC.js";const lt=()=>({prefixCls:String,width:{type:[Number,String]}}),rt=T({compatConfig:{MODE:3},name:"SkeletonTitle",props:lt(),setup(t){return()=>{const{prefixCls:e,width:n}=t,o=typeof n=="number"?`${n}px`:n;return g("h3",{class:e,style:{width:o}},null)}}}),st=()=>({prefixCls:String,width:{type:[Number,String,Array]},rows:Number}),ct=T({compatConfig:{MODE:3},name:"SkeletonParagraph",props:st(),setup(t){const e=n=>{const{width:o,rows:i=2}=t;if(Array.isArray(o))return o[n];if(i-1===n)return o};return()=>{const{prefixCls:n,rows:o}=t,i=[...Array(o)].map((r,s)=>{const c=e(s);return g("li",{key:s,style:{width:typeof c=="number"?`${c}px`:c}},null)});return g("ul",{class:n},[i])}}}),U=t=>{const{prefixCls:e,size:n,shape:o}=t,i=B({[`${e}-lg`]:n==="large",[`${e}-sm`]:n==="small"}),r=B({[`${e}-circle`]:o==="circle",[`${e}-square`]:o==="square",[`${e}-round`]:o==="round"}),s=typeof n=="number"?{width:`${n}px`,height:`${n}px`,lineHeight:`${n}px`}:{};return g("span",{class:B(e,i,r),style:s},null)};U.displayName="SkeletonElement";const dt=new V("ant-skeleton-loading",{"0%":{transform:"translateX(-37.5%)"},"100%":{transform:"translateX(37.5%)"}}),I=t=>({height:t,lineHeight:`${t}px`}),x=t=>a({width:t},I(t)),ut=t=>({position:"relative",zIndex:0,overflow:"hidden",background:"transparent","&::after":{position:"absolute",top:0,insetInlineEnd:"-150%",bottom:0,insetInlineStart:"-150%",background:t.skeletonLoadingBackground,animationName:dt,animationDuration:t.skeletonLoadingMotionDuration,animationTimingFunction:"ease",animationIterationCount:"infinite",content:'""'}}),A=t=>a({width:t*5,minWidth:t*5},I(t)),gt=t=>{const{skeletonAvatarCls:e,color:n,controlHeight:o,controlHeightLG:i,controlHeightSM:r}=t;return{[`${e}`]:a({display:"inline-block",verticalAlign:"top",background:n},x(o)),[`${e}${e}-circle`]:{borderRadius:"50%"},[`${e}${e}-lg`]:a({},x(i)),[`${e}${e}-sm`]:a({},x(r))}},pt=t=>{const{controlHeight:e,borderRadiusSM:n,skeletonInputCls:o,controlHeightLG:i,controlHeightSM:r,color:s}=t;return{[`${o}`]:a({display:"inline-block",verticalAlign:"top",background:s,borderRadius:n},A(e)),[`${o}-lg`]:a({},A(i)),[`${o}-sm`]:a({},A(r))}},z=t=>a({width:t},I(t)),mt=t=>{const{skeletonImageCls:e,imageSizeBase:n,color:o,borderRadiusSM:i}=t;return{[`${e}`]:a(a({display:"flex",alignItems:"center",justifyContent:"center",verticalAlign:"top",background:o,borderRadius:i},z(n*2)),{[`${e}-path`]:{fill:"#bfbfbf"},[`${e}-svg`]:a(a({},z(n)),{maxWidth:n*4,maxHeight:n*4}),[`${e}-svg${e}-svg-circle`]:{borderRadius:"50%"}}),[`${e}${e}-circle`]:{borderRadius:"50%"}}},M=(t,e,n)=>{const{skeletonButtonCls:o}=t;return{[`${n}${o}-circle`]:{width:e,minWidth:e,borderRadius:"50%"},[`${n}${o}-round`]:{borderRadius:e}}},_=t=>a({width:t*2,minWidth:t*2},I(t)),ft=t=>{const{borderRadiusSM:e,skeletonButtonCls:n,controlHeight:o,controlHeightLG:i,controlHeightSM:r,color:s}=t;return a(a(a(a(a({[`${n}`]:a({display:"inline-block",verticalAlign:"top",background:s,borderRadius:e,width:o*2,minWidth:o*2},_(o))},M(t,o,n)),{[`${n}-lg`]:a({},_(i))}),M(t,i,`${n}-lg`)),{[`${n}-sm`]:a({},_(r))}),M(t,r,`${n}-sm`))},ht=t=>{const{componentCls:e,skeletonAvatarCls:n,skeletonTitleCls:o,skeletonParagraphCls:i,skeletonButtonCls:r,skeletonInputCls:s,skeletonImageCls:c,controlHeight:d,controlHeightLG:m,controlHeightSM:f,color:l,padding:u,marginSM:$,borderRadius:p,skeletonTitleHeight:b,skeletonBlockRadius:S,skeletonParagraphLineHeight:v,controlHeightXS:h,skeletonParagraphMarginTop:C}=t;return{[`${e}`]:{display:"table",width:"100%",[`${e}-header`]:{display:"table-cell",paddingInlineEnd:u,verticalAlign:"top",[`${n}`]:a({display:"inline-block",verticalAlign:"top",background:l},x(d)),[`${n}-circle`]:{borderRadius:"50%"},[`${n}-lg`]:a({},x(m)),[`${n}-sm`]:a({},x(f))},[`${e}-content`]:{display:"table-cell",width:"100%",verticalAlign:"top",[`${o}`]:{width:"100%",height:b,background:l,borderRadius:S,[`+ ${i}`]:{marginBlockStart:f}},[`${i}`]:{padding:0,"> li":{width:"100%",height:v,listStyle:"none",background:l,borderRadius:S,"+ li":{marginBlockStart:h}}},[`${i}> li:last-child:not(:first-child):not(:nth-child(2))`]:{width:"61%"}},[`&-round ${e}-content`]:{[`${o}, ${i} > li`]:{borderRadius:p}}},[`${e}-with-avatar ${e}-content`]:{[`${o}`]:{marginBlockStart:$,[`+ ${i}`]:{marginBlockStart:C}}},[`${e}${e}-element`]:a(a(a(a({display:"inline-block",width:"auto"},ft(t)),gt(t)),pt(t)),mt(t)),[`${e}${e}-block`]:{width:"100%",[`${r}`]:{width:"100%"},[`${s}`]:{width:"100%"}},[`${e}${e}-active`]:{[`
        ${o},
        ${i} > li,
        ${n},
        ${r},
        ${s},
        ${c}
      `]:a({},ut(t))}}},$t=j("Skeleton",t=>{const{componentCls:e}=t,n=O(t,{skeletonAvatarCls:`${e}-avatar`,skeletonTitleCls:`${e}-title`,skeletonParagraphCls:`${e}-paragraph`,skeletonButtonCls:`${e}-button`,skeletonInputCls:`${e}-input`,skeletonImageCls:`${e}-image`,imageSizeBase:t.controlHeight*1.5,skeletonTitleHeight:t.controlHeight/2,skeletonBlockRadius:t.borderRadiusSM,skeletonParagraphLineHeight:t.controlHeight/2,skeletonParagraphMarginTop:t.marginLG+t.marginXXS,borderRadius:100,skeletonLoadingBackground:`linear-gradient(90deg, ${t.color} 25%, ${t.colorGradientEnd} 37%, ${t.color} 63%)`,skeletonLoadingMotionDuration:"1.4s"});return[ht(n)]},t=>{const{colorFillContent:e,colorFill:n}=t;return{color:e,colorGradientEnd:n}}),St=()=>({active:{type:Boolean,default:void 0},loading:{type:Boolean,default:void 0},prefixCls:String,avatar:{type:[Boolean,Object],default:void 0},title:{type:[Boolean,Object],default:void 0},paragraph:{type:[Boolean,Object],default:void 0},round:{type:Boolean,default:void 0}});function D(t){return t&&typeof t=="object"?t:{}}function vt(t,e){return t&&!e?{size:"large",shape:"square"}:{size:"large",shape:"circle"}}function bt(t,e){return!t&&e?{width:"38%"}:t&&e?{width:"50%"}:{}}function kt(t,e){const n={};return(!t||!e)&&(n.width="61%"),!t&&e?n.rows=3:n.rows=2,n}const Ct=T({compatConfig:{MODE:3},name:"ASkeleton",props:F(St(),{avatar:!1,title:!0,paragraph:!0}),setup(t,e){let{slots:n}=e;const{prefixCls:o,direction:i}=G("skeleton",t),[r,s]=$t(o);return()=>{var c;const{loading:d,avatar:m,title:f,paragraph:l,active:u,round:$}=t,p=o.value;if(d||t.loading===void 0){const b=!!m||m==="",S=!!f||f==="",v=!!l||l==="";let h;if(b){const y=a(a({prefixCls:`${p}-avatar`},vt(S,v)),D(m));h=g("div",{class:`${p}-header`},[g(U,y,null)])}let C;if(S||v){let y;if(S){const w=a(a({prefixCls:`${p}-title`},bt(b,v)),D(f));y=g(rt,w,null)}let R;if(v){const w=a(a({prefixCls:`${p}-paragraph`},kt(b,S)),D(l));R=g(ct,w,null)}C=g("div",{class:`${p}-content`},[y,R])}const H=B(p,{[`${p}-with-avatar`]:b,[`${p}-active`]:u,[`${p}-rtl`]:i.value==="rtl",[`${p}-round`]:$,[s.value]:!0});return r(g("div",{class:H},[h,C]))}return(c=n.default)===null||c===void 0?void 0:c.call(n)}}}),Pt=W(J),q=t=>{const{value:e,formatter:n,precision:o,decimalSeparator:i,groupSeparator:r="",prefixCls:s}=t;let c;if(typeof n=="function")c=n({value:e});else{const d=String(e),m=d.match(/^(-?)(\d*)(\.(\d+))?$/);if(!m)c=d;else{const f=m[1];let l=m[2]||"0",u=m[4]||"";l=l.replace(/\B(?=(\d{3})+(?!\d))/g,r),typeof o=="number"&&(u=u.padEnd(o,"0").slice(0,o>0?o:0)),u&&(u=`${i}${u}`),c=[g("span",{key:"int",class:`${s}-content-value-int`},[f,l]),u&&g("span",{key:"decimal",class:`${s}-content-value-decimal`},[u])]}}return g("span",{class:`${s}-content-value`},[c])};q.displayName="StatisticNumber";const yt=t=>{const{componentCls:e,marginXXS:n,padding:o,colorTextDescription:i,statisticTitleFontSize:r,colorTextHeading:s,statisticContentFontSize:c,statisticFontFamily:d}=t;return{[`${e}`]:a(a({},Y(t)),{[`${e}-title`]:{marginBottom:n,color:i,fontSize:r},[`${e}-skeleton`]:{paddingTop:o},[`${e}-content`]:{color:s,fontSize:c,fontFamily:d,[`${e}-content-value`]:{display:"inline-block",direction:"ltr"},[`${e}-content-prefix, ${e}-content-suffix`]:{display:"inline-block"},[`${e}-content-prefix`]:{marginInlineEnd:n},[`${e}-content-suffix`]:{marginInlineStart:n}}})}},wt=j("Statistic",t=>{const{fontSizeHeading3:e,fontSize:n,fontFamily:o}=t,i=O(t,{statisticTitleFontSize:n,statisticContentFontSize:e,statisticFontFamily:o});return[yt(i)]}),K=()=>({prefixCls:String,decimalSeparator:String,groupSeparator:String,format:String,value:X([Number,String,Object]),valueStyle:{type:Object,default:void 0},valueRender:tt(),formatter:Q(),precision:Number,prefix:P(),suffix:P(),title:P(),loading:Z()}),k=T({compatConfig:{MODE:3},name:"AStatistic",inheritAttrs:!1,props:F(K(),{decimalSeparator:".",groupSeparator:",",loading:!1}),slots:Object,setup(t,e){let{slots:n,attrs:o}=e;const{prefixCls:i,direction:r}=G("statistic",t),[s,c]=wt(i);return()=>{var d,m,f,l,u,$,p;const{value:b=0,valueStyle:S,valueRender:v}=t,h=i.value,C=(d=t.title)!==null&&d!==void 0?d:(m=n.title)===null||m===void 0?void 0:m.call(n),H=(f=t.prefix)!==null&&f!==void 0?f:(l=n.prefix)===null||l===void 0?void 0:l.call(n),y=(u=t.suffix)!==null&&u!==void 0?u:($=n.suffix)===null||$===void 0?void 0:$.call(n),R=(p=t.formatter)!==null&&p!==void 0?p:n.formatter;let w=g(q,E({"data-for-update":Date.now()},a(a({},t),{prefixCls:h,value:b,formatter:R})),null);return v&&(w=v(w)),s(g("div",E(E({},o),{},{class:[h,{[`${h}-rtl`]:r.value==="rtl"},o.class,c.value]}),[C&&g("div",{class:`${h}-title`},[C]),g(Ct,{paragraph:!1,loading:t.loading},{default:()=>[g("div",{style:S,class:`${h}-content`},[H&&g("span",{class:`${h}-content-prefix`},[H]),w,y&&g("span",{class:`${h}-content-suffix`},[y])])]})]))}}}),xt=[["Y",1e3*60*60*24*365],["M",1e3*60*60*24*30],["D",1e3*60*60*24],["H",1e3*60*60],["m",1e3*60],["s",1e3],["S",1]];function Tt(t,e){let n=t;const o=/\[[^\]]*]/g,i=(e.match(o)||[]).map(d=>d.slice(1,-1)),r=e.replace(o,"[]"),s=xt.reduce((d,m)=>{let[f,l]=m;if(d.includes(f)){const u=Math.floor(n/l);return n-=u*l,d.replace(new RegExp(`${f}+`,"g"),$=>{const p=$.length;return u.toString().padStart(p,"0")})}return d},r);let c=0;return s.replace(o,()=>{const d=i[c];return c+=1,d})}function Ht(t,e){const{format:n=""}=e,o=new Date(t).getTime(),i=Date.now(),r=Math.max(o-i,0);return Tt(r,n)}const Rt=1e3/30;function N(t){return new Date(t).getTime()}const Bt=()=>a(a({},K()),{value:X([Number,String,Object]),format:String,onFinish:Function,onChange:Function}),Et=T({compatConfig:{MODE:3},name:"AStatisticCountdown",props:F(Bt(),{format:"HH:mm:ss"}),setup(t,e){let{emit:n,slots:o}=e;const i=L(),r=L(),s=()=>{const{value:l}=t;N(l)>=Date.now()?c():d()},c=()=>{if(i.value)return;const l=N(t.value);i.value=setInterval(()=>{r.value.$forceUpdate(),l>Date.now()&&n("change",l-Date.now()),s()},Rt)},d=()=>{const{value:l}=t;i.value&&(clearInterval(i.value),i.value=void 0,N(l)<Date.now()&&n("finish"))},m=l=>{let{value:u,config:$}=l;const{format:p}=t;return Ht(u,a(a({},$),{format:p}))},f=l=>l;return et(()=>{s()}),nt(()=>{s()}),ot(()=>{d()}),()=>{const l=t.value;return g(k,E({ref:r},a(a({},at(t,["onFinish","onChange"])),{value:l,valueRender:f,formatter:m})),o)}}});k.Countdown=Et;k.install=function(t){return t.component(k.name,k),t.component(k.Countdown.name,k.Countdown),t};k.Countdown;const At=W(it);export{k as S,At as _,Pt as a};
