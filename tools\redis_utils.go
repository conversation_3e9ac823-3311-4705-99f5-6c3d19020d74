package tools

import (
	"context"
	"errors"
	"fmt"
	"math/rand"
	"rsoc-system-go/middleware/pkg/redis"
	"rsoc-system-go/store"
	"rsoc-system-go/tools/constants"
	"strconv"
	"time"
)

// GetRandomFromRedis 从Redis中随机获取一条数据(适用于小数据量)
// pattern: key的模式，例如"useragent:ua:*"
func GetRandomFromRedis(pattern, field string) (string, error) {
	// 使用KEYS命令获取所有匹配的key
	ctx := context.Background()
	client := redis.SelectDB(0)

	// 获取随机过的值
	//out_value, err := client.HGet(ctx, "out_"+pattern+":"+field, field).Result()

	// 如果有值则重新获取
	/*if out_value != "" {
		uar := rand.New(rand.NewSource(time.Now().UnixNano()))
		uaIndex := uar.Intn(82) + 1
		key := "ua" + strconv.Itoa(uaIndex)
		count := constants.UAFileCountMap[key]
		valr := rand.New(rand.NewSource(time.Now().UnixNano()))
		val := valr.Intn(count) + 1
		return GetRandomFromRedis(key, strconv.Itoa(val))
	}*/

	// 获取该key的值
	value, err := client.HGet(ctx, pattern+":"+field, field).Result()
	if err != nil {
		if errors.Is(err, errors.New("redis: nil")) {
			uar := rand.New(rand.NewSource(time.Now().UnixNano()))
			uaIndex := uar.Intn(82) + 1
			key := "ua" + strconv.Itoa(uaIndex)
			count := constants.UAFileCountMap[key]
			valr := rand.New(rand.NewSource(time.Now().UnixNano()))
			val := valr.Intn(count) + 1
			return GetRandomFromRedis(key, strconv.Itoa(val))
		} else {
			return "", fmt.Errorf("获取值失败: %v", err)
		}

	}

	if value == "" {
		uar := rand.New(rand.NewSource(time.Now().UnixNano()))
		uaIndex := uar.Intn(82) + 1
		key := "ua" + strconv.Itoa(uaIndex)
		count := constants.UAFileCountMap[key]
		valr := rand.New(rand.NewSource(time.Now().UnixNano()))
		val := valr.Intn(count) + 1
		return GetRandomFromRedis(key, strconv.Itoa(val))
	}

	/*_, err = client.HSet(ctx, "out_"+pattern+":"+field, field, pattern+":"+field).Result()
	if err != nil {
		log.Println("设置out_"+pattern+":"+field+"失败:", err)
	}*/

	return value, nil
}

// GetRandomFromRedisByScan 使用SCAN命令从Redis中随机获取一条数据(适用于大数据量)
// pattern: key的模式，例如"useragent:ua:*"
// sampleSize: 采样大小，建议设置一个较小的值，如100
func GetRandomFromRedisByScan(pattern string, sampleSize int) (string, error) {
	ctx := context.Background()
	var cursor uint64
	var keys []string
	var err error

	// 使用SCAN命令进行采样
	for len(keys) < sampleSize {
		scanCmd := store.RedisClient.Scan(ctx, cursor, pattern, int64(100))
		keys2, cursor2 := scanCmd.Val()
		err := scanCmd.Err()
		if err != nil {
			return "", fmt.Errorf("SCAN失败: %v", err)
		}

		keys = append(keys, keys2...)
		cursor = cursor2

		// 如果已经扫描完所有键，就退出
		if cursor == 0 {
			break
		}
	}

	if len(keys) == 0 {
		return "", fmt.Errorf("没有找到匹配的key")
	}

	// 从采样中随机选择一个key
	randomIndex := rand.Intn(len(keys))
	randomKey := keys[randomIndex]

	// 获取该key的值
	value, err := store.RedisClient.Get(ctx, randomKey).Result()
	if err != nil {
		return "", fmt.Errorf("获取值失败: %v", err)
	}

	return value, nil
}
