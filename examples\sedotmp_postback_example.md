# SedoTMP Postback 兼容性使用示例

## 概述

系统现已完全兼容 SedoTMP 的 postback 格式，支持所有官方宏参数。您可以直接使用 SedoTMP 提供的 postback URL 格式。

## SedoTMP Postback URL 格式

```
https://your-tracking-url.com/rsoc/postback?click_id={click_id}&epayout={epayout}&campaign={campaign}&country={country}&country_name={country_name}&state={state}&city={city}&zip={zip}&os_type={os_type}&browser={browser}&device_type={device_type}&device_brand={device_brand}&subid1={subid1}&subid2={subid2}&subid3={subid3}&subid4={subid4}&subid5={subid5}
```

## 支持的宏参数

### 基础参数
- `{campaign}` - 广告系列ID
- `{click_id}` - 点击ID（用于转化跟踪）
- `{epayout}` - 估算收益金额

### 地理位置参数
- `{country}` - 2位ISO国家代码（如：US）
- `{country_name}` - 完整国家名称（如：United States）
- `{state}` - 州代码或名称（如：CA 或 California）
- `{city}` - 城市名称（如：Los Angeles）
- `{zip}` - 邮编（如：90001）

### 设备和浏览器参数
- `{os_type}` - 操作系统（如：WINDOWS, MACOS, ANDROID, IOS）
- `{browser}` - 浏览器类型（如：CHROME, FIREFOX, SAFARI）
- `{device_type}` - 设备类型（如：MOBILE, DESKTOP）
- `{device_brand}` - 设备品牌（如：APPLE, SAMSUNG）

### 自定义子ID参数
- `{subid1}` - 自定义子ID 1（如：AdSetID）
- `{subid2}` - 自定义子ID 2（如：PictureID）
- `{subid3}` - 自定义子ID 3
- `{subid4}` - 自定义子ID 4
- `{subid5}` - 自定义子ID 5

## 实际使用示例

### 示例1：基础转化跟踪

```bash
# SedoTMP 会调用这样的URL
curl "https://your-domain.com/rsoc/postback?click_id=abc123&epayout=0.123&campaign=12345&country=US"
```

### 示例2：完整参数跟踪

```bash
# 包含所有地理和设备信息的完整跟踪
curl "https://your-domain.com/rsoc/postback?click_id=xyz789&epayout=1.25&campaign=67890&country=US&country_name=United%20States&state=CA&city=Los%20Angeles&zip=90210&os_type=WINDOWS&browser=CHROME&device_type=DESKTOP&device_brand=DELL&subid1=AdSet123&subid2=Creative456&subid3=Audience789"
```

### 示例3：移动设备转化

```bash
# 移动设备上的转化跟踪
curl "https://your-domain.com/rsoc/postback?click_id=mobile123&epayout=0.85&campaign=mobile001&country=GB&country_name=United%20Kingdom&state=London&city=London&zip=SW1A&os_type=ANDROID&browser=CHROME&device_type=MOBILE&device_brand=SAMSUNG&subid1=MobileAd&subid2=Banner"
```

## 数据存储说明

所有 SedoTMP postback 数据都会存储到 `facebook_insights` 表中：

| SedoTMP 参数 | FacebookInsights 字段 | 说明 |
|--------------|----------------------|------|
| campaign | campaign_id, account_id, ad_id, adset_id | 广告系列标识 |
| epayout | real_price | 真实收益 |
| click_id | - | 用于日志记录 |
| country, state, city | - | 地理信息（日志记录） |
| os_type, browser, device_type | - | 设备信息（日志记录） |
| subid1-subid5 | - | 自定义参数（日志记录） |

## 与 AdTech 格式的兼容性

系统同时支持 AdTech 和 SedoTMP 两种格式：

### AdTech 格式
```bash
curl "https://your-domain.com/rsoc/postback?campaign=123&click_id=abc&payout=1.50&s1=test1"
```

### SedoTMP 格式
```bash
curl "https://your-domain.com/rsoc/postback?campaign=123&click_id=abc&epayout=1.50&subid1=test1"
```

### 混合格式（自动处理）
```bash
# 系统会优先使用 payout，如果没有则使用 epayout
curl "https://your-domain.com/rsoc/postback?campaign=123&click_id=abc&payout=1.50&epayout=1.25&s1=test1&subid1=test2"
```

## 配置 SedoTMP Postback

### 步骤1：在 SedoTMP 中设置 Postback URL

在 SedoTMP 平台中，将您的 postback URL 设置为：

```
https://your-domain.com/rsoc/postback?click_id={click_id}&epayout={epayout}&campaign={campaign}&country={country}&subid1={subid1}&subid2={subid2}
```

### 步骤2：测试 Postback

使用测试数据验证 postback 是否正常工作：

```bash
curl "https://your-domain.com/rsoc/postback?click_id=test123&epayout=0.50&campaign=test_campaign&country=US&subid1=test_subid"
```

### 步骤3：监控转化数据

查询数据库验证转化数据是否正确存储：

```sql
SELECT 
    campaign_id,
    date,
    clicks,
    real_price,
    update_time
FROM facebook_insights 
WHERE campaign_id = 'test_campaign'
ORDER BY update_time DESC;
```

## 日志和调试

### 查看 Postback 日志

```bash
# 查看应用日志中的 postback 相关信息
tail -f /var/log/your-app.log | grep "postback"
```

### 典型日志输出

```
2025-01-15 10:30:00 INFO 接收到postback数据: {Campaign:12345 ClickID:abc123 EPayout:0.123 Country:US CountryName:United States State:CA City:Los Angeles ...}
2025-01-15 10:30:01 INFO 处理postback数据成功: campaign=12345, real_price=0.123
```

## 故障排除

### 常见问题

1. **收益数据不正确**
   - 检查是否同时传递了 `payout` 和 `epayout`
   - 系统优先使用 `payout`，确保传递正确的参数

2. **数据重复**
   - 同一天同一个 campaign 的多次转化会更新而不是创建新记录
   - 这是正常行为，确保数据一致性

3. **参数编码问题**
   - 确保 URL 参数正确编码，特别是包含空格的参数
   - 例如：`country_name=United%20States`

### 验证脚本

```bash
#!/bin/bash
# 验证 SedoTMP postback 功能

BASE_URL="https://your-domain.com/rsoc/postback"

# 测试基础功能
echo "测试基础 SedoTMP postback..."
response=$(curl -s "$BASE_URL?click_id=test123&epayout=1.25&campaign=test_sedotmp&country=US")
echo "响应: $response"

# 检查数据库
echo "检查数据库记录..."
mysql -u user -p'password' -D database -e "
SELECT campaign_id, real_price, update_time 
FROM facebook_insights 
WHERE campaign_id = 'test_sedotmp' 
ORDER BY update_time DESC 
LIMIT 1;"
```

## 最佳实践

1. **参数选择**：根据您的跟踪需求选择必要的宏参数，避免传递过多不需要的参数
2. **错误处理**：监控 postback 调用的成功率，设置告警机制
3. **数据验证**：定期检查转化数据的准确性和完整性
4. **性能优化**：对于高流量场景，考虑使用异步处理机制
