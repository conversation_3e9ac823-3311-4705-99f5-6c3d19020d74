package pagination

import "gorm.io/gorm"

// PageRequest 定义通用分页请求结构
type PageRequest struct {
	PageNum  int `form:"pageNum" json:"pageNum"`   // 页码
	PageSize int `form:"pageSize" json:"pageSize"` // 每页大小
}

// PageResult 定义通用分页结果结构
type PageResult struct {
	Total     int64       `json:"total"`     // 总记录数
	PageNum   int         `json:"pageNum"`   // 当前页码
	PageSize  int         `json:"pageSize"`  // 每页大小
	PageTotal int         `json:"pageTotal"` // 总页数
	List      interface{} `json:"list"`      // 数据列表
}

// GetDefaultPageRequest 获取默认的分页请求参数
func GetDefaultPageRequest(req *PageRequest) *PageRequest {
	if req == nil {
		return &PageRequest{
			PageNum:  1,
			PageSize: 10,
		}
	}
	if req.PageNum <= 0 {
		req.PageNum = 1
	}
	if req.PageSize <= 0 {
		req.PageSize = 10
	}
	return req
}

// GetPageTotal 计算总页数
func GetPageTotal(total int64, pageSize int) int {
	return (int(total) + pageSize - 1) / pageSize
}

// Paginate 通用分页查询方法
func Paginate[T any](db *gorm.DB, req *PageRequest, condition interface{}) (*PageResult, error) {
	req = GetDefaultPageRequest(req)
	var total int64
	var list []T

	// 统计总数
	if err := db.Model(new(T)).Where(condition).Count(&total).Error; err != nil {
		return nil, err
	}

	// 查询分页数据
	if err := db.Model(new(T)).
		Where(condition).
		Offset((req.PageNum - 1) * req.PageSize).
		Limit(req.PageSize).
		Find(&list).Error; err != nil {
		return nil, err
	}

	// 构造分页结果
	pageResult := &PageResult{
		Total:     total,
		PageNum:   req.PageNum,
		PageSize:  req.PageSize,
		PageTotal: GetPageTotal(total, req.PageSize),
		List:      list,
	}

	return pageResult, nil
}

func PageR[T any]() *PagesRequest {
	var model []T
	datas := &PagesRequest{
		PageRequest: &PageRequest{
			PageNum:  1,
			PageSize: 10,
		},
		data: model,
	}
	return datas
}

type PagesRequest struct {
	*PageRequest
	data interface{}
}
