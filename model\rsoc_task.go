package model

import (
	"time"

	"gorm.io/gorm"
)

// Task 任务模型
type RTask struct {
	gorm.Model
	TaskID       string    `gorm:"column:task_id;type:varchar(255);not null"`
	Status       int       `gorm:"column:status;type:int;default:1"`
	IsGlobal     int       `gorm:"column:is_global;type:int;default:0"`
	TwoDirectory int       `gorm:"column:two_directory;type:int"`
	Platform     string    `gorm:"column:platform;type:varchar(50)"`
	PlatformID   int       `gorm:"column:platform_id;type:int"`
	HoldRate     int       `gorm:"column:hold_rate;type:int;default:50"`
	FBPixelID    string    `gorm:"column:fb_pixel_id;type:varchar(255)"`
	FBPixelToken string    `gorm:"column:fb_pixel_api_token;type:varchar(255)"`
	ProxyID      string    `gorm:"column:proxy_id;type:varchar(255)"`
	TrafficURL   string    `gorm:"column:traffic_url;type:varchar(255)"`
	BlackURL     string    `gorm:"column:black_url;type:varchar(255)"`
	AllNum       int64     `gorm:"column:all_num;type:bigint;default:0"`
	AllAutoNum   int64     `gorm:"column:all_auto_num;type:bigint;default:0"`
	CreatedAt    time.Time `gorm:"column:created_at"`
	UpdatedAt    time.Time `gorm:"column:updated_at"`
}

// AutoTask 自动任务模型
type RAutoTask struct {
	gorm.Model
	TaskID      string    `gorm:"column:task_id;type:varchar(255);not null"`
	ClickID     string    `gorm:"column:click_id;type:varchar(255);not null"`
	Country     string    `gorm:"column:country;type:varchar(50)"`
	IPCountry   string    `gorm:"column:ip_country;type:varchar(50)"`
	UserAgent   string    `gorm:"column:useragent;type:text"`
	Lang        string    `gorm:"column:lang;type:varchar(50)"`
	UserIP      string    `gorm:"column:user_ip;type:varchar(50)"`
	Ref         string    `gorm:"column:ref;type:text"`
	Viewport    string    `gorm:"column:viewport;type:text"`
	AddTime     time.Time `gorm:"column:add_time"`
	Date        string    `gorm:"column:date;type:varchar(20)"`
	CampaignID  string    `gorm:"column:campaign_id;type:varchar(255)"`
	TrafficData string    `gorm:"column:traffic_data;type:text"`
	Traffic     string    `gorm:"column:traffic;type:varchar(50)"`
	TrafficURL  string    `gorm:"column:traffic_url;type:text"`
	ClickURL    string    `gorm:"column:click_url;type:text"`
}

// TableName 设置Task表名
func (RTask) TableName() string {
	return "task"
}

// TableName 设置AutoTask表名
func (RAutoTask) TableName() string {
	return "auto_task"
}

// AutoTaskNot 非自动任务模型
type AutoTaskNot struct {
	AutoTask
}

// TableName 设置AutoTaskNot表名
func (AutoTaskNot) TableName() string {
	return "auto_task_not"
}
