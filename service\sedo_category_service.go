package service

import (
	"context"
	"fmt"
	"os"
	"rsoc-system-go/model"
	"sync"
)

// SedoCategoryService Sedo分类服务
type SedoCategoryService struct {
	httpClient *SedoHTTPClient
}

var (
	sedoCategoryService     *SedoCategoryService
	sedoCategoryServiceOnce sync.Once
)

// GetSedoCategoryService 获取Sedo分类服务单例
func GetSedoCategoryService() *SedoCategoryService {
	sedoCategoryServiceOnce.Do(func() {
		sedoCategoryService = NewSedoCategoryService(GetSedoHTTPClient())
	})
	// 方法1：使用简化的OAuth初始化
	sedoCategoryService.InitializeWithOAuth(
		os.Getenv("SEDO_CLIENT_ID"),
		os.Getenv("SEDO_CLIENT_SECRET"),
	)
	return sedoCategoryService
}

// NewSedoCategoryService 创建新的Sedo分类服务
func NewSedoCategoryService(httpClient *SedoHTTPClient) *SedoCategoryService {
	return &SedoCategoryService{
		httpClient: httpClient,
	}
}

// GetCategories 获取分类列表
func (s *SedoCategoryService) GetCategories(ctx context.Context, page, size int, term string) ([]model.SedoCategory, *model.SedoPageResponse, error) {
	query := s.httpClient.BuildQueryParams(page, size, "", term)

	resp, err := s.httpClient.Get(ctx, "/categories", query, nil)
	if err != nil {
		return nil, nil, fmt.Errorf("获取分类列表请求失败: %v", err)
	}

	var categories []model.SedoCategory
	if err := s.httpClient.ParseResponse(resp, &categories); err != nil {
		return nil, nil, fmt.Errorf("解析分类列表响应失败: %v", err)
	}

	totalCount, totalPages, err := s.httpClient.GetPageHeaders(resp)
	if err != nil {
		return nil, nil, fmt.Errorf("获取分页信息失败: %v", err)
	}

	pageResponse := &model.SedoPageResponse{
		TotalCount: totalCount,
		TotalPages: totalPages,
	}

	return categories, pageResponse, nil
}

// GetCategoryByID 根据ID获取分类
func (s *SedoCategoryService) GetCategoryByID(ctx context.Context, id string) (*model.SedoCategory, error) {
	resp, err := s.httpClient.Get(ctx, fmt.Sprintf("/categories/%s", id), nil, nil)
	if err != nil {
		return nil, fmt.Errorf("获取分类详情请求失败: %v", err)
	}

	var category model.SedoCategory
	if err := s.httpClient.ParseResponse(resp, &category); err != nil {
		return nil, fmt.Errorf("解析分类详情响应失败: %v", err)
	}

	return &category, nil
}

// CreateCategory 创建分类
func (s *SedoCategoryService) CreateCategory(ctx context.Context, request *model.SedoCreateCategoryRequest) (*model.SedoCategory, error) {
	resp, err := s.httpClient.Post(ctx, "/categories", nil, request, nil)
	if err != nil {
		return nil, fmt.Errorf("创建分类请求失败: %v", err)
	}

	var category model.SedoCategory
	if err := s.httpClient.ParseResponse(resp, &category); err != nil {
		return nil, fmt.Errorf("解析创建分类响应失败: %v", err)
	}

	return &category, nil
}

// SetBearerToken 设置Bearer令牌
func (s *SedoCategoryService) SetBearerToken(token string) {
	s.httpClient.SetBearerToken(token)
}

// UseOAuth 启用OAuth身份验证
func (s *SedoCategoryService) UseOAuth(enable bool) {
	s.httpClient.UseOAuth(enable)
}

// SetOAuthCredentials 设置OAuth凭据
func (s *SedoCategoryService) SetOAuthCredentials(clientID, clientSecret string) {
	s.httpClient.SetOAuthCredentials(clientID, clientSecret)
}

// SetOAuthConfig 设置OAuth配置
func (s *SedoCategoryService) SetOAuthConfig(tokenEndpoint, clientID, clientSecret, audience, grantType string) {
	oauthClient := GetSedoOAuthClient()
	oauthClient.SetConfig(SedoOAuthConfig{
		TokenEndpoint: tokenEndpoint,
		ClientID:      clientID,
		ClientSecret:  clientSecret,
		Audience:      audience,
		GrantType:     grantType,
		Logger:        oauthClient.logger,
	})
	s.httpClient.UseOAuth(true)
}

// -----------------------------
// 辅助方法
// -----------------------------

// InitializeWithToken 使用令牌初始化服务
func (s *SedoCategoryService) InitializeWithToken(token string) {
	s.SetBearerToken(token)
}

// InitializeWithOAuth 使用OAuth凭据初始化服务
func (s *SedoCategoryService) InitializeWithOAuth(clientID, clientSecret string) {
	s.SetOAuthCredentials(clientID, clientSecret)
}

// InitializeWithFullOAuth 使用完整OAuth配置初始化服务
func (s *SedoCategoryService) InitializeWithFullOAuth(tokenEndpoint, clientID, clientSecret, audience, grantType string) {
	s.SetOAuthConfig(tokenEndpoint, clientID, clientSecret, audience, grantType)
}
