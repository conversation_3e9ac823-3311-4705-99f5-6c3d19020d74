package system

import (
	"context"
	"github.com/gin-gonic/gin"
	"net/http"
	"os"
	"rsoc-system-go/config"
	"rsoc-system-go/dao"
	"rsoc-system-go/middleware/pkg/redis"
	"rsoc-system-go/store"
	"strconv"
)

type SystemH struct {
}

// Reload 重新加载token
func (r *SystemH) Reload(c *gin.Context) {
	userDao := dao.NewUserDao(store.DB)
	list, err := userDao.List()
	if err != nil {
		c.JSON(http.StatusOK, config.Result{500, "date is empty", nil})
		return
	}
	/*redisManager := store.GetRedisManager()
	err = redisManager.ConnectRedis("task")
	client := redisManager.GetRedisClient("task")*/
	indexdbStr := os.Getenv("redisdb")
	indexdb, _ := strconv.Atoi(indexdbStr)
	client := redis.SelectDB(indexdb)
	//client := redis.SelectDB(1)
	_, err = client.Del(context.Background(), "Token").Result()
	if err != nil {
		c.JSON(http.StatusOK, config.Result{500, "reload err", nil})
		return
	}

	for _, user := range list {
		_, err = client.HSet(context.Background(), "Token", user.Token, user.Token).Result()
		if err != nil {
			c.JSON(http.StatusOK, config.Result{500, "date is empty", nil})
			return
		}
	}
	c.JSON(http.StatusOK, config.Result{1, "OK", nil})
}
