import{_ as A,u as E,o as J,r as x,a as M,M as i,j as z,c as L,b as p,d as l,w as s,B as Y,e as a,k as D,f as k,h as y,m as O,n as S,F as K,g as G,I as H,p as Q,s as N}from"./index-DlVegDiC.js";import{P as W}from"./PlusOutlined-Cg2o2XQN.js";import{_ as X}from"./index-1uCBjWky.js";import{_ as Z}from"./index-DXcpAzs8.js";import{_ as ee}from"./index-B8PO_1fg.js";import"./index-CSU5nP3m.js";const te={class:"main"},le={class:"filter"},ae={__name:"proxy",setup(oe){const c="",f=E(),$=M();J(()=>{b()});const m=x({data:[],loading:!1}),b=()=>{m.loading=!0,fetch(`${c}/proxy/list`,{method:"POST",body:JSON.stringify({directoryStatus:1}),headers:{token:f.token}}).then(o=>o.json()).then(o=>{console.log(o),o.code==1?m.data=o.data.map(e=>(e.checked=e.status==1,e.checked_loading=!1,e)):o.code==3e3?(f.$patch({token:!1}),$.push("/login"),i.error({title:o.msg})):(m.data=[],i.error({title:o.msg})),m.loading=!1}).catch(o=>{i.error({title:"服务器错误",content:`${o}`})})},v=z(),r=x({open:!1,label:"",url:"",port:"",user:"",pass:"",note:"",rate:"",loading:!1}),P=()=>{r.open=!0},B=()=>{r.loading=!0,fetch(`${c}/proxy/add`,{method:"POST",body:JSON.stringify({label:r.label,proxy_url:r.url,proxy_port:r.port,proxy_user:r.user,proxy_pass:r.pass,rate:r.rate,note:r.note}),headers:{token:f.token}}).then(o=>o.json()).then(o=>{console.log(o),o.code==1?(N.success("添加成功"),b(),v.value.resetFields()):i.error({title:o.msg}),r.open=!1,r.loading=!1}).catch(o=>{i.error({title:"服务器错误",content:`${o}`})})},t=x({open:!1,id:0,label:"",url:"",port:"",user:"",pass:"",status:1,rate:"",note:"",loading:!1}),F=o=>{console.log(o),t.id=o.id,t.label=o.label,t.url=o.proxy_url,t.port=o.proxy_port,t.user=o.proxy_user,t.pass=o.proxy_pass,t.status=o.checked,t.rate=o.rate,t.note=o.note,t.open=!0},T=()=>{t.loading=!0,fetch(`${c}/proxy/update`,{method:"POST",body:JSON.stringify({id:t.id,label:t.label,proxy_url:t.url,proxy_port:t.port,proxy_user:t.user,proxy_pass:t.pass,status:t.status?1:2,rate:t.rate,note:t.note}),headers:{token:f.token}}).then(o=>o.json()).then(o=>{console.log(o),o.code==1?(N.success("修改成功"),b()):i.error({title:o.msg}),t.open=!1,t.loading=!1}).catch(o=>{i.error({title:"服务器错误",content:`${o}`})})},j=[{title:"ID",dataIndex:"id",key:"id",align:"center"},{title:"名称",dataIndex:"label",key:"label",align:"center"},{title:"代理主机",dataIndex:"proxy_url",key:"proxy_url",align:"center"},{title:"代理端口",dataIndex:"proxy_port",key:"proxy_port",align:"center"},{title:"用户名",dataIndex:"proxy_user",key:"proxy_user",align:"center"},{title:"密码",dataIndex:"proxy_pass",key:"proxy_pass",align:"center"},{title:"状态",dataIndex:"status",key:"status",align:"center"},{title:"权重",dataIndex:"rate",key:"rate",align:"center"},{title:"备注",dataIndex:"note",key:"note",align:"center"},{title:"操作",key:"action",align:"center"}];return(o,e)=>{const R=W,_=Y,U=Z,V=X,u=H,d=G,h=ee,w=Q,I=K,q=i;return k(),L(D,null,[p("div",te,[p("div",null,[p("div",le,[e[18]||(e[18]=p("div",{class:"filter_item"},null,-1)),p("div",null,[l(_,{type:"primary",onClick:P},{icon:s(()=>[l(R)]),default:s(()=>[e[17]||(e[17]=y(" 添加代理 "))]),_:1})])])]),l(V,{columns:j,"data-source":a(m).data,rowKey:"id",pagination:!1,loading:a(m).loading,bordered:""},{bodyCell:s(({column:n,record:g})=>[n.key==="status"?(k(),O(U,{key:0,checked:g.checked,"onUpdate:checked":C=>g.checked=C,loading:g.checked_loading,disabled:""},null,8,["checked","onUpdate:checked","loading"])):S("",!0),n.key==="action"?(k(),O(_,{key:1,type:"link",onClick:C=>F(g)},{default:s(()=>e[19]||(e[19]=[y("修改")])),_:2},1032,["onClick"])):S("",!0)]),_:1},8,["data-source","loading"])]),l(q,{open:a(r).open,"onUpdate:open":e[7]||(e[7]=n=>a(r).open=n),title:"添加代理",footer:null,maskClosable:!1},{default:s(()=>[e[22]||(e[22]=p("div",{style:{height:"20px"}},null,-1)),l(I,{ref_key:"add_form",ref:v,model:a(r),onFinish:B,"label-col":{span:5},"wrapper-col":{span:17}},{default:s(()=>[l(d,{label:"代理名称",name:"label",rules:[{required:!0,message:"请输入代理名称"}]},{default:s(()=>[l(u,{value:a(r).label,"onUpdate:value":e[0]||(e[0]=n=>a(r).label=n),placeholder:"代理名称"},null,8,["value"])]),_:1}),l(d,{label:"代理主机",name:"url",rules:[{required:!0,message:"请输入代理主机"}]},{default:s(()=>[l(u,{value:a(r).url,"onUpdate:value":e[1]||(e[1]=n=>a(r).url=n),placeholder:"代理主机"},null,8,["value"])]),_:1}),l(d,{label:"代理端口",name:"port",rules:[{required:!0,message:"请输入代理端口"}]},{default:s(()=>[l(u,{value:a(r).port,"onUpdate:value":e[2]||(e[2]=n=>a(r).port=n),placeholder:"代理端口"},null,8,["value"])]),_:1}),l(d,{label:"用户名",name:"user",rules:[{required:!0,message:"请输入用户名"}]},{default:s(()=>[l(u,{value:a(r).user,"onUpdate:value":e[3]||(e[3]=n=>a(r).user=n),placeholder:"用户名"},null,8,["value"]),e[20]||(e[20]=p("p",{style:{"margin-top":"6px","font-size":"12px",color:"red"}}," 用户名可以包括变量：{country}代替小写国家，{COUNTRY}代表大写的国家，{randstring:10}表示随机字符串，10的部分可以自定义字符串的长度 ",-1))]),_:1}),l(d,{label:"密码",name:"pass",rules:[{required:!0,message:"请输入密码"}]},{default:s(()=>[l(u,{value:a(r).pass,"onUpdate:value":e[4]||(e[4]=n=>a(r).pass=n),placeholder:"密码"},null,8,["value"])]),_:1}),l(d,{label:"权重",name:"rate",rules:[{required:!0,message:"请输入权重"}]},{default:s(()=>[l(h,{value:a(r).rate,"onUpdate:value":e[5]||(e[5]=n=>a(r).rate=n),placeholder:"请输入权重",min:1,max:100,style:{width:"100%"}},null,8,["value"])]),_:1}),l(d,{label:"备注",name:"note"},{default:s(()=>[l(w,{value:a(r).note,"onUpdate:value":e[6]||(e[6]=n=>a(r).note=n),placeholder:"备注",rows:2},null,8,["value"])]),_:1}),l(d,{"wrapper-col":{offset:5,span:17}},{default:s(()=>[l(_,{type:"primary","html-type":"submit",loading:a(r).loading},{default:s(()=>e[21]||(e[21]=[y(" 提交 ")])),_:1},8,["loading"])]),_:1})]),_:1},8,["model"])]),_:1},8,["open"]),l(q,{open:a(t).open,"onUpdate:open":e[16]||(e[16]=n=>a(t).open=n),title:"编辑代理",footer:null,maskClosable:!1},{default:s(()=>[e[25]||(e[25]=p("div",{style:{height:"20px"}},null,-1)),l(I,{model:a(t),onFinish:T,"label-col":{span:5},"wrapper-col":{span:17}},{default:s(()=>[l(d,{label:"代理名称",name:"label",rules:[{required:!0,message:"请输入代理名称"}]},{default:s(()=>[l(u,{value:a(t).label,"onUpdate:value":e[8]||(e[8]=n=>a(t).label=n),placeholder:"代理名称"},null,8,["value"])]),_:1}),l(d,{label:"代理主机",name:"url",rules:[{required:!0,message:"请输入代理主机"}]},{default:s(()=>[l(u,{value:a(t).url,"onUpdate:value":e[9]||(e[9]=n=>a(t).url=n),placeholder:"代理主机"},null,8,["value"])]),_:1}),l(d,{label:"代理端口",name:"port",rules:[{required:!0,message:"请输入代理端口"}]},{default:s(()=>[l(u,{value:a(t).port,"onUpdate:value":e[10]||(e[10]=n=>a(t).port=n),placeholder:"代理端口"},null,8,["value"])]),_:1}),l(d,{label:"用户名",name:"user",rules:[{required:!0,message:"请输入用户名"}]},{default:s(()=>[l(u,{value:a(t).user,"onUpdate:value":e[11]||(e[11]=n=>a(t).user=n),placeholder:"用户名"},null,8,["value"]),e[23]||(e[23]=p("p",{style:{"margin-top":"6px","font-size":"12px",color:"red"}}," 用户名可以包括变量：{country}代替小写国家，{COUNTRY}代表大写的国家，{randstring:10}表示随机字符串，10的部分可以自定义字符串的长度 ",-1))]),_:1}),l(d,{label:"密码",name:"pass",rules:[{required:!0,message:"请输入密码"}]},{default:s(()=>[l(u,{value:a(t).pass,"onUpdate:value":e[12]||(e[12]=n=>a(t).pass=n),placeholder:"密码"},null,8,["value"])]),_:1}),l(d,{label:"状态",name:"status"},{default:s(()=>[l(U,{checked:a(t).status,"onUpdate:checked":e[13]||(e[13]=n=>a(t).status=n)},null,8,["checked"])]),_:1}),l(d,{label:"权重",name:"rate",rules:[{required:!0,message:"请输入权重"}]},{default:s(()=>[l(h,{value:a(t).rate,"onUpdate:value":e[14]||(e[14]=n=>a(t).rate=n),placeholder:"请输入权重",min:1,max:100,style:{width:"100%"}},null,8,["value"])]),_:1}),l(d,{label:"备注",name:"note"},{default:s(()=>[l(w,{value:a(t).note,"onUpdate:value":e[15]||(e[15]=n=>a(t).note=n),placeholder:"备注",rows:2},null,8,["value"])]),_:1}),l(d,{"wrapper-col":{offset:5,span:17}},{default:s(()=>[l(_,{type:"primary","html-type":"submit",loading:a(t).loading},{default:s(()=>e[24]||(e[24]=[y(" 提交 ")])),_:1},8,["loading"])]),_:1})]),_:1},8,["model"])]),_:1},8,["open"])],64)}}},ie=A(ae,[["__scopeId","data-v-7aa96a44"]]);export{ie as default};
