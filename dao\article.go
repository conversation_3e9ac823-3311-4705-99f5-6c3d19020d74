package dao

import (
	"rsoc-system-go/model"

	"gorm.io/gorm"
)

type ArticleDAO struct {
	db *gorm.DB
}

func NewArticleDAO(db *gorm.DB) *ArticleDAO {
	return &ArticleDAO{db: db}
}

// Create 创建文章
func (d *ArticleDAO) Create(article *model.Article) error {
	return d.db.Create(article).Error
}

// GetByID 根据ID获取文章
func (d *ArticleDAO) GetByID(id uint) (*model.Article, error) {
	var article model.Article
	err := d.db.First(&article, id).Error
	if err != nil {
		return nil, err
	}
	return &article, nil
}

// List 获取文章列表
func (d *ArticleDAO) List(req *model.ArticleListRequest) ([]model.Article, int64, error) {
	var articles []model.Article
	var total int64

	query := d.db.Model(&model.Article{})

	if req.Status != 0 {
		query = query.Where("status = ?", req.Status)
	}
	if req.AuthorID != 0 {
		query = query.Where("author_id = ?", req.AuthorID)
	}

	err := query.Count(&total).Error
	if err != nil {
		return nil, 0, err
	}

	if req.Page > 0 && req.PageSize > 0 {
		offset := (req.Page - 1) * req.PageSize
		query = query.Offset(offset).Limit(req.PageSize)
	}

	err = query.Order("created_at DESC").Find(&articles).Error
	if err != nil {
		return nil, 0, err
	}

	return articles, total, nil
}

// UpdateStatus 更新文章状态
func (d *ArticleDAO) UpdateStatus(id uint, status int) error {
	return d.db.Model(&model.Article{}).Where("id = ?", id).Update("status", status).Error
}
