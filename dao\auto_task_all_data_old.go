package dao

import (
	"context"
	"rsoc-system-go/model"
	"rsoc-system-go/store"
	"sort"

	"gorm.io/gorm"
)

type AutoTaskAllDataOldDAO struct {
	db  *gorm.DB
	dao *store.DAO
}

func NewAutoTaskAllDataOldDAO(db *gorm.DB) *AutoTaskAllDataOldDAO {
	return &AutoTaskAllDataOldDAO{db: db}
}

func NewAutoTaskAllDataOld(instance store.DBInstance) *AutoTaskAllDataOldDAO {
	return &AutoTaskAllDataOldDAO{dao: store.NewDAO(instance)}
}

// Create 创建记录
func (d *AutoTaskAllDataOldDAO) Create(data *model.AutoTaskAllDataOld) error {
	return d.db.Create(data).Error
}

// Update 更新记录
func (d *AutoTaskAllDataOldDAO) Update(data *model.AutoTaskAllDataOld) error {
	return d.db.Save(data).Error
}

// Delete 删除记录
func (d *AutoTaskAllDataOldDAO) Delete(id int64) error {
	return d.db.Delete(&model.AutoTaskAllDataOld{}, id).Error
}

// GetByID 根据ID获取记录
func (d *AutoTaskAllDataOldDAO) GetByID(id int64) (*model.AutoTaskAllDataOld, error) {
	var data model.AutoTaskAllDataOld
	err := d.db.First(&data, id).Error
	if err != nil {
		return nil, err
	}
	return &data, nil
}

// Page 获取记录列表
func (d *AutoTaskAllDataOldDAO) Page(page, size int) ([]*model.AutoTaskAllDataOld, error) {
	var dataList []*model.AutoTaskAllDataOld
	err := d.db.Offset((page - 1) * size).Limit(size).Find(&dataList).Error
	if err != nil {
		return nil, err
	}
	return dataList, nil
}

// List 获取记录列表
func (d *AutoTaskAllDataOldDAO) List(query *model.AutoTaskAllDataOld, order []string) ([]*model.AutoTaskAllDataOld, error) {
	var dataList []*model.AutoTaskAllDataOld
	db := d.db
	if query != nil {
		db = db.Where(query)
	}
	if len(order) > 0 {
		for _, o := range order {
			db = db.Order(o)
		}
	}
	err := db.Find(&dataList).Error
	if err != nil {
		return nil, err
	}
	return dataList, nil
}

// Count 获取总记录数
func (d *AutoTaskAllDataOldDAO) Count(ctx context.Context) (int64, error) {
	var count int64
	err := d.db.Model(&model.AutoTaskAllDataOld{}).Count(&count).Error
	if err != nil {
		return 0, err
	}
	return count, nil
}

// GetByTaskID 根据TaskID获取记录
func (d *AutoTaskAllDataOldDAO) GetByTaskID(taskID string) (*model.AutoTaskAllDataOld, error) {
	var data model.AutoTaskAllDataOld
	err := d.db.Where("task_id = ?", taskID).First(&data).Error
	if err != nil {
		return nil, err
	}
	return &data, nil
}

// GetByClickID 根据ClickID获取记录
func (d *AutoTaskAllDataOldDAO) GetByClickID(clickID string) (*model.AutoTaskAllDataOld, error) {
	var data model.AutoTaskAllDataOld
	err := d.db.Where("click_id = ?", clickID).First(&data).Error
	if err != nil {
		return nil, err
	}
	return &data, nil
}

type Result struct {
	AddTime string `gorm:"column:addtime"`
	Number  string `gorm:"column:number"`
}

// GetHourlyCountByTaskID 根据TaskID获取指定时间范围内的每小时数据统计
func (d *AutoTaskAllDataOldDAO) GetHourlyCountByTaskID(date string) ([]map[string]string, error) {

	var results []Result
	var db *gorm.DB
	db = d.db
	if db == nil {
		db = d.dao.DB()
	}
	err := db.Raw(`
		SELECT 
			DATE_FORMAT(add_time, '%H:%i:%s') as addtime,
			COUNT(1) as number 
		FROM auto_task_all_data_old 
		WHERE date = ? 
		GROUP BY addtime`,
		date,
	).
		Scan(&results).Error

	if err != nil {
		return nil, err
	}
	maps := make([]map[string]string, len(results))
	// 根据key排序
	sort.Slice(maps, func(i, j int) bool {
		return maps[i]["addtime"] < maps[j]["addtime"]
	})
	// 转换为map格式 根据key排序
	for i, r := range results {
		maps[i] = map[string]string{
			"addtime": r.AddTime,
			"number":  r.Number,
		}
	}

	return maps, nil

}

// GetAutoTaskAllDataOldDate 获取所有日期 TODO 查询老数据库
func (d *AutoTaskAllDataOldDAO) GetAutoTaskAllDataOldDate() ([]string, error) {
	//	select date from auto_task_all_data_old GROUP BY date
	var results []string
	var db *gorm.DB
	db = d.db
	if db == nil {
		db = d.dao.DB()
	}
	err := db.Raw(`select date from auto_task_all_data_old GROUP BY date`).
		Scan(&results).Error

	if err != nil {
		return nil, err
	}

	return results, nil
}
