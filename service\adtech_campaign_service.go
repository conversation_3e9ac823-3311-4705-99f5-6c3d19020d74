package service

import (
	"context"
	"encoding/json"
	"fmt"
	"log"
	"net/url"
	"rsoc-system-go/model"
	"sync"
)

// AdTechCampaignService AdTech广告系列服务
type AdTechCampaignService struct {
	httpClient *AdTechHTTPClient
}

var (
	adTechCampaignService     *AdTechCampaignService
	adTechCampaignServiceOnce sync.Once
)

// GetAdTechCampaignService 获取AdTech广告系列服务单例
func GetAdTechCampaignService() *AdTechCampaignService {
	adTechCampaignServiceOnce.Do(func() {
		adTechCampaignService = NewAdTechCampaignService(GetAdTechHTTPClient())
	})
	return adTechCampaignService
}

// NewAdTechCampaignService 创建新的AdTech广告系列服务
func NewAdTechCampaignService(httpClient *AdTechHTTPClient) *AdTechCampaignService {
	return &AdTechCampaignService{
		httpClient: httpClient,
	}
}

// CreateCampaign 创建广告系列
func (s *AdTechCampaignService) CreateCampaign(ctx context.Context, request *model.AdTechCreateCampaignRequest) (*model.AdTechCreateCampaignResponse, error) {
	// 将请求转换为表单数据格式
	publishDomainName := request.PublishDomainName

	// 构建article表单数据
	article := map[string]interface{}{
		"topics":     request.Article.Topics,
		"type":       request.Article.Type,
		"country":    request.Article.Country,
		"locale":     request.Article.Locale,
		"title":      request.Article.Title,
		"excerpt":    request.Article.Excerpt,
		"categoryId": request.Article.CategoryID,
	}

	// 构建campaign表单数据
	trackingSettings := map[string]interface{}{
		"type":                    request.Campaign.TrackingData.TrackingSettings.Type,
		"s2sMetaToken":            request.Campaign.TrackingData.TrackingSettings.S2sMetaToken,
		"s2sMetaPixelId":          request.Campaign.TrackingData.TrackingSettings.S2sMetaPixelId,
		"s2sMetaLandingPageEvent": request.Campaign.TrackingData.TrackingSettings.S2sMetaLandingPageEvent,
		"s2sMetaClickEvent":       request.Campaign.TrackingData.TrackingSettings.S2sMetaClickEvent,
		"s2sMetaSearchEvent":      request.Campaign.TrackingData.TrackingSettings.S2sMetaSearchEvent,
	}

	trackingData := map[string]interface{}{
		"trafficSource":    request.Campaign.TrackingData.TrafficSource,
		"trackingMethod":   request.Campaign.TrackingData.TrackingMethod,
		"trackingSettings": trackingSettings,
	}

	campaign := map[string]interface{}{
		"name":         request.Campaign.Name,
		"type":         request.Campaign.Type,
		"trackingData": trackingData,
	}

	// 发送请求
	resp, err := s.httpClient.CreateCampaignWithFormData(ctx, publishDomainName, article, campaign)
	if err != nil {
		return nil, fmt.Errorf("创建广告系列请求失败: %v", err)
	}

	// 解析响应
	var response model.AdTechCreateCampaignResponse
	if err := s.httpClient.ParseResponse(resp, &response); err != nil {
		return nil, fmt.Errorf("解析创建广告系列响应失败: %v", err)
	}

	return &response, nil
}

// GetCampaign 查询广告系列状态
func (s *AdTechCampaignService) GetCampaign(ctx context.Context, key string, campaignID string) (*model.AdTechCampaignViewResponse, error) {
	// 构建查询参数
	query := url.Values{}
	query.Set("id", campaignID)
	s.httpClient.securityKey = key
	// 发送请求
	resp, err := s.httpClient.Get(ctx, "/campaign/view", query)
	if err != nil {
		return nil, fmt.Errorf("查询广告系列状态请求失败: %v", err)
	}

	// 首先解析为通用响应格式
	var apiResponse model.AdTechAPIResponse
	if err := s.httpClient.ParseResponse(resp, &apiResponse); err != nil {
		return nil, fmt.Errorf("解析API响应失败: %v", err)
	}

	// 检查响应状态
	if apiResponse.Code != 200 {
		return &model.AdTechCampaignViewResponse{
			Success: false,
			Message: fmt.Sprintf("API错误: %s (代码: %d)", apiResponse.Msg, apiResponse.Code),
		}, nil
	}

	// 将data字段解析为AdTechCampaignDetail
	var campaignDetail model.AdTechCampaignDetail
	dataBytes, err := json.Marshal(apiResponse.Data)
	if err != nil {
		return nil, fmt.Errorf("序列化数据失败: %v", err)
	}

	if err := json.Unmarshal(dataBytes, &campaignDetail); err != nil {
		return nil, fmt.Errorf("解析广告系列详情失败: %v", err)
	}
	log.Println(string(dataBytes))
	// 构建响应
	response := &model.AdTechCampaignViewResponse{
		Success: true,
		Message: apiResponse.Msg,
		Detail:  &campaignDetail,
		// 填充兼容性字段
		CampaignID:  campaignDetail.ID,
		Status:      campaignDetail.Status,
		TrackingUrl: campaignDetail.TrackingUrl,
	}

	return response, nil
}

// GetDomains 获取可用于发布文章的域名列表
func (s *AdTechCampaignService) GetDomains(ctx context.Context, key string) ([]model.AdTechDomain, error) {
	// 发送请求
	s.httpClient.securityKey = key
	resp, err := s.httpClient.Get(ctx, "/campaign/domains", nil)
	if err != nil {
		return nil, fmt.Errorf("获取域名列表请求失败: %v", err)
	}

	// 解析响应
	var response model.AdTechDomainsResponse
	if err := s.httpClient.ParseResponse(resp, &response); err != nil {
		return nil, fmt.Errorf("解析域名列表响应失败: %v", err)
	}

	// 检查响应码
	if response.Code != 200 {
		return nil, fmt.Errorf("获取域名列表失败: %s (代码: %d)", response.Msg, response.Code)
	}

	return response.Data, nil
}

// 示例使用方法
// 以下是如何使用该服务的示例代码：
/*
func ExampleCreateCampaign() {
	// 创建请求
	request := &model.AdTechCreateCampaignRequest{
		PublishDomainName: "example.com",
		Article: model.AdTechArticle{
			Topics:     []string{"Summer vacation", "All inclusive resort in Thailand", "Cheap flights from USA"},
			Type:       "CreateArticle",
			Country:    "US",
			Locale:     "en-US",
			Title:      "Summer vacation",
			Excerpt:    "The best summer vacation deals",
			CategoryID: "2e5c8fbb-f078-498b-82e5-d45263e21f67",
		},
		Campaign: model.AdTechCampaign{
			Name: "summer vacation",
			Type: "CreateCampaign",
			TrackingData: model.AdTechTrackingData{
				TrafficSource:  "META",
				TrackingMethod: "S2S",
				TrackingSettings: model.AdTechTrackingSettings{
					Type:                   "S2sMetaTrackingSettings",
					S2sMetaToken:           "xxxxxxxxxxxxxx",
					S2sMetaPixelId:         "1205376682832142",
					S2sMetaLandingPageEvent: "Lead",
					S2sMetaClickEvent:      "Search",
					S2sMetaSearchEvent:     "Purchase",
				},
			},
		},
	}

	// 获取服务实例
	service := GetAdTechCampaignService()

	// 设置安全密钥（如果需要）
	service.httpClient.SetSecurityKey("your_security_key")

	// 调用服务
	response, err := service.CreateCampaign(context.Background(), request)
	if err != nil {
		log.Fatalf("创建广告系列失败: %v", err)
	}

	// 处理响应
	fmt.Printf("创建成功: %v, 消息: %s, 资源ID: %s\n", response.Success, response.Message, response.ResourceID)
}

// ExampleGetCampaign 示例：查询广告系列状态
func ExampleGetCampaign() {
	// 获取服务实例
	service := GetAdTechCampaignService()

	// 设置安全密钥（如果需要）
	service.httpClient.SetSecurityKey("your_security_key")

	// 查询广告系列状态
	campaignID := "a4xxxf-1180-4238-95xxxx44ea" // 示例ID
	response, err := service.GetCampaign(context.Background(), campaignID)
	if err != nil {
		log.Fatalf("查询广告系列状态失败: %v", err)
	}

	// 处理响应
	fmt.Printf("查询成功: %v, 状态: %s, 投放链接: %s\n", response.Success, response.Status, response.LandingURL)
}

// ExampleGetDomains 示例：获取可用域名列表
func ExampleGetDomains() {
	// 获取服务实例
	service := GetAdTechCampaignService()

	// 设置安全密钥（如果需要）
	service.httpClient.SetSecurityKey("your_security_key")

	// 获取域名列表
	domains, err := service.GetDomains(context.Background())
	if err != nil {
		log.Fatalf("获取域名列表失败: %v", err)
	}

	// 处理响应
	fmt.Printf("获取到 %d 个可用域名\n", len(domains))
	for i, domain := range domains {
		fmt.Printf("%d. 域名: %s, URL: %s\n", i+1, domain.Domain, domain.DomainURL)
	}

	// 在创建广告系列时可以使用这些域名
	if len(domains) > 0 {
		fmt.Printf("可以使用域名 %s 来创建广告系列\n", domains[0].Domain)
	}
}
*/
