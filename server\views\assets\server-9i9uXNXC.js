import{_ as O,u as T,o as w,r as f,a as z,M as o,c as U,d as k,w as u,e as m,f as d,m as _,n as g,I as B,B as E,h as j,s as y}from"./index-DlVegDiC.js";import{_ as J}from"./index-1uCBjWky.js";import{_ as M}from"./index-B8PO_1fg.js";import{_ as V}from"./index-BrFZluVG.js";import"./index-CSU5nP3m.js";const D={class:"main"},L={__name:"server",setup(R){const l="",r=T(),v=z();w(()=>{c()});const a=f({data:[],loading:!1}),s=f({total:0,current:1,pageSize:10,position:["bottomCenter"],showSizeChanger:!0,pageSizeOptions:["10","20","30"],showTotal:e=>`共 ${e} 项`}),c=()=>{a.loading=!0,fetch(`${l}/server/list`,{method:"POST",body:JSON.stringify({page:s.current,limit:s.pageSize,directoryStatus:1}),headers:{token:r.token}}).then(e=>e.json()).then(e=>{console.log(e),e.code==1?(a.data=e.data.data,s.total=Number(e.data.total)):e.code==3e3?(r.$patch({token:!1}),v.push("/login"),o.error({title:e.msg})):(a.data=[],o.error({title:e.msg})),a.loading=!1}).catch(e=>{o.error({title:"服务器错误",content:`${e}`})})},S=e=>{s.current=e.current,s.pageSize=e.pageSize,c()},h=e=>{fetch(`${l}/server/update`,{method:"POST",body:JSON.stringify({id:e.id,num:e.num,note:e.note}),headers:{token:r.token}}).then(t=>t.json()).then(t=>{console.log(t),t.code==1?(y.success(t.msg),c()):o.error({title:t.msg})}).catch(t=>{o.error({title:"服务器错误",content:`${t}`})})},b=e=>{fetch(`${l}/server/update`,{method:"POST",body:JSON.stringify({id:e.id,status:2}),headers:{token:r.token}}).then(t=>t.json()).then(t=>{console.log(t),t.code==1?(y.success(t.msg),c()):o.error({title:t.msg})}).catch(t=>{o.error({title:"服务器错误",content:`${t}`})})},I=[{title:"ID",dataIndex:"id",key:"id",align:"center"},{title:"备注",dataIndex:"note",key:"note",align:"center"},{title:"服务器IP",dataIndex:"ip",key:"ip",align:"center"},{title:"最大启动数",dataIndex:"num",key:"num",align:"center"},{title:"添加日期",dataIndex:"time",key:"time",align:"center",width:300,ellipsis:!0},{title:"操作",key:"action",align:"center"}];return(e,t)=>{const $=B,x=M,C=E,P=V,N=J;return d(),U("div",D,[k(N,{columns:I,"data-source":m(a).data,rowKey:"id",pagination:m(s),loading:m(a).loading,onChange:S,bordered:""},{bodyCell:u(({column:p,record:n})=>[p.key==="note"?(d(),_($,{key:0,value:n.note,"onUpdate:value":i=>n.note=i,onPressEnter:i=>h(n)},null,8,["value","onUpdate:value","onPressEnter"])):g("",!0),p.key==="num"?(d(),_(x,{key:1,value:n.num,"onUpdate:value":i=>n.num=i,min:0,onPressEnter:i=>h(n)},null,8,["value","onUpdate:value","onPressEnter"])):g("",!0),p.key==="action"?(d(),_(P,{key:2,title:"确认删除？",onConfirm:i=>b(n)},{default:u(()=>[k(C,{type:"link",danger:""},{default:u(()=>t[0]||(t[0]=[j("删除")])),_:1})]),_:2},1032,["onConfirm"])):g("",!0)]),_:1},8,["data-source","pagination","loading"])])}}},H=O(L,[["__scopeId","data-v-459ed2f7"]]);export{H as default};
