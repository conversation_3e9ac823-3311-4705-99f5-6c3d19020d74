import{_ as Ue,u as Oe,o as Te,r as h,a as Fe,M as s,j as Be,c as R,b as k,d as o,w as i,B as Ne,e as n,k as V,f as v,h as y,m as w,n as x,aw as ze,t as K,q as H,v as Q,F as je,g as qe,I as Le,p as De,s as D}from"./index-DlVegDiC.js";import{P as Je}from"./PlusOutlined-Cg2o2XQN.js";import{R as Ee}from"./index-CCw7iZyQ.js";import{P as Ae}from"./Paragraph-DEgIVM3w.js";import{S as Ge,a as Me}from"./index-CSU5nP3m.js";import{_ as Re,a as Ve,S as Ke}from"./index-C0YopvWv.js";import{_ as He}from"./index-1uCBjWky.js";import{_ as Qe}from"./index-B8PO_1fg.js";import{_ as We}from"./index-BrFZluVG.js";import{_ as Xe}from"./index-D_v6jNwB.js";import{_ as Ye}from"./index-DXcpAzs8.js";const Ze={class:"main"},et={class:"filter"},tt={style:{"word-break":"break-all"}},at={class:"info_review"},nt={__name:"list",setup(ot){const f="",c=Oe(),N=Fe();Te(()=>{T()});const $=h({data:[],loading:!1}),C=h({total:0,current:1,pageSize:10,position:["bottomCenter"],showSizeChanger:!0,pageSizeOptions:["10","20","30"],showTotal:e=>`共 ${e} 项`}),T=()=>{$.loading=!0,fetch(`${f}/Task/list`,{method:"POST",body:JSON.stringify({page:C.current,limit:C.pageSize,directoryStatus:1}),headers:{token:c.token}}).then(e=>e.json()).then(e=>{console.log(e),e.code==1?($.data=e.data.data,C.total=Number(e.data.total)):e.code==3e3?(c.$patch({token:!1}),N.push("/login"),s.error({title:e.msg})):($.data=[],s.error({title:e.msg})),$.loading=!1}).catch(e=>{s.error({title:"服务器错误",content:`${e}`})})},W=e=>{C.current=e.current,C.pageSize=e.pageSize,T()},J=Be(),a=h({open:!1,loading:!1,result:{text:"",show:!1},main_directory:[],main_directory_options:[],fbId:void 0,accountId:[],traffic:void 0,traffic_url:void 0,fb_pixel_id:void 0,fb_pixel_api_token:void 0,platform:void 0,platform_id:void 0,hold_rate:void 0,seach_rate:void 0,click1_rate:void 0,click2_rate:void 0,click3_rate:void 0,proxy_id:void 0,proxy_name:void 0,country:"",ref:"https://m.facebook.com",note:"",is_global:!1}),u=h({main_list:[],ads_list:[],ads_url_list:[],sedo_list:[],pixel_list:[],proxy_list:[],fb_ads_list:[]}),X=()=>{a.open=!0,Z(),ne(),te(),ae(),ie()},Y=(e,t)=>{console.log(t),a.main_directory_options=t},Z=()=>{fetch(`${f}/MainDirectory/list`,{method:"POST",body:JSON.stringify({directoryStatus:1,page:1,limit:200}),headers:{token:c.token}}).then(e=>e.json()).then(e=>{console.log(e),u.main_list=e.data.data.map(t=>(t.isLeaf=!1,t))}).catch(e=>{s.error({title:"服务器错误",content:`${e}`})})},ee=e=>{const t=e[e.length-1];t.loading=!0,console.log(t),fetch(`${f}/TwoDirectory/list`,{method:"POST",body:JSON.stringify({mianId:t.id,directoryStatus:1,page:1,limit:200}),headers:{token:c.token}}).then(r=>r.json()).then(r=>{console.log(r),t.loading=!1,t.children=r.data.data,u.main_list=[...u.main_list]}).catch(r=>{s.error({title:"服务器错误",content:`${r}`})})},te=()=>{fetch(`${f}/FaceBook/fbPixelList`,{method:"POST",body:JSON.stringify({directoryStatus:1,page:1,limit:200}),headers:{token:c.token}}).then(e=>e.json()).then(e=>{console.log(e),u.pixel_list=e.data.data}).catch(e=>{s.error({title:"服务器错误",content:`${e}`})})},ae=()=>{fetch(`${f}/proxy/list`,{method:"POST",body:JSON.stringify({directoryStatus:1}),headers:{token:c.token}}).then(e=>e.json()).then(e=>{console.log(e),u.proxy_list=e.data}).catch(e=>{s.error({title:"服务器错误",content:`${e}`})})},ne=()=>{fetch(`${f}/FaceBook/appList`,{method:"POST",headers:{token:c.token}}).then(e=>e.json()).then(e=>{console.log(e),u.ads_list=e.data}).catch(e=>{s.error({title:"服务器错误",content:`${e}`})})},oe=(e,t)=>{e&&le()},le=()=>{fetch(`${f}/FaceBook/fbUrlList`,{method:"POST",body:JSON.stringify({fbId:a.fbId,directoryStatus:1,page:1,limit:200}),headers:{token:c.token}}).then(e=>e.json()).then(e=>{console.log(e),u.ads_url_list=e.data.data}).catch(e=>{s.error({title:"服务器错误",content:`${e}`})})},ie=()=>{fetch(`${f}/FaceBook/adsList`,{method:"POST",body:JSON.stringify({directoryStatus:1,page:1,limit:200}),headers:{token:c.token}}).then(e=>e.json()).then(e=>{console.log(e),u.fb_ads_list=e.data.data.map(t=>(t.label=t.accountName,t.value=t.accountId,t.isLeaf=!1,t))}).catch(e=>{s.error({title:"服务器错误",content:`${e}`})})},re=e=>{const t=e[e.length-1];t.loading=!0,console.log(t),fetch(`${f}/FaceBook/campainList`,{method:"POST",body:JSON.stringify({accountId:t.accountId,directoryStatus:1,page:1,limit:200}),headers:{token:c.token}}).then(r=>r.json()).then(r=>{console.log(r),t.loading=!1,t.children=r.data.data.map(m=>(m.label=m.campaignName,m.value=m.campaignId,m)),u.fb_ads_list=[...u.fb_ads_list]}).catch(r=>{s.error({title:"服务器错误",content:`${r}`})})},se=e=>{e&&(a.platform_id=void 0,u.sedo_list=[],de(e))},de=e=>{let t;e=="sedo"?t=`${f}/SeDo/list`:t=`${f}/SeDo2/list`,fetch(t,{method:"POST",headers:{token:c.token}}).then(r=>r.json()).then(r=>{console.log(r),u.sedo_list=r.data}).catch(r=>{s.error({title:"服务器错误",content:`${r}`})})},ce=(e,t)=>{e&&(console.log(t),a.fb_pixel_api_token=t.api_token)},ue=()=>{if(!a.main_directory[1])return s.error({title:"请选择关键词二级目录"}),!1;if(!a.accountId[1])return s.error({title:"请选择Campaign"}),!1;a.loading=!0,fetch(`${f}/Task/add`,{method:"POST",body:JSON.stringify({two_directory:a.main_directory[1],fbId:a.fbId,fb_ads_account:a.accountId[0],campaignId:a.accountId[1],traffic:a.traffic,traffic_url:a.traffic_url,fb_pixel_id:a.fb_pixel_id,fb_pixel_api_token:a.fb_pixel_api_token,platform:a.platform,platform_id:a.platform_id,hold_rate:a.hold_rate,seach_rate:a.seach_rate,click1_rate:a.click1_rate,click2_rate:a.click2_rate,click3_rate:a.click3_rate,proxy_id:a.proxy_id,proxy_name:a.main_directory_options[1].name,country:a.country,is_global:a.is_global?"1":"0",note:a.note}),headers:{token:c.token}}).then(e=>e.json()).then(e=>{console.log(e),a.result.text=e.data.url,e.code==1?(D.success("添加成功"),T(),J.value.resetFields()):s.error({title:e.msg}),a.result.show=!0,a.loading=!1}).catch(e=>{s.error({title:"服务器错误",content:`${e}`})})},b=h({open:!1,values:{},task_id:0}),_e=e=>{b.open=!0,console.log(e),b.values=e,S.current=1,E()},P=h({data:[],loading:!1}),S=h({total:0,current:1,pageSize:10,position:["bottomCenter"],showSizeChanger:!0,pageSizeOptions:["10","20","30"],showTotal:e=>`共 ${e} 项`}),E=()=>{P.loading=!0,fetch(`${f}/Task/autoTask`,{method:"POST",body:JSON.stringify({task_id:b.values.task_id,page:S.current,limit:S.pageSize,directoryStatus:1}),headers:{token:c.token}}).then(e=>e.json()).then(e=>{console.log(e),e.code==1?(P.data=e.data.data,S.total=Number(e.data.total)):e.code==3e3?(c.$patch({token:!1}),N.push("/login"),s.error({title:e.msg})):(P.data=[],s.error({title:e.msg})),P.loading=!1}).catch(e=>{s.error({title:"服务器错误",content:`${e}`})})},pe=e=>{S.current=e.current,S.pageSize=e.pageSize,E()},F=h({open:!1,click_id:0}),fe=e=>{F.open=!0,console.log(e),F.click_id=e.click_id,I.current=1,A()},U=h({data:[],loading:!1}),I=h({total:0,current:1,pageSize:10,position:["bottomCenter"],showSizeChanger:!0,pageSizeOptions:["10","20","30"],showTotal:e=>`共 ${e} 项`}),A=()=>{U.loading=!0,fetch(`${f}/Task/getAutoTaskLog`,{method:"POST",body:JSON.stringify({click_id:F.click_id,page:I.current,limit:I.pageSize,directoryStatus:1}),headers:{token:c.token}}).then(e=>e.json()).then(e=>{console.log(e),e.code==1?(U.data=e.data.data,I.total=Number(e.data.total)):e.code==3e3?(c.$patch({token:!1}),N.push("/login"),s.error({title:e.msg})):(U.data=[],s.error({title:e.msg})),U.loading=!1}).catch(e=>{s.error({title:"服务器错误",content:`${e}`})})},me=e=>{I.current=e.current,I.pageSize=e.pageSize,A()},B=(e,t)=>{console.log(e),console.log(t),fetch(`${f}/Task/update`,{method:"POST",body:JSON.stringify({id:t.id,[e]:t[e]}),headers:{token:c.token}}).then(r=>r.json()).then(r=>{console.log(r),r.code==1?(D.success(r.msg),T()):s.error({title:r.msg})}).catch(r=>{s.error({title:"服务器错误",content:`${r}`})})},ge=e=>{fetch(`${f}/Task/update`,{method:"POST",body:JSON.stringify({id:e.id,status:2}),headers:{token:c.token}}).then(t=>t.json()).then(t=>{console.log(t),t.code==1?(D.success(t.msg),T()):s.error({title:t.msg})}).catch(t=>{s.error({title:"服务器错误",content:`${t}`})})},he=[{title:"ID",dataIndex:"id",key:"id",align:"center"},{title:"全球流量",dataIndex:"is_global",key:"is_global",align:"center"},{title:"二级目录",dataIndex:"proxy_name",key:"proxy_name",align:"center"},{title:"流量源",dataIndex:"traffic",key:"traffic",align:"center"},{title:"FB投放域名",dataIndex:"traffic_url",key:"traffic_url",align:"center"},{title:"拦截比例",dataIndex:"hold_rate",key:"hold_rate",align:"center"},{title:"搜索比例",dataIndex:"seach_rate",key:"seach_rate",align:"center"},{title:"点广告概率",dataIndex:"click1_rate",key:"click1_rate",align:"center"},{title:"二次点广告概率",dataIndex:"click2_rate",key:"click2_rate",align:"center"},{title:"三次点广告概率",dataIndex:"click3_rate",key:"click3_rate",align:"center"},{title:"投放链接",dataIndex:"put_url",key:"put_url",align:"center"},{title:"操作",key:"action",align:"center"}],ke=[{title:"ID",dataIndex:"id",key:"id",align:"center",width:70,fixed:"left"},{title:"日期",dataIndex:"add_time",key:"add_time",align:"center",width:150},{title:"链接",dataIndex:"traffic_url",key:"traffic_url",align:"center",width:200,ellipsis:!0},{title:"浏览器参数",dataIndex:"viewport",key:"viewport",align:"center",width:200,ellipsis:!0},{title:"UserAgent",dataIndex:"useragent",key:"useragent",align:"center",width:200,ellipsis:!0},{title:"语言",dataIndex:"lang",key:"lang",align:"center",width:100,ellipsis:!0},{title:"IP",dataIndex:"user_ip",key:"user_ip",align:"center",width:200,ellipsis:!0},{title:"操作",key:"action",align:"center",fixed:"right",width:80}],ve=[{title:"ID",dataIndex:"id",key:"id",align:"center",width:70,fixed:"left"},{title:"日期",dataIndex:"add_time",key:"add_time",align:"center",width:150},{title:"信息",dataIndex:"note",key:"note",align:"center",width:200,ellipsis:!0},{title:"详情",dataIndex:"info",key:"info",align:"center",width:200,ellipsis:!0}];return(e,t)=>{const r=Je,m=Ne,g=Qe,ye=ze,be=We,z=He,xe=Ee,Se=Ae,G=Xe,_=qe,M=Me,O=Ge,Ie=Le,we=Ye,$e=De,Ce=je,j=s,q=Ke,L=Ve,Pe=Re;return v(),R(V,null,[k("div",Ze,[k("div",null,[k("div",et,[t[20]||(t[20]=k("div",{class:"filter_item"},null,-1)),k("div",null,[o(m,{type:"primary",onClick:X},{icon:i(()=>[o(r)]),default:i(()=>[t[19]||(t[19]=y(" 添加任务 "))]),_:1})])])]),o(z,{columns:he,"data-source":n($).data,rowKey:"id",pagination:n(C),loading:n($).loading,onChange:W,bordered:""},{bodyCell:i(({column:l,record:d})=>[l.key==="hold_rate"?(v(),w(g,{key:0,value:d.hold_rate,"onUpdate:value":p=>d.hold_rate=p,min:1,max:100,onPressEnter:p=>B("hold_rate",d)},null,8,["value","onUpdate:value","onPressEnter"])):x("",!0),l.key==="seach_rate"?(v(),w(g,{key:1,value:d.seach_rate,"onUpdate:value":p=>d.seach_rate=p,min:1,max:100,onPressEnter:p=>B("seach_rate",d)},null,8,["value","onUpdate:value","onPressEnter"])):x("",!0),l.key==="click1_rate"?(v(),w(g,{key:2,value:d.click1_rate,"onUpdate:value":p=>d.click1_rate=p,min:1,max:100,onPressEnter:p=>B("click1_rate",d)},null,8,["value","onUpdate:value","onPressEnter"])):x("",!0),l.key==="click2_rate"?(v(),w(g,{key:3,value:d.click2_rate,"onUpdate:value":p=>d.click2_rate=p,min:1,max:100,onPressEnter:p=>B("click2_rate",d)},null,8,["value","onUpdate:value","onPressEnter"])):x("",!0),l.key==="click3_rate"?(v(),w(g,{key:4,value:d.click3_rate,"onUpdate:value":p=>d.click3_rate=p,min:1,max:100,onPressEnter:p=>B("click3_rate",d)},null,8,["value","onUpdate:value","onPressEnter"])):x("",!0),l.key==="put_url"?(v(),w(ye,{key:5},{title:i(()=>[k("div",tt,K(d.put_url),1)]),default:i(()=>[o(m,{type:"link"},{default:i(()=>t[21]||(t[21]=[y("查看投放链接")])),_:1})]),_:2},1024)):x("",!0),l.key==="action"?(v(),R(V,{key:6},[o(m,{type:"link",onClick:p=>_e(d)},{default:i(()=>t[22]||(t[22]=[y(" 任务详情 ")])),_:2},1032,["onClick"]),o(be,{title:"确认删除？",onConfirm:p=>ge(d)},{default:i(()=>[o(m,{type:"link",danger:""},{default:i(()=>t[23]||(t[23]=[y("删除")])),_:1})]),_:2},1032,["onConfirm"])],64)):x("",!0)]),_:1},8,["data-source","pagination","loading"])]),o(j,{open:n(a).open,"onUpdate:open":t[16]||(t[16]=l=>n(a).open=l),title:"添加任务",footer:null,maskClosable:!1,width:600},{default:i(()=>[t[27]||(t[27]=k("div",{style:{height:"20px"}},null,-1)),H(k("div",null,[o(xe,{status:"success",title:"生成的广告链接",class:"result_icon"}),o(Se,{copyable:"",class:"result_text"},{default:i(()=>[y(K(n(a).result.text),1)]),_:1})],512),[[Q,n(a).result.show]]),H(k("div",null,[o(Ce,{ref_key:"add_form",ref:J,model:n(a),onFinish:ue,"label-col":{span:6},"wrapper-col":{span:16}},{default:i(()=>[o(_,{label:"关键词",name:"main_directory",rules:[{required:!0,message:"请选择关键词"}]},{default:i(()=>[o(G,{value:n(a).main_directory,"onUpdate:value":t[0]||(t[0]=l=>n(a).main_directory=l),options:n(u).main_list,"load-data":ee,placeholder:"请选择关键词","change-on-select":"","field-names":{label:"name",value:"id"},onChange:Y},null,8,["value","options"])]),_:1}),o(_,{label:"流量源",name:"traffic",rules:[{required:!0,message:"请选择流量源"}]},{default:i(()=>[o(O,{value:n(a).traffic,"onUpdate:value":t[1]||(t[1]=l=>n(a).traffic=l),placeholder:"请选择流量源"},{default:i(()=>[o(M,{value:"facebook"},{default:i(()=>t[24]||(t[24]=[y("facebook")])),_:1})]),_:1},8,["value"])]),_:1}),o(_,{label:"FB总账号",name:"fbId",rules:[{required:!0,message:"请选择FB总账号"}]},{default:i(()=>[o(O,{value:n(a).fbId,"onUpdate:value":t[2]||(t[2]=l=>n(a).fbId=l),placeholder:"请选择FB总账号",options:n(u).ads_list,"field-names":{label:"note",value:"id"},onChange:oe},null,8,["value","options"])]),_:1}),o(_,{label:"FB广告账户",name:"accountId",rules:[{required:!0,message:"请选择FB广告账户"}]},{default:i(()=>[o(G,{value:n(a).accountId,"onUpdate:value":t[3]||(t[3]=l=>n(a).accountId=l),options:n(u).fb_ads_list,"load-data":re,placeholder:"请选择FB广告账户","change-on-select":""},null,8,["value","options"])]),_:1}),o(_,{label:"FB投放域名",name:"traffic_url",rules:[{required:!0,message:"请选择FB投放域名"}]},{default:i(()=>[o(O,{value:n(a).traffic_url,"onUpdate:value":t[4]||(t[4]=l=>n(a).traffic_url=l),placeholder:"请选择FB投放域名",options:n(u).ads_url_list,"field-names":{label:"url",value:"url"}},null,8,["value","options"])]),_:1}),o(_,{label:"FB像素",name:"fb_pixel_id",rules:[{required:!0,message:"请选择FB像素"}]},{default:i(()=>[o(O,{value:n(a).fb_pixel_id,"onUpdate:value":t[5]||(t[5]=l=>n(a).fb_pixel_id=l),placeholder:"请选择FB像素",options:n(u).pixel_list,"field-names":{label:"pixel_name",value:"pixel_id"},onChange:ce},null,8,["value","options"])]),_:1}),o(_,{label:"平台",name:"platform",rules:[{required:!0,message:"请选择平台"}]},{default:i(()=>[o(O,{value:n(a).platform,"onUpdate:value":t[6]||(t[6]=l=>n(a).platform=l),placeholder:"请选择平台",onChange:se},{default:i(()=>[o(M,{value:"sedo"},{default:i(()=>t[25]||(t[25]=[y("SeDo")])),_:1})]),_:1},8,["value"])]),_:1}),o(_,{label:"平台账户",name:"platform_id",rules:[{required:!0,message:"请选择平台账户"}]},{default:i(()=>[o(O,{value:n(a).platform_id,"onUpdate:value":t[7]||(t[7]=l=>n(a).platform_id=l),placeholder:"请选择平台账户",options:n(u).sedo_list,"field-names":{label:"note",value:"id"}},null,8,["value","options"])]),_:1}),o(_,{label:"拦截比例",name:"hold_rate",rules:[{required:!0,message:"请输入拦截比例"}]},{default:i(()=>[o(g,{value:n(a).hold_rate,"onUpdate:value":t[8]||(t[8]=l=>n(a).hold_rate=l),"addon-after":"%",placeholder:"请输入拦截比例",min:1,max:100},null,8,["value"])]),_:1}),o(_,{label:"搜索概率",name:"seach_rate",rules:[{required:!0,message:"请输入搜索概率"}]},{default:i(()=>[o(g,{value:n(a).seach_rate,"onUpdate:value":t[9]||(t[9]=l=>n(a).seach_rate=l),"addon-after":"%",placeholder:"请输入搜索概率",min:1,max:100},null,8,["value"])]),_:1}),o(_,{label:"点广告概率",name:"click1_rate",rules:[{required:!0,message:"请输入点广告概率"}]},{default:i(()=>[o(g,{value:n(a).click1_rate,"onUpdate:value":t[10]||(t[10]=l=>n(a).click1_rate=l),"addon-after":"%",placeholder:"请输入点广告概率",min:1,max:100},null,8,["value"])]),_:1}),o(_,{label:"二次点广告概率",name:"click2_rate",rules:[{required:!0,message:"请输入二次点广告概率"}]},{default:i(()=>[o(g,{value:n(a).click2_rate,"onUpdate:value":t[11]||(t[11]=l=>n(a).click2_rate=l),"addon-after":"%",placeholder:"请输入二次点广告概率",min:1,max:100},null,8,["value"])]),_:1}),o(_,{label:"三次点广告概率",name:"click3_rate",rules:[{required:!0,message:"请输入三次点广告概率"}]},{default:i(()=>[o(g,{value:n(a).click3_rate,"onUpdate:value":t[12]||(t[12]=l=>n(a).click3_rate=l),"addon-after":"%",placeholder:"请输入三次点广告概率",min:1,max:100},null,8,["value"])]),_:1}),o(_,{label:"国家",name:"country",rules:[{required:!0,message:"请输入国家"}]},{default:i(()=>[o(Ie,{value:n(a).country,"onUpdate:value":t[13]||(t[13]=l=>n(a).country=l),placeholder:"国家简写大写，比如US,CA,CN"},null,8,["value"])]),_:1}),o(_,{label:"全球流量",name:"is_global"},{default:i(()=>[o(we,{checked:n(a).is_global,"onUpdate:checked":t[14]||(t[14]=l=>n(a).is_global=l),"checked-children":"开","un-checked-children":"关"},null,8,["checked"])]),_:1}),o(_,{label:"备注",name:"note"},{default:i(()=>[o($e,{value:n(a).note,"onUpdate:value":t[15]||(t[15]=l=>n(a).note=l),placeholder:"备注",rows:2},null,8,["value"])]),_:1}),o(_,{"wrapper-col":{offset:6,span:16}},{default:i(()=>[o(m,{type:"primary","html-type":"submit",loading:n(a).loading},{default:i(()=>t[26]||(t[26]=[y(" 提交 ")])),_:1},8,["loading"])]),_:1})]),_:1},8,["model"])],512),[[Q,!n(a).result.show]])]),_:1},8,["open"]),o(j,{open:n(b).open,"onUpdate:open":t[17]||(t[17]=l=>n(b).open=l),title:"任务详情",footer:null,maskClosable:!1,width:900},{default:i(()=>[k("div",at,[o(Pe,null,{default:i(()=>[o(L,{span:4},{default:i(()=>[o(q,{title:"任务总数",value:n(b).values.all_num},null,8,["value"])]),_:1}),o(L,{span:4},{default:i(()=>[o(q,{title:"打开数量",value:n(b).values.all_view},null,8,["value"])]),_:1}),o(L,{span:4},{default:i(()=>[o(q,{title:"点击数量",value:n(b).values.all_click1},null,8,["value"])]),_:1})]),_:1})]),o(z,{columns:ke,"data-source":n(P).data,rowKey:"id",pagination:n(S),loading:n(P).loading,onChange:pe,bordered:"",scroll:{x:1500,y:400},size:"small"},{bodyCell:i(({column:l,record:d})=>[l.key==="action"?(v(),w(m,{key:0,type:"link",onClick:p=>fe(d)},{default:i(()=>t[28]||(t[28]=[y(" 查看日志 ")])),_:2},1032,["onClick"])):x("",!0)]),_:1},8,["data-source","pagination","loading"])]),_:1},8,["open"]),o(j,{open:n(F).open,"onUpdate:open":t[18]||(t[18]=l=>n(F).open=l),title:"任务日志",footer:null,maskClosable:!1,width:900},{default:i(()=>[o(z,{columns:ve,"data-source":n(U).data,rowKey:"id",pagination:n(I),loading:n(U).loading,onChange:me,bordered:"",size:"small"},null,8,["data-source","pagination","loading"])]),_:1},8,["open"])],64)}}},gt=Ue(nt,[["__scopeId","data-v-6a556c75"]]);export{gt as default};
