package handler

import (
	"net/http"
	"rsoc-system-go/model"
	"rsoc-system-go/service"
	"strconv"

	"github.com/gin-gonic/gin"
)

// CategoryHandler 分类处理器
type CategoryHandler struct {
	categoryService *service.SedoCategoryService
}

// NewCategoryHandler 创建分类处理器
func NewCategoryHandler(categoryService *service.SedoCategoryService) *CategoryHandler {

	return &CategoryHandler{
		categoryService: categoryService,
	}
}

// GetCategories 获取分类列表
func (h *CategoryHandler) GetCategories(c *gin.Context) {
	// 获取查询参数
	page, _ := strconv.Atoi(c.DefaultQuery("page", "0"))
	size, _ := strconv.Atoi(c.DefaultQuery("size", "10"))
	term := c.Query("term")

	// 调用服务获取分类列表
	categories, pageResponse, err := h.categoryService.GetCategories(c.Request.Context(), page, size, term)
	if err != nil {
		c.J<PERSON>(http.StatusInternalServerError, gin.H{"error": "获取分类列表失败: " + err.Error()})
		return
	}

	// 返回分类列表
	c.JSON(http.StatusOK, gin.H{
		"data":      categories,
		"total":     pageResponse.TotalCount,
		"page":      page,
		"page_size": size,
	})
}

// CreateCategory 创建分类
func (h *CategoryHandler) CreateCategory(c *gin.Context) {
	// 从请求体中绑定分类请求
	var request model.SedoCreateCategoryRequest
	if err := c.ShouldBindJSON(&request); err != nil {
		c.JSON(http.StatusBadRequest, gin.H{"error": "无效的请求数据: " + err.Error()})
		return
	}

	// 调用服务创建分类
	category, err := h.categoryService.CreateCategory(c.Request.Context(), &request)
	if err != nil {
		c.JSON(http.StatusInternalServerError, gin.H{"error": "创建分类失败: " + err.Error()})
		return
	}

	// 返回创建成功的分类
	c.JSON(http.StatusCreated, gin.H{"data": category})
}
