# Postback 回调接口测试数据

## 概述

本文档提供了用于测试 postback 回调接口的各种测试数据，包括通用回调接口和 SedoTMP 专用回调接口的测试用例。

## 通用回调接口测试数据 (`/rsoc/postback`)

### 测试用例1：AdTech 格式 - 美国桌面端用户
```bash
curl "http://localhost:8080/rsoc/postback?campaign=ADT001&click_id=us_desktop_001&payout=2.50&country=US&zip=10001&os_type=WINDOWS&browser=CHROME&device_type=DESKTOP&device_brand=DELL&s1=AdSetUS001&s2=CreativeDesktop&s3=AudienceRetargeting"
```

### 测试用例2：AdTech 格式 - 英国移动端用户
```bash
curl "http://localhost:8080/rsoc/postback?campaign=ADT002&click_id=uk_mobile_001&payout=1.75&country=GB&zip=SW1A&os_type=ANDROID&browser=CHROME&device_type=MOBILE&device_brand=SAMSUNG&s1=AdSetUK001&s2=CreativeMobile&s3=AudienceLookalike"
```

### 测试用例3：混合格式 - 加拿大用户
```bash
curl "http://localhost:8080/rsoc/postback?campaign=MIX001&click_id=ca_user_001&payout=3.20&country=CA&zip=M5V&os_type=MACOS&browser=SAFARI&device_type=DESKTOP&device_brand=APPLE&s1=AdSetCA001&subid1=TestSubID1&subid2=TestSubID2"
```

### 测试用例4：POST 方式提交
```bash
curl -X POST "http://localhost:8080/rsoc/postback" \
  -H "Content-Type: application/x-www-form-urlencoded" \
  -d "campaign=POST001&click_id=post_test_001&payout=1.95&country=AU&zip=2000&os_type=WINDOWS&browser=EDGE&device_type=DESKTOP&device_brand=HP&s1=AdSetAU001&s2=CreativeVideo&s3=AudienceInterest"
```

### 测试用例5：最小参数集
```bash
curl "http://localhost:8080/rsoc/postback?campaign=MIN001&payout=0.85"
```

## SedoTMP 专用回调接口测试数据 (`/rsoc/sedotmp/callback`)

### 测试用例1：美国用户 - 完整参数
```bash
curl "http://localhost:8080/rsoc/sedotmp/callback?campaign=SEDO001&click_id=us_full_001&epayout=1.25&country=US&country_name=United%20States&state=CA&city=Los%20Angeles&zip=90001&os_type=WINDOWS&browser=CHROME&device_type=DESKTOP&device_brand=DELL&subid1=AdSetUS001&subid2=CreativeDesktop&subid3=AudienceRetargeting&subid4=CampaignTypeConversion&subid5=SeasonSummer"
```

### 测试用例2：英国移动端用户
```bash
curl "http://localhost:8080/rsoc/sedotmp/callback?campaign=SEDO002&click_id=uk_mobile_002&epayout=0.85&country=GB&country_name=United%20Kingdom&state=London&city=London&zip=SW1A&os_type=ANDROID&browser=CHROME&device_type=MOBILE&device_brand=SAMSUNG&subid1=AdSetUK001&subid2=CreativeMobile&subid3=AudienceLookalike"
```

### 测试用例3：德国用户 - iOS 设备
```bash
curl "http://localhost:8080/rsoc/sedotmp/callback?campaign=SEDO003&click_id=de_ios_001&epayout=2.10&country=DE&country_name=Germany&state=Bavaria&city=Munich&zip=80331&os_type=IOS&browser=SAFARI&device_type=MOBILE&device_brand=APPLE&subid1=AdSetDE001&subid2=CreativeVideo&subid3=AudienceInterest&subid4=CampaignTypeBranding&subid5=SeasonWinter"
```

### 测试用例4：加拿大用户 - POST 方式
```bash
curl -X POST "http://localhost:8080/rsoc/sedotmp/callback" \
  -H "Content-Type: application/x-www-form-urlencoded" \
  -d "campaign=SEDO004&click_id=ca_post_001&epayout=1.60&country=CA&country_name=Canada&state=Ontario&city=Toronto&zip=M5V&os_type=MACOS&browser=SAFARI&device_type=DESKTOP&device_brand=APPLE&subid1=AdSetCA001&subid2=CreativeImage&subid3=AudienceCustom"
```

### 测试用例5：澳大利亚用户 - 基础参数
```bash
curl "http://localhost:8080/rsoc/sedotmp/callback?campaign=SEDO005&click_id=au_basic_001&epayout=0.95&country=AU&subid1=AdSetAU001&subid2=CreativeBasic"
```

### 测试用例6：法国用户 - 平板设备
```bash
curl "http://localhost:8080/rsoc/sedotmp/callback?campaign=SEDO006&click_id=fr_tablet_001&epayout=1.40&country=FR&country_name=France&state=Ile-de-France&city=Paris&zip=75001&os_type=ANDROID&browser=CHROME&device_type=TABLET&device_brand=SAMSUNG&subid1=AdSetFR001&subid2=CreativeTablet&subid3=AudienceRetargeting&subid4=CampaignTypeApp&subid5=SeasonSpring"
```

## 配置设置接口测试数据 (`/rsoc/postback/setup`)

### 测试用例1：AdTech 平台配置
```bash
curl -X POST "http://localhost:8080/rsoc/postback/setup" \
  -H "Content-Type: application/x-www-form-urlencoded" \
  -d "security_key=adtech_test_key_123" \
  -d "domain_name=http://callback.adtech-platform.com/postback" \
  -d "campaign=campaign_id" \
  -d "click_id=click_tracking_id" \
  -d "payout=revenue_amount" \
  -d "country=geo_country" \
  -d "zip=geo_zip" \
  -d "os_type=user_os" \
  -d "browser=user_browser" \
  -d "device_type=user_device" \
  -d "device_brand=device_manufacturer" \
  -d "s1=custom_param_1" \
  -d "s2=custom_param_2" \
  -d "s3=custom_param_3"
```

### 测试用例2：SedoTMP 平台配置
```bash
curl -X POST "http://localhost:8080/rsoc/postback/config" \
  -H "Content-Type: application/json" \
  -d '{
    "security_key": "sedotmp_test_key_456",
    "domain_name": "http://callback.sedotmp-platform.com/callback",
    "campaign": "campaign_identifier",
    "click_id": "click_uuid",
    "payout": "estimated_payout",
    "country": "user_country",
    "zip": "user_postal_code",
    "os_type": "operating_system",
    "browser": "browser_type",
    "device_type": "device_category",
    "device_brand": "device_manufacturer",
    "s1": "subid_1",
    "s2": "subid_2",
    "s3": "subid_3"
  }'
```

## 批量测试脚本

### 创建批量测试脚本
```bash
#!/bin/bash
# 批量测试脚本

BASE_URL="http://localhost:8080"

echo "=== 开始批量测试 ==="

# 测试通用回调接口
echo "1. 测试通用回调接口..."
campaigns=("TEST001" "TEST002" "TEST003" "TEST004" "TEST005")
payouts=("1.25" "2.50" "0.85" "3.75" "1.95")
countries=("US" "GB" "DE" "CA" "AU")

for i in {0..4}; do
    echo "测试 ${campaigns[$i]}..."
    curl -s "http://localhost:8080/rsoc/postback?campaign=${campaigns[$i]}&click_id=batch_test_$i&payout=${payouts[$i]}&country=${countries[$i]}" > /dev/null
    echo "✓ ${campaigns[$i]} 完成"
done

# 测试 SedoTMP 专用接口
echo "2. 测试 SedoTMP 专用接口..."
sedo_campaigns=("SEDO001" "SEDO002" "SEDO003" "SEDO004" "SEDO005")
epayouts=("0.123" "0.456" "0.789" "1.234" "0.567")

for i in {0..4}; do
    echo "测试 ${sedo_campaigns[$i]}..."
    curl -s "http://localhost:8080/rsoc/sedotmp/callback?campaign=${sedo_campaigns[$i]}&click_id=sedo_batch_$i&epayout=${epayouts[$i]}&country=${countries[$i]}" > /dev/null
    echo "✓ ${sedo_campaigns[$i]} 完成"
done

echo "=== 批量测试完成 ==="
```

## 数据验证 SQL 查询

### 验证测试数据是否正确存储
```sql
-- 查看所有测试数据
SELECT 
    campaign_id,
    date,
    real_price,
    clicks,
    update_time
FROM facebook_insights 
WHERE campaign_id LIKE 'ADT%' 
   OR campaign_id LIKE 'SEDO%' 
   OR campaign_id LIKE 'MIX%' 
   OR campaign_id LIKE 'POST%' 
   OR campaign_id LIKE 'MIN%' 
   OR campaign_id LIKE 'TEST%'
ORDER BY update_time DESC;

-- 按平台统计测试数据
SELECT 
    CASE 
        WHEN campaign_id LIKE 'ADT%' THEN 'AdTech'
        WHEN campaign_id LIKE 'SEDO%' THEN 'SedoTMP'
        WHEN campaign_id LIKE 'MIX%' THEN 'Mixed'
        WHEN campaign_id LIKE 'POST%' THEN 'POST Test'
        WHEN campaign_id LIKE 'MIN%' THEN 'Minimal'
        WHEN campaign_id LIKE 'TEST%' THEN 'Batch Test'
        ELSE 'Other'
    END as platform,
    COUNT(*) as record_count,
    SUM(real_price) as total_revenue,
    AVG(real_price) as avg_revenue
FROM facebook_insights 
WHERE campaign_id LIKE 'ADT%' 
   OR campaign_id LIKE 'SEDO%' 
   OR campaign_id LIKE 'MIX%' 
   OR campaign_id LIKE 'POST%' 
   OR campaign_id LIKE 'MIN%' 
   OR campaign_id LIKE 'TEST%'
GROUP BY platform
ORDER BY total_revenue DESC;

-- 查看最近1小时的测试数据
SELECT 
    campaign_id,
    real_price,
    update_time
FROM facebook_insights 
WHERE update_time >= DATE_SUB(NOW(), INTERVAL 1 HOUR)
  AND (campaign_id LIKE 'ADT%' 
       OR campaign_id LIKE 'SEDO%' 
       OR campaign_id LIKE 'TEST%')
ORDER BY update_time DESC;
```

## 性能测试数据

### 高并发测试
```bash
#!/bin/bash
# 高并发测试脚本

BASE_URL="http://localhost:8080"
CONCURRENT_REQUESTS=10
TOTAL_REQUESTS=100

echo "=== 性能测试开始 ==="
echo "并发数: $CONCURRENT_REQUESTS"
echo "总请求数: $TOTAL_REQUESTS"

# 使用 Apache Bench 进行性能测试
ab -n $TOTAL_REQUESTS -c $CONCURRENT_REQUESTS \
   "$BASE_URL/rsoc/postback?campaign=PERF001&payout=1.00&country=US"

# 使用 curl 进行并发测试
for i in $(seq 1 $CONCURRENT_REQUESTS); do
    (
        for j in $(seq 1 10); do
            curl -s "$BASE_URL/rsoc/sedotmp/callback?campaign=PERF$i$j&epayout=0.50&country=US" > /dev/null
        done
    ) &
done

wait
echo "=== 性能测试完成 ==="
```

## 错误测试用例

### 测试错误处理
```bash
# 测试缺少必要参数
curl "http://localhost:8080/rsoc/postback"

# 测试无效的收益格式
curl "http://localhost:8080/rsoc/postback?campaign=ERROR001&payout=invalid_number"

# 测试超长参数
curl "http://localhost:8080/rsoc/sedotmp/callback?campaign=ERROR002&click_id=$(python3 -c 'print("x" * 1000)')&epayout=1.00"

# 测试特殊字符
curl "http://localhost:8080/rsoc/postback?campaign=ERROR003&click_id=test%20with%20spaces&payout=1.00&country=US"
```

## 测试文件说明

### 测试脚本文件
1. **postback_test.sh** - 基础功能测试脚本
2. **run_test_data.sh** - 详细测试数据执行脚本
3. **run_test_data.py** - Python测试脚本，支持详细报告
4. **performance_test.py** - 性能测试脚本，支持并发测试
5. **test_data.json** - JSON格式的测试数据文件

### 使用方法

#### 基础测试
```bash
chmod +x postback_test.sh
./postback_test.sh
```

#### 详细测试
```bash
chmod +x run_test_data.sh
./run_test_data.sh [base_url]
```

#### Python测试（推荐）
```bash
pip3 install requests
python3 run_test_data.py [base_url]
```

#### 性能测试
```bash
pip3 install aiohttp
python3 performance_test.py [base_url] [concurrent_users] [total_requests]

# 示例：10个并发用户，总共100个请求
python3 performance_test.py http://localhost:8080 10 100
```

## 清理测试数据

### 清理 SQL 脚本
```sql
-- 清理所有测试数据
DELETE FROM facebook_insights
WHERE campaign_id LIKE 'ADT%'
   OR campaign_id LIKE 'SEDO%'
   OR campaign_id LIKE 'MIX%'
   OR campaign_id LIKE 'POST%'
   OR campaign_id LIKE 'MIN%'
   OR campaign_id LIKE 'TEST%'
   OR campaign_id LIKE 'PERF%'
   OR campaign_id LIKE 'ERROR%'
   OR campaign_id LIKE 'BATCH%'
   OR campaign_id LIKE 'SBATCH%';

-- 验证清理结果
SELECT COUNT(*) as remaining_test_records
FROM facebook_insights
WHERE campaign_id LIKE 'ADT%'
   OR campaign_id LIKE 'SEDO%'
   OR campaign_id LIKE 'TEST%'
   OR campaign_id LIKE 'BATCH%';
```
