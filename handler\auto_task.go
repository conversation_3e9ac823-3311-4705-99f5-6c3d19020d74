package handler

import (
	"github.com/gin-gonic/gin"
	"net/http"
	"rsoc-system-go/dao"
	"rsoc-system-go/model"
	"strconv"
)

type AutoTaskHandler struct {
	autoTaskDao *dao.AutoTaskDao
}

func NewAutoTaskHandler(autoTaskDao *dao.AutoTaskDao) *AutoTaskHandler {
	return &AutoTaskHandler{
		autoTaskDao: autoTaskDao,
	}
}

// Create 创建自动任务
func (h *AutoTaskHandler) Create(c *gin.Context) {
	var task model.AutoTask
	if err := c.ShouldBindJSON(&task); err != nil {
		c.JSON(http.StatusBadRequest, gin.H{"error": err.Error()})
		return
	}

	//task.AddTime = time.Now().String()
	if err := h.autoTaskDao.Create(&task); err != nil {
		c.JSON(http.StatusInternalServerError, gin.H{"error": err.Error()})
		return
	}

	c.<PERSON><PERSON><PERSON>(http.StatusOK, task)
}

// Update 更新自动任务
func (h *AutoTaskHandler) Update(c *gin.Context) {
	var task model.AutoTask
	if err := c.ShouldBindJSON(&task); err != nil {
		c.JSON(http.StatusBadRequest, gin.H{"error": err.Error()})
		return
	}

	if err := h.autoTaskDao.Update(&task); err != nil {
		c.JSON(http.StatusInternalServerError, gin.H{"error": err.Error()})
		return
	}

	c.JSON(http.StatusOK, task)
}

// GetByID 根据ID获取任务
func (h *AutoTaskHandler) GetByID(c *gin.Context) {
	id, err := strconv.Atoi(c.Param("id"))
	if err != nil {
		c.JSON(http.StatusBadRequest, gin.H{"error": "invalid id"})
		return
	}

	task, err := h.autoTaskDao.GetByID(id)
	if err != nil {
		c.JSON(http.StatusInternalServerError, gin.H{"error": err.Error()})
		return
	}

	c.JSON(http.StatusOK, task)
}

// GetByTaskID 根据TaskID获取任务
func (h *AutoTaskHandler) GetByTaskID(c *gin.Context) {
	taskID := c.Param("taskId")
	task, err := h.autoTaskDao.GetByTaskID(taskID)
	if err != nil {
		c.JSON(http.StatusInternalServerError, gin.H{"error": err.Error()})
		return
	}

	c.JSON(http.StatusOK, task)
}

// GetByClickID 根据ClickID获取任务
func (h *AutoTaskHandler) GetByClickID(c *gin.Context) {
	clickID := c.Param("clickId")
	task, err := h.autoTaskDao.GetByClickID(clickID)
	if err != nil {
		c.JSON(http.StatusInternalServerError, gin.H{"error": err.Error()})
		return
	}

	c.JSON(http.StatusOK, task)
}

// List 获取任务列表
func (h *AutoTaskHandler) List(c *gin.Context) {
	page, _ := strconv.Atoi(c.DefaultQuery("page", "1"))
	pageSize, _ := strconv.Atoi(c.DefaultQuery("pageSize", "10"))

	tasks, total, err := h.autoTaskDao.List(page, pageSize)
	if err != nil {
		c.JSON(http.StatusInternalServerError, gin.H{"error": err.Error()})
		return
	}

	c.JSON(http.StatusOK, gin.H{
		"data":     tasks,
		"total":    total,
		"page":     page,
		"pageSize": pageSize,
	})
}

// UpdateStatus 更新任务状态
func (h *AutoTaskHandler) UpdateStatus(c *gin.Context) {
	id, err := strconv.Atoi(c.Param("id"))
	if err != nil {
		c.JSON(http.StatusBadRequest, gin.H{"error": "invalid id"})
		return
	}

	status, err := strconv.Atoi(c.PostForm("status"))
	if err != nil {
		c.JSON(http.StatusBadRequest, gin.H{"error": "invalid status"})
		return
	}

	if err := h.autoTaskDao.UpdateStatus(id, status); err != nil {
		c.JSON(http.StatusInternalServerError, gin.H{"error": err.Error()})
		return
	}

	c.JSON(http.StatusOK, gin.H{"message": "status updated successfully"})
}

// UpdateConver 更新转化状态
func (h *AutoTaskHandler) UpdateConver(c *gin.Context) {
	id, err := strconv.Atoi(c.Param("id"))
	if err != nil {
		c.JSON(http.StatusBadRequest, gin.H{"error": "invalid id"})
		return
	}

	conver, err := strconv.Atoi(c.PostForm("conver"))
	if err != nil {
		c.JSON(http.StatusBadRequest, gin.H{"error": "invalid conver"})
		return
	}

	if err := h.autoTaskDao.UpdateConver(id, conver); err != nil {
		c.JSON(http.StatusInternalServerError, gin.H{"error": err.Error()})
		return
	}

	c.JSON(http.StatusOK, gin.H{"message": "conver updated successfully"})
}
