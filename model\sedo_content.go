package model

import (
	"time"
)

// SedoArticle 文章模型
type SedoArticle struct {
	ID               string             `json:"id"`               // ID
	Title            string             `json:"title"`            // 文章标题
	Excerpt          string             `json:"excerpt"`          // 文章摘要
	Text             string             `json:"text"`             // 文章内容
	CategoryID       string             `json:"categoryId"`       // 分类ID
	Tags             []string           `json:"tags"`             // 标签列表
	Country          string             `json:"country"`          // 国家代码
	Locale           string             `json:"locale"`           // 语言区域设置
	CreatedDate      time.Time          `json:"createdDate"`      // 创建时间
	LastModifiedDate time.Time          `json:"lastModifiedDate"` // 最后修改时间
	Partner          string             `json:"partner"`          // 合作伙伴
	CreatedBy        string             `json:"createdBy"`        // 创建者
	LastModifiedBy   string             `json:"lastModifiedBy"`   // 最后修改者
	Images           []SedoArticleImage `json:"images"`           // 文章图片
}

// SedoArticleImage 文章图片
type SedoArticleImage struct {
	ID       string `json:"id"`       // ID
	UID      string `json:"uid"`      // UID
	Name     string `json:"name"`     // 名称
	Filename string `json:"filename"` // 文件名
	URL      string `json:"url"`      // URL
	Type     string `json:"type"`     // 类型
	Size     int64  `json:"size"`     // 大小
}

// SedoPublishedArticle 已发布文章
type SedoPublishedArticle struct {
	ID               string             `json:"id"`               // ID
	Domain           string             `json:"domain"`           // 域名
	URL              string             `json:"url"`              // URL
	Slug             string             `json:"slug"`             // Slug
	PublishedID      string             `json:"publishedId"`      // 发布ID
	ArticleID        string             `json:"articleId"`        // 文章ID
	PublishedDate    time.Time          `json:"publishedDate"`    // 发布时间
	PublishedStatus  string             `json:"publishedStatus"`  // 发布状态
	PublishedBy      string             `json:"publishedBy"`      // 发布者
	Title            string             `json:"title"`            // 标题
	Excerpt          string             `json:"excerpt"`          // 摘要
	Text             string             `json:"text"`             // 内容
	CategoryID       string             `json:"categoryId"`       // 分类ID
	Tags             []string           `json:"tags"`             // 标签
	Locale           string             `json:"locale"`           // 区域设置
	CreatedDate      time.Time          `json:"createdDate"`      // 创建时间
	LastModifiedDate time.Time          `json:"lastModifiedDate"` // 最后修改时间
	Partner          string             `json:"partner"`          // 合作伙伴
	CreatedBy        string             `json:"createdBy"`        // 创建者
	LastModifiedBy   string             `json:"lastModifiedBy"`   // 最后修改者
	Images           []SedoArticleImage `json:"images"`           // 图片
}

// SedoMinimalPublishedArticle 最小已发布文章信息
type SedoMinimalPublishedArticle struct {
	ID              string    `json:"id"`              // ID
	ArticleID       string    `json:"articleId"`       // 文章ID
	Domain          string    `json:"domain"`          // 域名
	URL             string    `json:"url"`             // URL
	Slug            string    `json:"slug"`            // Slug
	PublishedID     string    `json:"publishedId"`     // 发布ID
	PublishedBy     string    `json:"publishedBy"`     // 发布者
	PublishedDate   time.Time `json:"publishedDate"`   // 发布时间
	PublishedStatus string    `json:"publishedStatus"` // 发布状态
	Partner         string    `json:"partner"`         // 合作伙伴
}

// SedoDetailedArticleResponse 详细文章响应（包含发布信息）
type SedoDetailedArticleResponse struct {
	ID                string                        `json:"id"`                // ID
	Title             string                        `json:"title"`             // 文章标题
	Excerpt           string                        `json:"excerpt"`           // 文章摘要
	Text              string                        `json:"text"`              // 文章内容
	CategoryID        string                        `json:"categoryId"`        // 分类ID
	Tags              []string                      `json:"tags"`              // 标签列表
	Country           string                        `json:"country"`           // 国家代码
	Locale            string                        `json:"locale"`            // 语言区域设置
	Images            []SedoArticleImage            `json:"images"`            // 文章图片
	CreatedDate       time.Time                     `json:"createdDate"`       // 创建时间
	LastModifiedDate  time.Time                     `json:"lastModifiedDate"`  // 最后修改时间
	Partner           string                        `json:"partner"`           // 合作伙伴
	CreatedBy         string                        `json:"createdBy"`         // 创建者
	LastModifiedBy    string                        `json:"lastModifiedBy"`    // 最后修改者
	PublishedArticles []SedoMinimalPublishedArticle `json:"publishedArticles"` // 已发布文章信息
}

// SedoCategory 分类
type SedoCategory struct {
	ID    string `json:"id"`    // ID
	Title string `json:"title"` // 标题
}

// SedoDomain 域名
type SedoDomain struct {
	ID               string    `json:"id"`               // ID
	Domain           string    `json:"domain"`           // 域名
	URL              string    `json:"url"`              // URL
	Platform         string    `json:"platform"`         // 平台
	CreatedDate      time.Time `json:"createdDate"`      // 创建时间
	LastModifiedDate time.Time `json:"lastModifiedDate"` // 最后修改时间
	Partners         []string  `json:"partners"`         // 合作伙伴
	CreatedBy        string    `json:"createdBy"`        // 创建者
	LastModifiedBy   string    `json:"lastModifiedBy"`   // 最后修改者
}

// SedoMediaResource 媒体资源
type SedoMediaResource struct {
	ID               string    `json:"id"`               // ID
	Name             string    `json:"name"`             // 名称
	Filename         string    `json:"filename"`         // 文件名
	OriginalFilename string    `json:"originalFilename"` // 原始文件名
	URL              string    `json:"url"`              // URL
	Type             string    `json:"type"`             // 类型
	CreatedDate      time.Time `json:"createdDate"`      // 创建时间
	LastModifiedDate time.Time `json:"lastModifiedDate"` // 最后修改时间
	Partner          string    `json:"partner"`          // 合作伙伴
	CreatedBy        string    `json:"createdBy"`        // 创建者
	LastModifiedBy   string    `json:"lastModifiedBy"`   // 最后修改者
}

// 请求结构体

// SedoCreateArticleRequest 创建文章请求
type SedoCreateArticleRequest struct {
	Title       string             `json:"title"`                 // 标题
	Excerpt     string             `json:"excerpt"`               // 摘要
	Text        string             `json:"text"`                  // 内容
	CategoryID  string             `json:"categoryId"`            // 分类ID
	Tags        []string           `json:"tags"`                  // 标签
	Country     string             `json:"country"`               // 国家
	Locale      string             `json:"locale"`                // 区域设置
	Images      []SedoArticleImage `json:"images"`                // 图片
	AutoPublish *SedoAutoPublish   `json:"autoPublish,omitempty"` // 自动发布
}

// SedoUpdateArticleRequest 更新文章请求
type SedoUpdateArticleRequest struct {
	Title      string             `json:"title"`      // 标题
	Excerpt    string             `json:"excerpt"`    // 摘要
	Text       string             `json:"text"`       // 内容
	CategoryID string             `json:"categoryId"` // 分类ID
	Tags       []string           `json:"tags"`       // 标签
	Country    string             `json:"country"`    // 国家
	Locale     string             `json:"locale"`     // 区域设置
	Images     []SedoArticleImage `json:"images"`     // 图片
	Partner    string             `json:"partner"`    // 合作伙伴
}

// SedoGenerateArticleRequest 生成文章请求
type SedoGenerateArticleRequest struct {
	Topics        []string         `json:"topics"`                // 主题
	Title         string           `json:"title"`                 // 标题
	Excerpt       string           `json:"excerpt"`               // 摘要
	Country       string           `json:"country"`               // 国家
	Locale        string           `json:"locale"`                // 区域设置
	CategoryID    string           `json:"categoryId"`            // 分类ID
	Tags          []string         `json:"tags"`                  // 标签
	AutoPublish   *SedoAutoPublish `json:"autoPublish,omitempty"` // 自动发布
	GenerateImage *struct {
		Enabled     bool   `json:"enabled"`     // 是否启用
		Description string `json:"description"` // 描述
	} `json:"generateImage,omitempty"` // 生成图片
}

// SedoPublishArticleRequest 发布文章请求
type SedoPublishArticleRequest struct {
	ArticleID   string    `json:"articleId"`   // 文章ID
	Domain      string    `json:"domain"`      // 域名
	PublishDate time.Time `json:"publishDate"` // 发布时间
}

// SedoCreateCategoryRequest 创建分类请求
type SedoCreateCategoryRequest struct {
	Title string `json:"title"` // 标题
}

// SedoAutoPublish 自动发布
type SedoAutoPublish struct {
	DomainName  string    `json:"domainName"`  // 域名
	PublishDate time.Time `json:"publishDate"` // 发布时间
}

// 分页响应

// SedoPageResponse 分页响应
type SedoPageResponse struct {
	TotalCount int `json:"totalCount"` // 总数
	TotalPages int `json:"totalPages"` // 总页数
}

// SedoArticleListResponse 文章列表响应
type SedoArticleListResponse struct {
	Articles []SedoArticle    `json:"articles"` // 文章列表
	Page     SedoPageResponse `json:"page"`     // 分页信息
}

// SedoPublishedArticleListResponse 已发布文章列表响应
type SedoPublishedArticleListResponse struct {
	Articles []SedoPublishedArticle `json:"articles"` // 文章列表
	Page     SedoPageResponse       `json:"page"`     // 分页信息
}

// SedoCategoryListResponse 分类列表响应
type SedoCategoryListResponse struct {
	Categories []SedoCategory   `json:"categories"` // 分类列表
	Page       SedoPageResponse `json:"page"`       // 分页信息
}

// SedoDomainListResponse 域名列表响应
type SedoDomainListResponse struct {
	Domains []SedoDomain     `json:"domains"` // 域名列表
	Page    SedoPageResponse `json:"page"`    // 分页信息
}

// SedoMediaResourceListResponse 媒体资源列表响应
type SedoMediaResourceListResponse struct {
	MediaResources []SedoMediaResource `json:"mediaResources"` // 媒体资源列表
	Page           SedoPageResponse    `json:"page"`           // 分页信息
}

// SedoPageable 分页请求
type SedoPageable struct {
	Page int    `json:"page,omitempty"` // 页码
	Size int    `json:"size,omitempty"` // 每页大小
	Sort string `json:"sort,omitempty"` // 排序
}
