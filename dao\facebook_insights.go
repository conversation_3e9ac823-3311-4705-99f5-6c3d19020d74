package dao

import (
	"rsoc-system-go/model"

	"gorm.io/gorm"
)

// FacebookInsightsDAO Facebook广告数据访问对象
type FacebookInsightsDAO struct {
	db *gorm.DB
}

// NewFacebookInsightsDAO 创建新的FacebookInsightsDAO实例
func NewFacebookInsightsDAO(db *gorm.DB) *FacebookInsightsDAO {
	return &FacebookInsightsDAO{db: db}
}

// Create 创建Facebook广告数据记录
func (dao *FacebookInsightsDAO) Create(insights *model.FacebookInsights) error {
	return dao.db.Create(insights).Error
}

// Update 更新Facebook广告数据记录
func (dao *FacebookInsightsDAO) Update(insights *model.FacebookInsights) error {
	return dao.db.Save(insights).Error
}

// UpdateClicksByCampaignIDAndDate 更新Facebook广告数据记录
func (dao *FacebookInsightsDAO) UpdateClicksByCampaignIDAndDate(campaignID string, date string, insights *model.FacebookInsights) error {
	return dao.db.Model(&model.FacebookInsights{}).Where("campaign_id = ? AND date = ?", campaignID, date).Select("clicks").Updates(insights).Error
}

// Delete 删除Facebook广告数据记录
func (dao *FacebookInsightsDAO) Delete(id uint) error {
	return dao.db.Delete(&model.FacebookInsights{}, id).Error
}

// FindByID 根据ID查找Facebook广告数据记录
func (dao *FacebookInsightsDAO) FindByID(id uint) (*model.FacebookInsights, error) {
	var insights model.FacebookInsights
	err := dao.db.First(&insights, id).Error
	if err != nil {
		return nil, err
	}
	return &insights, nil
}

// FindByDate 根据日期查找Facebook广告数据记录
func (dao *FacebookInsightsDAO) FindByDate(date string) ([]*model.FacebookInsights, error) {
	var insights []*model.FacebookInsights
	err := dao.db.Where("date = ?", date).Find(&insights).Error
	return insights, err
}

// FindByAccountID 根据账户ID查找Facebook广告数据记录
func (dao *FacebookInsightsDAO) FindByAccountID(accountID string) ([]*model.FacebookInsights, error) {
	var insights []*model.FacebookInsights
	err := dao.db.Where("account_id = ?", accountID).Find(&insights).Error
	return insights, err
}

// FindByCampaignID 根据广告系列ID查找Facebook广告数据记录
func (dao *FacebookInsightsDAO) FindByCampaignID(campaignID string) ([]*model.FacebookInsights, error) {
	var insights []*model.FacebookInsights
	err := dao.db.Where("campaign_id = ?", campaignID).Find(&insights).Error
	return insights, err
}

// FindByAdID 根据广告ID查找Facebook广告数据记录
func (dao *FacebookInsightsDAO) FindByAdID(adID string) ([]*model.FacebookInsights, error) {
	var insights []*model.FacebookInsights
	err := dao.db.Where("ad_id = ?", adID).Find(&insights).Error
	return insights, err
}

// List 列出所有Facebook广告数据记录（支持分页）
func (dao *FacebookInsightsDAO) List(page, pageSize int) ([]*model.FacebookInsights, error) {
	var insights []*model.FacebookInsights
	offset := (page - 1) * pageSize
	err := dao.db.Offset(offset).Limit(pageSize).Find(&insights).Error
	return insights, err
}

// FindOne 根据条件查询单条Facebook广告数据记录
func (dao *FacebookInsightsDAO) FindOne(conditions map[string]interface{}) (*model.FacebookInsights, error) {
	var insights model.FacebookInsights
	err := dao.db.Where(conditions).First(&insights).Error
	if err != nil {
		return nil, err
	}
	return &insights, nil
}

// Count 获取记录总数
func (dao *FacebookInsightsDAO) Count() (int64, error) {
	var count int64
	err := dao.db.Model(&model.FacebookInsights{}).Count(&count).Error
	return count, err
}
