package tools

import (
	"encoding/json"
	"github.com/imroc/req/v3"
	"log"
	"net/url"
)

func GetUa() map[string]string {
	client := req.C()
	response, err := client.R().Get("http://3.142.132.231/index/getUa")
	if err != nil {
		return nil
	}
	log.Println(response.String())
	jsonData, _ := url.QueryUnescape(response.String())
	log.Println(jsonData)
	var result map[string]string

	err = json.Unmarshal([]byte(jsonData), &result)
	if err != nil {
		log.Fatal(err)
	}
	return result
}
