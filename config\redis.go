package config

import (
	"log"
	"os"
	"rsoc-system-go/middleware/pkg/redis"
	"rsoc-system-go/store"
	"strings"
	"sync"

	goredis "github.com/go-redis/redis/v8"
)

var redisName = "redis"

// 保持原有的redisClients用于向后兼容
var (
	redisClients = make(map[int]*goredis.Client)
	clientsMutex sync.RWMutex
)

func init() {
	go RedisClientInit()
}

// SelectDB 切换到指定的 Redis 数据库
func SelectDB(db int) *goredis.Client {
	if store.RedisClient == nil {
		log.Printf("[%s] Redis client not initialized", redisName)
		return nil
	}

	// 如果是默认数据库（通常是0），直接返回全局客户端
	if db == 0 {
		return store.RedisClient
	}

	// 首先尝试从缓存中获取已存在的客户端
	clientsMutex.RLock()
	if client, exists := redisClients[db]; exists {
		clientsMutex.RUnlock()
		return client
	}
	clientsMutex.RUnlock()

	// 如果不存在，创建新的客户端
	clientsMutex.Lock()
	defer clientsMutex.Unlock()

	// 双重检查，防止在加锁期间其他协程已创建
	if client, exists := redisClients[db]; exists {
		return client
	}

	// 创建新的客户端实例，指定新的数据库
	newOpts := *store.RedisClient.Options()
	newOpts.DB = db
	client := goredis.NewClient(&newOpts)
	redisClients[db] = client
	return client
}

// GetRedisClient 获取当前默认的 Redis 客户端
func GetRedisClient() *goredis.Client {
	return store.RedisClient
}

// CloseDB 关闭指定数据库的连接
func CloseDB(db int) {
	// 不允许关闭默认数据库连接
	if db == 0 {
		return
	}

	clientsMutex.Lock()
	defer clientsMutex.Unlock()

	if client, exists := redisClients[db]; exists {
		client.Close()
		delete(redisClients, db)
	}
}

// CloseAllDBs 关闭所有额外的数据库连接
func CloseAllDBs() {
	clientsMutex.Lock()
	defer clientsMutex.Unlock()

	for db, client := range redisClients {
		client.Close()
		delete(redisClients, db)
	}
}

func RedisClientInit() {
	// 从环境变量中获取主Redis连接信息
	mainRedisURL := os.Getenv("REDIS_URL")
	if mainRedisURL == "" {
		log.Printf("[%s] REDIS_URL not set, skipping Redis initialization", redisName)
		return
	}

	// 初始化主Redis实例
	err := store.GetRedisManager().RegisterRedis(store.RedisConfig{
		Instance: "main",
		URL:      mainRedisURL,
		PoolSize: 10,
	})
	if err != nil {
		log.Printf("[%s] Failed to register main Redis instance: %v", redisName, err)
		return
	}

	// 连接主Redis实例
	err = store.GetRedisManager().ConnectRedis("main")
	if err != nil {
		log.Printf("[%s] Failed to connect to main Redis instance: %v", redisName, err)
		return
	}

	// 获取主Redis客户端并设置为默认客户端
	mainClient := store.GetRedisManager().GetDefaultRedisClient()
	if mainClient != nil {
		redis.SetDefaultClient(mainClient)
		log.Printf("[%s] Main Redis instance connected successfully", redisName)
	}

	// 初始化其他Redis实例（如果环境变量中配置了的话）
	// 格式：REDIS_URL_<NAME>=<URL>
	for _, env := range os.Environ() {
		if len(env) > 10 && strings.HasPrefix(env, "REDIS_URL_") {
			parts := strings.SplitN(env, "=", 2)
			name := strings.TrimPrefix(parts[0], "REDIS_URL_")
			url := os.Getenv(parts[0])
			if url != "" && name != "MAIN" { // 避免重复初始化主实例
				name = strings.ToLower(name)
				err := store.GetRedisManager().RegisterRedis(store.RedisConfig{
					Instance: store.RedisInstance(name),
					URL:      url,
					PoolSize: 10,
				})
				if err != nil {
					log.Printf("[%s] Failed to register Redis instance %s: %v", redisName, name, err)
					continue
				}

				err = store.GetRedisManager().ConnectRedis(store.RedisInstance(name))
				if err != nil {
					log.Printf("[%s] Failed to connect to Redis instance %s: %v", redisName, name, err)
					continue
				}

				log.Printf("[%s] Additional Redis instance %s connected successfully", redisName, name)
			}
		}
	}

	// 检查所有必需的数据库连接是否就绪
	store.CheckDatabaseConnections()
}
