package store

import (
	"database/sql"
	"fmt"
	"log"
	"sync"

	"github.com/go-redis/redis/v8"
	"gorm.io/gorm"
)

var (
	DB          *gorm.DB
	TDB         *sql.DB
	RedisClient *redis.Client
	dbOnce      sync.Once
)

// GetDB returns the global database instance
func GetDB() *gorm.DB {
	dbOnce.Do(func() {
		if DB == nil {
			// 尝试从数据库管理器获取默认连接
			if db, err := GetDBManager().GetDefaultDBConnection(); err == nil {
				DB = db
			}
		}
	})

	if DB == nil {
		panic("Database not initialized")
	}
	return DB
}

// SetDB sets the global database instance
func SetDB(db *gorm.DB) {
	DB = db
}

// GetTDB returns the global database instance
func GetTDB() *sql.DB {
	if TDB == nil {
		panic("Database not initialized")
	}
	return TDB
}

// SetTDB sets the global database instance
func SetTDB(db *sql.DB) {
	TDB = db
}

// GetRedis returns the global Redis client
func GetRedis() *redis.Client {
	if RedisClient == nil {
		panic("Redis client not initialized")
	}
	return RedisClient
}

// SetRedis sets the global Redis client
func SetRedis(client *redis.Client) {
	RedisClient = client
}

// SwitchDBInstance 切换到指定的数据库实例
func SwitchDBInstance(instance DBInstance) error {
	db, err := GetDBManager().GetDB(instance)
	if err != nil {
		return err
	}
	SetDB(db)
	return nil
}

// GetCurrentDBConfig 获取当前使用的数据库配置
func GetCurrentDBConfig() DBConfig {
	return GetDBManager().GetCurrentDB()
}

// GetMySQLInstances 获取所有MySQL数据库实例
func GetMySQLInstances() []DBInstance {
	return GetDBManager().GetInstancesByType(MySQL)
}

// GetPostgreSQLInstances 获取所有PostgreSQL数据库实例
func GetPostgreSQLInstances() []DBInstance {
	return GetDBManager().GetInstancesByType(PostgreSQL)
}

// RegisterMySQLInstance 注册新的MySQL实例
func RegisterMySQLInstance(instance DBInstance, dsn string) {
	GetDBManager().RegisterDB(DBConfig{
		Type:     MySQL,
		Instance: instance,
		DSN:      dsn,
	})
}

// RegisterPostgreSQLInstance 注册新的PostgreSQL实例
func RegisterPostgreSQLInstance(instance DBInstance, dsn string) {
	GetDBManager().RegisterDB(DBConfig{
		Type:     PostgreSQL,
		Instance: instance,
		DSN:      dsn,
	})
}

// WithDB 在指定的数据库实例上执行操作，完成后自动切换回原数据库
func WithDB(instance DBInstance, fn func(*gorm.DB) error) error {
	// 保存当前数据库配置
	currentConfig := GetCurrentDBConfig()

	// 切换到指定实例
	if err := SwitchDBInstance(instance); err != nil {
		return fmt.Errorf("切换到数据库实例 %s 失败: %v", instance, err)
	}

	// 确保在函数返回时切换回原来的数据库
	defer func() {
		if err := SwitchDBInstance(currentConfig.Instance); err != nil {
			log.Printf("警告: 切换回原数据库实例 %s 失败: %v", currentConfig.Instance, err)
		}
	}()

	// 执行用户的操作
	return fn(GetDB())
}

// WithDBFunc 在指定的数据库实例上执行任意函数，完成后自动切换回原数据库
func WithDBFunc[T any](instance DBInstance, fn func(*gorm.DB) (T, error)) (T, error) {
	// 保存当前数据库配置
	currentConfig := GetCurrentDBConfig()

	// 切换到指定实例
	if err := SwitchDBInstance(instance); err != nil {
		var zero T
		return zero, fmt.Errorf("切换到数据库实例 %s 失败: %v", instance, err)
	}

	// 确保在函数返回时切换回原来的数据库
	defer func() {
		if err := SwitchDBInstance(currentConfig.Instance); err != nil {
			log.Printf("警告: 切换回原数据库实例 %s 失败: %v", currentConfig.Instance, err)
		}
	}()

	// 执行用户的操作
	return fn(GetDB())
}

// NewDAO 创建支持多实例的 DAO
type DAO struct {
	instance DBInstance
}

func NewDAO(instance DBInstance) *DAO {
	return &DAO{instance: instance}
}

// DB 获取当前实例的数据库连接，自动处理切换和恢复
func (d *DAO) DB() *gorm.DB {
	if d.instance == "" {
		return GetDB() // 使用默认数据库
	}

	db, err := GetDBManager().GetDB(d.instance)
	if err != nil {
		log.Printf("警告: 获取数据库实例 %s 失败: %v，使用默认数据库", d.instance, err)
		return GetDB()
	}
	return db
}
