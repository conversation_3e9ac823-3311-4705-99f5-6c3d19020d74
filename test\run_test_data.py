#!/usr/bin/env python3
"""
Postback 回调接口测试数据执行脚本
使用方法: python3 run_test_data.py [base_url]
"""

import json
import requests
import sys
import time
from urllib.parse import urlencode

class PostbackTester:
    def __init__(self, base_url="http://localhost:8080"):
        self.base_url = base_url
        self.session = requests.Session()
        self.results = []
        
    def load_test_data(self, file_path="test_data.json"):
        """加载测试数据"""
        try:
            with open(file_path, 'r', encoding='utf-8') as f:
                return json.load(f)
        except FileNotFoundError:
            print(f"测试数据文件 {file_path} 不存在")
            return None
    
    def run_test_case(self, test_case, endpoint):
        """执行单个测试用例"""
        url = f"{self.base_url}{endpoint}"
        method = test_case.get('method', 'GET')
        params = test_case.get('params', {})
        name = test_case.get('name', 'Unknown Test')
        
        print(f"执行测试: {name}")
        print(f"方法: {method}")
        print(f"URL: {url}")
        print(f"参数: {params}")
        
        try:
            if method.upper() == 'GET':
                response = self.session.get(url, params=params, timeout=10)
            elif method.upper() == 'POST':
                content_type = test_case.get('content_type', 'application/x-www-form-urlencoded')
                if content_type == 'application/json':
                    response = self.session.post(url, json=params, timeout=10)
                else:
                    response = self.session.post(url, data=params, timeout=10)
            
            result = {
                'name': name,
                'method': method,
                'url': url,
                'params': params,
                'status_code': response.status_code,
                'response': response.text,
                'success': response.status_code == 200
            }
            
            print(f"状态码: {response.status_code}")
            print(f"响应: {response.text}")
            print("---")
            
            self.results.append(result)
            return result
            
        except Exception as e:
            print(f"错误: {str(e)}")
            result = {
                'name': name,
                'method': method,
                'url': url,
                'params': params,
                'error': str(e),
                'success': False
            }
            self.results.append(result)
            return result
    
    def run_general_postback_tests(self, test_data):
        """运行通用回调接口测试"""
        print("### 1. 通用回调接口测试")
        print("")
        
        general_tests = test_data['test_data']['general_postback']
        endpoint = general_tests['endpoint']
        
        for test_case in general_tests['test_cases']:
            self.run_test_case(test_case, endpoint)
            time.sleep(0.5)  # 避免请求过于频繁
        
        print("")
    
    def run_sedotmp_callback_tests(self, test_data):
        """运行SedoTMP专用回调接口测试"""
        print("### 2. SedoTMP 专用回调接口测试")
        print("")
        
        sedotmp_tests = test_data['test_data']['sedotmp_callback']
        endpoint = sedotmp_tests['endpoint']
        
        for test_case in sedotmp_tests['test_cases']:
            self.run_test_case(test_case, endpoint)
            time.sleep(0.5)
        
        print("")
    
    def run_setup_config_tests(self, test_data):
        """运行配置设置接口测试"""
        print("### 3. 配置设置接口测试")
        print("")
        
        setup_tests = test_data['test_data']['setup_config']
        endpoint = setup_tests['endpoint']
        
        for test_case in setup_tests['test_cases']:
            self.run_test_case(test_case, endpoint)
            time.sleep(0.5)
        
        print("")
    
    def run_error_tests(self, test_data):
        """运行错误测试用例"""
        print("### 4. 错误测试用例")
        print("")
        
        error_tests = test_data['test_data']['error_test_cases']
        
        for test_case in error_tests['test_cases']:
            endpoint = test_case['endpoint']
            self.run_test_case(test_case, endpoint)
            time.sleep(0.5)
        
        print("")
    
    def run_batch_tests(self, test_data):
        """运行批量测试"""
        print("### 5. 批量测试")
        print("")
        
        batch_data = test_data['test_data']['batch_test_data']
        
        # 通用回调接口批量测试
        print("通用回调接口批量测试:")
        for i, params in enumerate(batch_data['general_postback_batch']):
            params['click_id'] = f"batch_test_{i}"
            test_case = {
                'name': f"批量测试 {params['campaign']}",
                'method': 'GET',
                'params': params
            }
            self.run_test_case(test_case, '/rsoc/postback')
        
        print("")
        
        # SedoTMP专用接口批量测试
        print("SedoTMP专用接口批量测试:")
        for i, params in enumerate(batch_data['sedotmp_callback_batch']):
            params['click_id'] = f"sedo_batch_{i}"
            test_case = {
                'name': f"SedoTMP批量测试 {params['campaign']}",
                'method': 'GET',
                'params': params
            }
            self.run_test_case(test_case, '/rsoc/sedotmp/callback')
        
        print("")
    
    def generate_report(self):
        """生成测试报告"""
        print("=== 测试报告 ===")
        print("")
        
        total_tests = len(self.results)
        successful_tests = len([r for r in self.results if r.get('success', False)])
        failed_tests = total_tests - successful_tests
        
        print(f"总测试数: {total_tests}")
        print(f"成功: {successful_tests}")
        print(f"失败: {failed_tests}")
        print(f"成功率: {(successful_tests/total_tests*100):.1f}%")
        print("")
        
        if failed_tests > 0:
            print("失败的测试:")
            for result in self.results:
                if not result.get('success', False):
                    print(f"- {result['name']}: {result.get('error', 'HTTP错误')}")
        
        print("")
        print("数据验证SQL查询:")
        print("SELECT campaign_id, real_price, date, update_time FROM facebook_insights WHERE campaign_id LIKE 'ADT%' OR campaign_id LIKE 'SEDO%' OR campaign_id LIKE 'BATCH%' OR campaign_id LIKE 'SBATCH%' ORDER BY update_time DESC LIMIT 20;")
        print("")
        print("清理测试数据SQL:")
        print("DELETE FROM facebook_insights WHERE campaign_id LIKE 'ADT%' OR campaign_id LIKE 'SEDO%' OR campaign_id LIKE 'BATCH%' OR campaign_id LIKE 'SBATCH%' OR campaign_id LIKE 'ERROR%' OR campaign_id LIKE 'MIN%' OR campaign_id LIKE 'MIX%' OR campaign_id LIKE 'POST%';")
    
    def run_all_tests(self):
        """运行所有测试"""
        print("=== Postback 回调接口测试数据执行 ===")
        print(f"基础URL: {self.base_url}")
        print(f"开始时间: {time.strftime('%Y-%m-%d %H:%M:%S')}")
        print("")
        
        # 加载测试数据
        test_data = self.load_test_data()
        if not test_data:
            return
        
        # 运行各种测试
        self.run_general_postback_tests(test_data)
        self.run_sedotmp_callback_tests(test_data)
        self.run_setup_config_tests(test_data)
        self.run_error_tests(test_data)
        self.run_batch_tests(test_data)
        
        # 生成报告
        self.generate_report()
        
        print(f"结束时间: {time.strftime('%Y-%m-%d %H:%M:%S')}")

def main():
    """主函数"""
    base_url = sys.argv[1] if len(sys.argv) > 1 else "http://localhost:8080"
    
    tester = PostbackTester(base_url)
    tester.run_all_tests()

if __name__ == "__main__":
    main()
