package handler

import (
	"net/http"
	"rsoc-system-go/dao"
	"rsoc-system-go/model"
	"strconv"

	"github.com/gin-gonic/gin"
)

type ArticleHandler struct {
	articleDAO *dao.ArticleDAO
}

func NewArticleHandler(articleDAO *dao.ArticleDAO) *ArticleHandler {
	return &ArticleHandler{articleDAO: articleDAO}
}

// List 获取文章列表
func (h *ArticleHandler) List(c *gin.Context) {
	var req model.ArticleListRequest
	if err := c.ShouldBindQuery(&req); err != nil {
		c.JSON(http.StatusBadRequest, gin.H{"error": err.Error()})
		return
	}

	// 设置默认分页参数
	if req.Page <= 0 {
		req.Page = 1
	}
	if req.PageSize <= 0 {
		req.PageSize = 10
	}

	articles, total, err := h.articleDAO.List(&req)
	if err != nil {
		c.JSON(http.StatusInternalServerError, gin.H{"error": "获取文章列表失败"})
		return
	}

	c.JSON(http.StatusOK, gin.H{
		"data":      articles,
		"total":     total,
		"page":      req.Page,
		"page_size": req.PageSize,
	})
}

// Create 创建文章
func (h *ArticleHandler) Create(c *gin.Context) {
	var req model.ArticleCreateRequest
	if err := c.ShouldBindJSON(&req); err != nil {
		c.JSON(http.StatusBadRequest, gin.H{"error": err.Error()})
		return
	}

	// 从上下文中获取当前用户ID
	userID, exists := c.Get("user_id")
	if !exists {
		c.JSON(http.StatusUnauthorized, gin.H{"error": "未登录"})
		return
	}

	article := &model.Article{
		Title:    req.Title,
		Content:  req.Content,
		AuthorID: userID.(uint),
		Status:   0, // 默认待审核状态
	}

	if err := h.articleDAO.Create(article); err != nil {
		c.JSON(http.StatusInternalServerError, gin.H{"error": "创建文章失败"})
		return
	}

	c.JSON(http.StatusOK, gin.H{"data": article})
}

// UpdateStatus 更新文章状态（审核）
func (h *ArticleHandler) UpdateStatus(c *gin.Context) {
	id, err := strconv.ParseUint(c.Param("id"), 10, 32)
	if err != nil {
		c.JSON(http.StatusBadRequest, gin.H{"error": "无效的文章ID"})
		return
	}

	var req model.ArticleUpdateStatusRequest
	if err := c.ShouldBindJSON(&req); err != nil {
		c.JSON(http.StatusBadRequest, gin.H{"error": err.Error()})
		return
	}

	// 验证状态值是否有效
	if req.Status != 1 && req.Status != 2 {
		c.JSON(http.StatusBadRequest, gin.H{"error": "无效的状态值"})
		return
	}

	if err := h.articleDAO.UpdateStatus(uint(id), req.Status); err != nil {
		c.JSON(http.StatusInternalServerError, gin.H{"error": "更新文章状态失败"})
		return
	}

	c.JSON(http.StatusOK, gin.H{"message": "更新成功"})
}
