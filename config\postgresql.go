package config

import (
	"fmt"
	"github.com/lib/pq"
	"gorm.io/driver/postgres"
	"gorm.io/gorm"
	"log"
	"os"
	"rsoc-system-go/store"
)

var postgresName = "postgres"

func init() {
	go PostgresInit()
}

func PostgresInit() {
	// init config
	dsn := os.Getenv("POSTGRES_URL")
	if dsn == "" || validatePostgreSQLDSN(dsn) != nil {
		return
	}

	// 注册PostgreSQL配置
	dbManager := store.GetDBManager()
	dbManager.RegisterDB(store.DBConfig{
		Type: store.PostgreSQL,
		DSN:  dsn,
	})

	log.Printf("[%s] POSTGRES config initial", postgresName)
	log.Printf("[%s] dsn: %s", postgresName, dsn)

	var err error
	db, err := gorm.Open(postgres.Open(dsn), &gorm.Config{})
	if err != nil {
		log.Printf("[%s] POSTGRES connect fail: %v", postgresName, err)
		return
	}

	// 设置到数据库管理器
	defaultInstance := store.DBInstance("postgres_default")
	if err := dbManager.SetDB(defaultInstance, db); err != nil {
		log.Printf("[%s] Failed to set PostgreSQL connection: %v", postgresName, err)
		return
	}

	// 如果MySQL未配置，则将PostgreSQL设置为默认数据库
	if os.Getenv("MYSQL_URL") == "" {
		dbManager.SetDefaultDB(defaultInstance)
		store.SetDB(db)
	}

	log.Printf("[%s] POSTGRES connected successfully ", postgresName)
}

// validatePostgreSQLDSN 验证 PostgreSQL DSN 格式
func validatePostgreSQLDSN(dsn string) error {
	_, err := pq.ParseURL(dsn)
	if err != nil {
		log.Printf("[%s] invalid PostgreSQL DSN: %v", postgresName, err)
		return fmt.Errorf("[%s] invalid PostgreSQL DSN: %v", postgresName, err)
	}
	return nil
}
