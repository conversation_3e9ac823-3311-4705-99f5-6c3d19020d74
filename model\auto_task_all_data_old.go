package model

// AutoTaskAllDataOld 自动任务数据旧表模型
type AutoTaskAllDataOld struct {
	ID            int64   `gorm:"column:id;primaryKey;autoIncrement" json:"id"`
	TaskID        string  `gorm:"column:task_id" json:"task_id"`
	ClickID       string  `gorm:"column:click_id" json:"click_id"`
	Status        int     `gorm:"column:status" json:"status"`
	AddTime       string  `gorm:"column:add_time" json:"add_time"`
	Country       string  `gorm:"column:country" json:"country"`
	IPCountry     string  `gorm:"column:ip_country" json:"ip_country"`
	Viewport      string  `gorm:"column:viewport" json:"viewport"`
	UserAgent     string  `gorm:"column:useragent" json:"useragent"`
	Lang          string  `gorm:"column:lang" json:"lang"`
	UserIP        string  `gorm:"column:user_ip" json:"user_ip"`
	Ref           string  `gorm:"column:ref" json:"ref"`
	Keywords      string  `gorm:"column:keywords" json:"keywords"`
	Conver        int     `gorm:"column:conver" json:"conver"`
	StartTime     string  `gorm:"column:start_time" json:"start_time"`
	OverTime      string  `gorm:"column:over_time" json:"over_time"`
	FingerprintJS string  `gorm:"column:fingerprintjs" json:"fingerprintjs"`
	CampaignID    string  `gorm:"column:campaign_id" json:"campaign_id"`
	AdsetID       string  `gorm:"column:adset_id" json:"adset_id"`
	AdID          string  `gorm:"column:ad_id" json:"ad_id"`
	View          int     `gorm:"column:view" json:"view"`
	Click1        int     `gorm:"column:click1" json:"click1"`
	Click2        int     `gorm:"column:click2" json:"click2"`
	Click3        int     `gorm:"column:click3" json:"click3"`
	Traffic       string  `gorm:"column:traffic" json:"traffic"`
	TrafficData   string  `gorm:"column:traffic_data" json:"traffic_data"`
	ProxyName     string  `gorm:"column:proxy_name" json:"proxy_name"`
	ProxyInfo     string  `gorm:"column:proxy_info" json:"proxy_info"`
	TrafficURL    string  `gorm:"column:traffic_url" json:"traffic_url"`
	RealPrice     float64 `gorm:"column:real_price" json:"real_price"`
	OraclePrice   float64 `gorm:"column:oracle_price" json:"oracle_price"`
	ClickURL      string  `gorm:"column:click_url" json:"click_url"`
	Date          string  `gorm:"column:date" json:"date"`
}

// TableName 指定表名
func (AutoTaskAllDataOld) TableName() string {
	return "auto_task_all_data_old"
}
