package config

import (
	"log"
	"os"

	"github.com/gin-gonic/gin"
	"github.com/joho/godotenv"
)

var configName = "config"

// Config 应用配置结构
type Config struct {
	// 基础配置
	Mode string
	Port string

	// IP2Location配置
	IPV4DBPath string
	IPV6DBPath string

	// Facebook配置
	FacebookPixelID  string
	FacebookAPIToken string

	// 其他配置
	DefaultHoldRate int
	GlobalMode      bool
}

// NewConfig 创建新的配置实例
func NewConfig() *Config {
	return &Config{
		Mode:             os.Getenv("mode"),
		Port:             os.Getenv("PORT"),
		IPV4DBPath:       os.Getenv("IPV4DB_PATH"),
		IPV6DBPath:       os.Getenv("IPV6DB_PATH"),
		FacebookPixelID:  os.Getenv("FB_PIXEL_ID"),
		FacebookAPIToken: os.Getenv("FB_API_TOKEN"),
		DefaultHoldRate:  50, // 默认hold rate
		GlobalMode:       false,
	}
}

func init() {
	log.Printf("[%s] env initial", configName)

	// 加载环境变量
	err := godotenv.Load()
	if err != nil {
		log.Printf("[%s] Error loading .env file", configName)
		//log.Fatal("Error loading .env file")
	}

	// 设置 gin 模式
	mode := os.Getenv("mode")
	if mode != "" {
		gin.SetMode(mode)
	}

	/*	if err := logger.InitLogger(); err != nil {
			panic(err)
		}
		// 设置时区为德国柏林时间
		berlinLocation, err := time.LoadLocation("Europe/Berlin")
		if err != nil {
			log.Printf("[%s] Failed to load Berlin timezone: %v", configName, err)
			return
		}
		time.Local = berlinLocation*/
}
