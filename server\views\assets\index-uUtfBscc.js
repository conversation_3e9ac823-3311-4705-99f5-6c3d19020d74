import{_ as xe,u as Ce,o as $e,r as v,a as Oe,M as d,j as J,c as Q,b as k,d as n,w as o,e as a,B as De,k as X,f as $,h as g,l as Ie,m as U,n as P,t as Te,F as Ke,g as Pe,I as Ne,p as Ue,q as Y,v as Z,s as b}from"./index-DlVegDiC.js";import{S as ze,a as Re}from"./index-CSU5nP3m.js";import{P as je}from"./PlusOutlined-Cg2o2XQN.js";import{_ as Je}from"./index-DXcpAzs8.js";import{_ as qe}from"./index-Dj7iX41A.js";import{_ as Fe}from"./index-BrFZluVG.js";import{_ as Ae}from"./index-1uCBjWky.js";const Le={class:"main"},Me={class:"filter"},Be={class:"filter_item"},Ee={class:"inner_table"},Ve={class:"pb-10"},We={class:"bt_20"},Ge={__name:"index",setup(He){const h="",c=Ce(),q=Oe();$e(()=>{D()});const F=v({status:"1"}),r=v({data:[],loading:!1,expandedRowKeys:[""],innerData:[],innerLoading:!1}),O=v({total:0,current:1,pageSize:10,position:["bottomCenter"],showSizeChanger:!0,pageSizeOptions:["10","20","30"],showTotal:t=>`共 ${t} 项`}),x=v({total:0,current:1,pageSize:10,position:["bottomCenter"],showSizeChanger:!1,showTotal:t=>`共 ${t} 项`}),D=()=>{r.loading=!0,fetch(`${h}/MainDirectory/list`,{method:"POST",body:JSON.stringify({directoryStatus:F.status,page:O.current,limit:O.pageSize}),headers:{token:c.token}}).then(t=>t.json()).then(t=>{console.log(t),t.code==1?(r.data=t.data.data.map(e=>(e.checked=e.status==1,e.checked_loading=!1,e)),O.total=Number(t.data.total)):t.code==3e3?(c.$patch({token:!1}),q.push("/login"),d.error({title:t.msg})):(r.data=[],d.error({title:t.msg})),r.loading=!1}).catch(t=>{d.error({title:"服务器错误",content:`${t}`})})},ee=t=>{O.current=t.current,O.pageSize=t.pageSize,D()},te=(t,e)=>{console.log(t),console.log(e),t?(r.expandedRowKeys[0]=e.id,r.innerData=[],x.current=1,I()):r.expandedRowKeys=[]},I=()=>{r.innerLoading=!0,fetch(`${h}/TwoDirectory/list`,{method:"POST",body:JSON.stringify({mianId:r.expandedRowKeys[0],page:x.current,limit:x.pageSize}),headers:{token:c.token}}).then(t=>t.json()).then(t=>{console.log(t),t.code==1?(r.innerData=t.data.data.map(e=>(e.checked=e.status==1,e.checked_loading=!1,e)),x.total=Number(t.data.total)):t.code==3e3?(c.$patch({token:!1}),q.push("/login"),d.error({title:t.msg})):(r.innerData=[],d.error({title:t.msg})),r.innerLoading=!1}).catch(t=>{d.error({title:"服务器错误",content:`${t}`})})},ne=t=>{x.current=t.current,x.pageSize=t.pageSize,I()},E=J(),w=v({open:!1,text:"",loading:!1}),oe=()=>{w.open=!0},ae=()=>{w.loading=!0,fetch(`${h}/MainDirectory/add`,{method:"POST",body:JSON.stringify({name:w.text}),headers:{token:c.token}}).then(t=>t.json()).then(t=>{console.log(t),t.code==1?(b.success("添加成功"),D(),E.value.resetFields()):d.error({title:t.msg}),w.open=!1,w.loading=!1}).catch(t=>{d.error({title:"服务器错误",content:`${t}`})})},le=(t,e)=>{event.stopPropagation(),console.log(t),e.checked_loading=!0,fetch(`${h}/MainDirectory/update`,{method:"POST",body:JSON.stringify({id:e.id,status:t?1:2}),headers:{token:c.token}}).then(p=>p.json()).then(p=>{console.log(p),p.code==1?(b.success("更新成功"),D()):d.error({title:p.msg}),e.checked_loading=!1}).catch(p=>{d.error({title:"服务器错误",content:`${p}`})})},de=t=>{fetch(`${h}/MainDirectory/del`,{method:"POST",body:JSON.stringify({id:t.id}),headers:{token:c.token}}).then(e=>e.json()).then(e=>{console.log(e),e.code==1?(b.success(e.msg),D()):d.error({title:e.msg})}).catch(e=>{d.error({title:"服务器错误",content:`${e}`})})},V=J(),s=v({open:!1,loading:!1,id:0,text:"",lang:"",keyword:"",title:""}),ie=t=>{s.id=t.id,s.open=!0},se=()=>{s.loading=!0,fetch(`${h}/TwoDirectory/add`,{method:"POST",body:JSON.stringify({mianId:s.id,name:s.text,lang:s.lang,main_keyword:s.keyword,title:s.title}),headers:{token:c.token}}).then(t=>t.json()).then(t=>{console.log(t),t.code==1?(b.success("添加成功"),r.expandedRowKeys[0]&&I(),V.value.resetFields()):d.error({title:t.msg}),s.open=!1,s.loading=!1}).catch(t=>{d.error({title:"服务器错误",content:`${t}`})})},re=J(),i=v({open:!1,loading:!1,id:0,text:"",lang:"",keyword:"",title:""}),pe=t=>{console.log(t),i.id=t.id,i.text=t.name,i.lang=t.lang,i.keyword=t.main_keyword,i.title=t.title,i.open=!0},ue=()=>{i.loading=!0,fetch(`${h}/TwoDirectory/update`,{method:"POST",body:JSON.stringify({id:i.id,name:i.text,lang:i.lang,main_keyword:i.keyword,title:i.title}),headers:{token:c.token}}).then(t=>t.json()).then(t=>{console.log(t),t.code==1?(b.success("修改成功"),r.expandedRowKeys[0]&&I()):d.error({title:t.msg}),i.open=!1,i.loading=!1}).catch(t=>{d.error({title:"服务器错误",content:`${t}`})})},ge=(t,e)=>{event.stopPropagation(),console.log(t),e.checked_loading=!0,fetch(`${h}/TwoDirectory/updateStatus`,{method:"POST",body:JSON.stringify({id:e.id,status:t?1:2}),headers:{token:c.token}}).then(p=>p.json()).then(p=>{console.log(p),p.code==1?(b.success("更新成功"),r.expandedRowKeys[0]&&I()):d.error({title:p.msg}),e.checked_loading=!1}).catch(p=>{d.error({title:"服务器错误",content:`${p}`})})},ce=t=>{fetch(`${h}/TwoDirectory/del`,{method:"POST",body:JSON.stringify({id:t.id}),headers:{token:c.token}}).then(e=>e.json()).then(e=>{console.log(e),e.code==1?(b.success(e.msg),r.expandedRowKeys[0]&&I()):d.error({title:e.msg})}).catch(e=>{d.error({title:"服务器错误",content:`${e}`})})},u=v({type:"table",id:0,open:!1,data:[],loading:!1}),T=v({total:0,current:1,pageSize:10,position:["bottomCenter"],showSizeChanger:!0,pageSizeOptions:["10","20","30"],showTotal:t=>`共 ${t} 项`}),fe=t=>{u.id=t.id,N(),u.open=!0},N=()=>{u.loading=!0,fetch(`${h}/KeyWords/list`,{method:"POST",body:JSON.stringify({twoDirectoryId:u.id,page:T.current,limit:T.pageSize}),headers:{token:c.token}}).then(t=>t.json()).then(t=>{console.log(t),t.code==1?(u.data=t.data.data,T.total=Number(t.data.total)):t.code==3e3?(c.$patch({token:!1}),q.push("/login"),d.error({title:t.msg})):(u.data=[],d.error({title:t.msg})),u.loading=!1}).catch(t=>{d.error({title:"服务器错误",content:`${t}`})})},me=t=>{T.current=t.current,T.pageSize=t.pageSize,N()},W=J(),S=v({open:!1,text:"",loading:!1}),G=t=>{u.type=t},ye=()=>{S.loading=!0,fetch(`${h}/KeyWords/add`,{method:"POST",body:JSON.stringify({twoDirectoryId:u.id,name:S.text}),headers:{token:c.token}}).then(t=>t.json()).then(t=>{console.log(t),t.code==1?(b.success("添加成功"),N(),u.type="table",W.value.resetFields()):d.error({title:t.msg}),S.open=!1,S.loading=!1}).catch(t=>{d.error({title:"服务器错误",content:`${t}`})})},ke=t=>{fetch(`${h}/KeyWords/del`,{method:"POST",body:JSON.stringify({id:t.id}),headers:{token:c.token}}).then(e=>e.json()).then(e=>{console.log(e),e.code==1?(b.success(e.msg),N()):d.error({title:e.msg})}).catch(e=>{d.error({title:"服务器错误",content:`${e}`})})},he=()=>{fetch(`${h}/KeyWords/delAll`,{method:"POST",body:JSON.stringify({twoDirectoryId:u.id}),headers:{token:c.token}}).then(t=>t.json()).then(t=>{console.log(t),t.code==1?(b.success(t.msg),N()):d.error({title:t.msg})}).catch(t=>{d.error({title:"服务器错误",content:`${t}`})})},_e=()=>{event.stopPropagation()},we=[{title:"ID",dataIndex:"id",key:"id",align:"center"},{title:"名称",dataIndex:"name",key:"name",align:"center"},{title:"状态",dataIndex:"status",key:"status",align:"center"},{title:"操作",key:"action",align:"center"}],ve=[{title:"ID",dataIndex:"id",key:"id",align:"center"},{title:"名称",dataIndex:"name",key:"name",align:"center"},{title:"状态",dataIndex:"status",key:"status",align:"center"},{title:"操作",key:"action",align:"center"}],be=[{title:"ID",dataIndex:"id",key:"id",align:"center"},{title:"名称",dataIndex:"name",key:"name",align:"center"},{title:"操作",key:"action",align:"center"}];return(t,e)=>{const p=Re,Se=ze,A=je,f=De,H=Je,z=Fe,L=qe,M=Ae,C=Ne,m=Pe,R=Ke,j=d,B=Ue;return $(),Q(X,null,[k("div",Le,[k("div",null,[k("div",Me,[k("div",Be,[n(Se,{ref:"select",value:a(F).status,"onUpdate:value":e[0]||(e[0]=l=>a(F).status=l),style:{width:"300px"},onChange:D},{default:o(()=>[n(p,{value:"0"},{default:o(()=>e[17]||(e[17]=[g("全部")])),_:1}),n(p,{value:"1"},{default:o(()=>e[18]||(e[18]=[g("激活")])),_:1}),n(p,{value:"2"},{default:o(()=>e[19]||(e[19]=[g("关闭")])),_:1})]),_:1},8,["value"])]),k("div",null,[n(f,{type:"primary",onClick:oe},{icon:o(()=>[n(A)]),default:o(()=>[e[20]||(e[20]=g(" 添加主目录 "))]),_:1})])])]),n(M,{columns:we,"data-source":a(r).data,rowKey:"id",pagination:a(O),loading:a(r).loading,expandedRowKeys:a(r).expandedRowKeys,onChange:ee,onExpand:te,expandRowByClick:""},{bodyCell:o(({column:l,record:_})=>[l.key==="status"?($(),U(H,{key:0,checked:_.checked,"onUpdate:checked":y=>_.checked=y,loading:_.checked_loading,onChange:y=>le(y,_)},null,8,["checked","onUpdate:checked","loading","onChange"])):P("",!0),l.key==="action"?($(),U(z,{key:1,title:"确认删除？",onConfirm:y=>de(_),onOpenChange:_e},{default:o(()=>[n(f,{type:"link",danger:""},{default:o(()=>e[21]||(e[21]=[g("删除")])),_:1})]),_:2},1032,["onConfirm"])):P("",!0)]),expandedRowRender:o(({record:l})=>[k("div",Ee,[k("div",Ve,[n(f,{type:"primary",onClick:Ie(_=>ie(l),["stop"])},{icon:o(()=>[n(A)]),default:o(()=>[e[22]||(e[22]=g(" 添加二级目录 "))]),_:2},1032,["onClick"])]),n(M,{columns:ve,"data-source":a(r).innerData,pagination:a(x),loading:a(r).innerLoading,onChange:ne,rowKey:"id",size:"small"},{bodyCell:o(({column:_,record:y})=>[_.key==="name"?($(),U(f,{key:0,type:"link",onClick:K=>fe(y)},{default:o(()=>[g(Te(y.name),1)]),_:2},1032,["onClick"])):P("",!0),_.key==="status"?($(),U(H,{key:1,checked:y.checked,"onUpdate:checked":K=>y.checked=K,loading:y.checked_loading,onChange:K=>ge(K,y)},null,8,["checked","onUpdate:checked","loading","onChange"])):P("",!0),_.key==="action"?($(),Q(X,{key:2},[n(f,{type:"link",onClick:K=>pe(y)},{default:o(()=>e[23]||(e[23]=[g(" 修改 ")])),_:2},1032,["onClick"]),n(L,{type:"vertical"}),n(z,{title:"确认删除？",onConfirm:K=>ce(y)},{default:o(()=>[n(f,{type:"link",danger:""},{default:o(()=>e[24]||(e[24]=[g("删除")])),_:1})]),_:2},1032,["onConfirm"])],64)):P("",!0)]),_:2},1032,["data-source","pagination","loading"])])]),_:1},8,["data-source","pagination","loading","expandedRowKeys"])]),n(j,{open:a(w).open,"onUpdate:open":e[2]||(e[2]=l=>a(w).open=l),title:"添加主目录",footer:null,maskClosable:!1},{default:o(()=>[e[26]||(e[26]=k("div",{style:{height:"20px"}},null,-1)),n(R,{ref_key:"add_form",ref:E,model:a(w),onFinish:ae,"label-col":{span:4},"wrapper-col":{span:16}},{default:o(()=>[n(m,{label:"名称",name:"text",rules:[{required:!0,message:"请输入名称"}]},{default:o(()=>[n(C,{value:a(w).text,"onUpdate:value":e[1]||(e[1]=l=>a(w).text=l),placeholder:"名称"},null,8,["value"])]),_:1}),n(m,{"wrapper-col":{offset:4,span:16}},{default:o(()=>[n(f,{type:"primary","html-type":"submit",loading:a(w).loading},{default:o(()=>e[25]||(e[25]=[g(" 提交 ")])),_:1},8,["loading"])]),_:1})]),_:1},8,["model"])]),_:1},8,["open"]),n(j,{open:a(s).open,"onUpdate:open":e[7]||(e[7]=l=>a(s).open=l),title:"添加二级目录",footer:null,maskClosable:!1},{default:o(()=>[e[28]||(e[28]=k("div",{style:{height:"20px"}},null,-1)),n(R,{ref_key:"add_inner_form",ref:V,model:a(s),onFinish:se,"label-col":{span:5},"wrapper-col":{span:16}},{default:o(()=>[n(m,{label:"名称",name:"text",rules:[{required:!0,message:"请输入名称"}]},{default:o(()=>[n(C,{value:a(s).text,"onUpdate:value":e[3]||(e[3]=l=>a(s).text=l),placeholder:"名称"},null,8,["value"])]),_:1}),n(m,{label:"语言",name:"lang",rules:[{required:!0,message:"请输入语言"}]},{default:o(()=>[n(C,{value:a(s).lang,"onUpdate:value":e[4]||(e[4]=l=>a(s).lang=l),placeholder:"语言"},null,8,["value"])]),_:1}),n(m,{label:"核心关键词",name:"keyword",rules:[{required:!0,message:"请输入核心关键词"}]},{default:o(()=>[n(C,{value:a(s).keyword,"onUpdate:value":e[5]||(e[5]=l=>a(s).keyword=l),placeholder:"核心关键词"},null,8,["value"])]),_:1}),n(m,{label:"标题",name:"title",rules:[{required:!0,message:"请输入标题"}]},{default:o(()=>[n(B,{value:a(s).title,"onUpdate:value":e[6]||(e[6]=l=>a(s).title=l),placeholder:"标题",rows:4},null,8,["value"])]),_:1}),n(m,{"wrapper-col":{offset:5,span:16}},{default:o(()=>[n(f,{type:"primary","html-type":"submit",loading:a(s).loading},{default:o(()=>e[27]||(e[27]=[g(" 提交 ")])),_:1},8,["loading"])]),_:1})]),_:1},8,["model"])]),_:1},8,["open"]),n(j,{open:a(i).open,"onUpdate:open":e[12]||(e[12]=l=>a(i).open=l),title:"修改二级目录",footer:null,maskClosable:!1},{default:o(()=>[e[30]||(e[30]=k("div",{style:{height:"20px"}},null,-1)),n(R,{ref_key:"edit_inner_form",ref:re,model:a(i),onFinish:ue,"label-col":{span:5},"wrapper-col":{span:16}},{default:o(()=>[n(m,{label:"名称",name:"text",rules:[{required:!0,message:"请输入名称"}]},{default:o(()=>[n(C,{value:a(i).text,"onUpdate:value":e[8]||(e[8]=l=>a(i).text=l),placeholder:"名称"},null,8,["value"])]),_:1}),n(m,{label:"语言",name:"lang",rules:[{required:!0,message:"请输入语言"}]},{default:o(()=>[n(C,{value:a(i).lang,"onUpdate:value":e[9]||(e[9]=l=>a(i).lang=l),placeholder:"语言"},null,8,["value"])]),_:1}),n(m,{label:"核心关键词",name:"keyword",rules:[{required:!0,message:"请输入核心关键词"}]},{default:o(()=>[n(C,{value:a(i).keyword,"onUpdate:value":e[10]||(e[10]=l=>a(i).keyword=l),placeholder:"核心关键词"},null,8,["value"])]),_:1}),n(m,{label:"标题",name:"title",rules:[{required:!0,message:"请输入标题"}]},{default:o(()=>[n(B,{value:a(i).title,"onUpdate:value":e[11]||(e[11]=l=>a(i).title=l),placeholder:"标题",rows:4},null,8,["value"])]),_:1}),n(m,{"wrapper-col":{offset:5,span:16}},{default:o(()=>[n(f,{type:"primary","html-type":"submit",loading:a(i).loading},{default:o(()=>e[29]||(e[29]=[g(" 提交 ")])),_:1},8,["loading"])]),_:1})]),_:1},8,["model"])]),_:1},8,["open"]),n(j,{open:a(u).open,"onUpdate:open":e[16]||(e[16]=l=>a(u).open=l),title:a(u).type=="table"?"关键词列表":"添加关键词",footer:null,maskClosable:!1,width:800},{default:o(()=>[e[36]||(e[36]=k("div",{style:{height:"20px"}},null,-1)),Y(k("div",null,[n(R,{ref_key:"keys_add_ref",ref:W,model:a(S),onFinish:ye,"label-col":{span:6},"wrapper-col":{span:16}},{default:o(()=>[n(m,{name:"text",rules:[{required:!0,message:"请输入关键词"}]},{default:o(()=>[n(B,{value:a(S).text,"onUpdate:value":e[13]||(e[13]=l=>a(S).text=l),placeholder:"如需填写多个关键词，请换行填写，每行一个",rows:4},null,8,["value"])]),_:1}),n(m,null,{default:o(()=>[n(f,{type:"primary","html-type":"submit",loading:a(S).loading},{default:o(()=>e[31]||(e[31]=[g(" 提交 ")])),_:1},8,["loading"]),n(L,{type:"vertical"}),n(f,{onClick:e[14]||(e[14]=l=>G("table"))},{default:o(()=>e[32]||(e[32]=[g("取消")])),_:1})]),_:1})]),_:1},8,["model"])],512),[[Z,a(u).type=="add"]]),Y(k("div",null,[k("div",We,[n(f,{type:"primary",onClick:e[15]||(e[15]=l=>G("add"))},{icon:o(()=>[n(A)]),default:o(()=>[e[33]||(e[33]=g(" 添加关键词 "))]),_:1}),n(L,{type:"vertical"}),n(z,{title:"确认清空关键词列表",onConfirm:he},{default:o(()=>[n(f,{danger:""},{default:o(()=>e[34]||(e[34]=[g("清空列表")])),_:1})]),_:1})]),n(M,{columns:be,"data-source":a(u).data,rowKey:"id",pagination:a(T),loading:a(u).loading,onChange:me,bordered:"",size:"small"},{bodyCell:o(({column:l,record:_})=>[l.key==="action"?($(),U(z,{key:0,title:"确认删除？",onConfirm:y=>ke(_)},{default:o(()=>[n(f,{type:"link",danger:""},{default:o(()=>e[35]||(e[35]=[g("删除")])),_:1})]),_:2},1032,["onConfirm"])):P("",!0)]),_:1},8,["data-source","pagination","loading"])],512),[[Z,a(u).type=="table"]])]),_:1},8,["open","title"])],64)}}},ot=xe(Ge,[["__scopeId","data-v-b3faf424"]]);export{ot as default};
