package service

import (
	"context"
	"fmt"
	"log"
	"rsoc-system-go/dao"
	"rsoc-system-go/model"
	"rsoc-system-go/store"
	"sync"
)

// SyncResult 同步结果
type SyncResult struct {
	Total       int      `json:"total"`       // 总记录数
	Synced      int      `json:"synced"`      // 已同步数
	Failed      int      `json:"failed"`      // 失败数
	Skipped     int      `json:"skipped"`     // 跳过数
	FailedItems []string `json:"failedItems"` // 失败项详情
}

// GoogleArticleSyncService GoogleArticle同步服务
type GoogleArticleSyncService struct {
	googleArticleDAO *dao.GoogleArticleDAO
	campaignService  *AdTechCampaignService
}

var (
	googleArticleSyncService     *GoogleArticleSyncService
	googleArticleSyncServiceOnce sync.Once
)

// GetGoogleArticleSyncService 获取GoogleArticle同步服务单例
func GetGoogleArticleSyncService() *GoogleArticleSyncService {
	googleArticleSyncServiceOnce.Do(func() {
		googleArticleSyncService = NewGoogleArticleSyncService(
			dao.NewGoogleArticleDAO(store.DB),
			GetAdTechCampaignService(),
		)
	})
	return googleArticleSyncService
}

// NewGoogleArticleSyncService 创建新的GoogleArticle同步服务
func NewGoogleArticleSyncService(
	googleArticleDAO *dao.GoogleArticleDAO,
	campaignService *AdTechCampaignService,
) *GoogleArticleSyncService {
	return &GoogleArticleSyncService{
		googleArticleDAO: googleArticleDAO,
		campaignService:  campaignService,
	}
}

// SyncGoogleArticles 同步GoogleArticle数据
func (s *GoogleArticleSyncService) SyncGoogleArticles(ctx context.Context) (*SyncResult, error) {
	result := &SyncResult{
		FailedItems: make([]string, 0),
	}

	// 查询status为0的GoogleArticle记录
	req := &model.GoogleArticleListRequest{
		Status: 0,
	}
	daoGA := dao.NewGoogleArticleDAO(store.DB)
	articles, _, err := daoGA.List(req)
	if err != nil {
		return result, fmt.Errorf("查询待同步的GoogleArticle失败: %v", err)
	}

	result.Total = len(articles)
	log.Printf("找到 %d 条待同步的GoogleArticle记录", result.Total)

	// 遍历记录并同步状态
	for _, article := range articles {
		if article.CampaignId == "" || article.Key == "" {
			log.Printf("GoogleArticle ID: %d 缺少CampaignId或Key，跳过同步", article.ID)
			result.Skipped++
			result.FailedItems = append(result.FailedItems, fmt.Sprintf("ID: %d - 缺少CampaignId或Key", article.ID))
			continue
		}

		// 调用GetCampaign获取最新状态
		response, err := s.campaignService.GetCampaign(ctx, article.Key, article.CampaignId)
		if err != nil {
			log.Printf("获取GoogleArticle ID: %d 的广告系列状态失败: %v", article.ID, err)
			result.Failed++
			result.FailedItems = append(result.FailedItems, fmt.Sprintf("ID: %d - 获取状态失败: %v", article.ID, err))
			continue
		}

		// 解析状态并更新GoogleArticle
		newStatus := 0 // 默认保持待审核状态
		var resultReason string
		if !response.Success {
			// API调用成功但业务失败
			newStatus = 2 // 拒绝
			resultReason = response.Message
		} else if response.Detail != nil {
			// 根据返回的状态更新
			switch response.Detail.Status {
			case "COMPLETED":
				newStatus = 1 // 通过
				resultReason = "通过"
				// 如果有落地页URL，更新FinalURL和URLWPPost
				if response.Detail.TrackingUrl != "" {
					article.FinalURL = response.Detail.TrackingUrl
					article.URLWPPost = response.Detail.TrackingUrl
					article.Campaign = response.Detail.Campaign.Id
					err = daoGA.Updates(&article)
					if err != nil {
						log.Printf("更新GoogleArticle ID: %d 的URL失败: %v", article.ID, err)
						// 不将这视为同步失败，因为状态已经正确更新
					}
				}
			case "PROCESSING":
				// 保持待审核状态
				resultReason = "处理中"
			case "PROCESSING_ERROR":
				newStatus = 2 // 拒绝
				if response.Detail.ProcessingErrorDetails != nil {
					resultReason = fmt.Sprintf("%s: %s",
						response.Detail.ProcessingErrorDetails.Title,
						response.Detail.ProcessingErrorDetails.Detail)
				} else {
					resultReason = "处理错误"
				}
			default:
				resultReason = response.Detail.Status
			}
		}

		// 只有当状态需要更新时才更新数据库
		if newStatus != 0 || resultReason != "" {
			err = daoGA.UpdateStatus(article.ID, newStatus, "", resultReason, article.Campaign)
			if err != nil {
				log.Printf("更新GoogleArticle ID: %d 的状态失败: %v", article.ID, err)
				result.Failed++
				result.FailedItems = append(result.FailedItems, fmt.Sprintf("ID: %d - 更新状态失败: %v", article.ID, err))
				continue
			}
			log.Printf("成功更新GoogleArticle ID: %d 的状态为 %d，原因: %s", article.ID, newStatus, resultReason)
			result.Synced++
		} else {
			// 没有需要更新的内容，视为已同步
			result.Synced++
		}
	}

	return result, nil
}

// SyncGoogleArticleByID 同步指定ID的GoogleArticle数据
func (s *GoogleArticleSyncService) SyncGoogleArticleByID(ctx context.Context, id uint) (*model.GoogleArticle, error) {
	// 获取指定ID的GoogleArticle
	article, err := s.googleArticleDAO.GetByID(id)
	if err != nil {
		return nil, fmt.Errorf("获取GoogleArticle ID: %d 失败: %v", id, err)
	}

	if article.CampaignId == "" || article.Key == "" {
		return article, fmt.Errorf("GoogleArticle ID: %d 缺少CampaignId或Key", id)
	}

	// 调用GetCampaign获取最新状态
	response, err := s.campaignService.GetCampaign(ctx, article.Key, article.CampaignId)
	if err != nil {
		return article, fmt.Errorf("获取GoogleArticle ID: %d 的广告系列状态失败: %v", id, err)
	}

	// 解析状态并更新GoogleArticle
	newStatus := 0 // 默认保持待审核状态
	var resultReason string

	if !response.Success {
		// API调用成功但业务失败
		newStatus = 2 // 拒绝
		resultReason = response.Message
	} else if response.Detail != nil {
		// 根据返回的状态更新
		switch response.Detail.Status {
		case "COMPLETED":
			newStatus = 1 // 通过
			resultReason = "通过"
			// 如果有落地页URL，更新FinalURL和URLWPPost
			if response.Detail.TrackingUrl != "" {
				article.FinalURL = response.Detail.TrackingUrl
				article.URLWPPost = response.Detail.TrackingUrl
			}
		case "PROCESSING":
			// 保持待审核状态
			resultReason = "处理中"
		case "PROCESSING_ERROR":
			newStatus = 2 // 拒绝
			if response.Detail.ProcessingErrorDetails != nil {
				resultReason = fmt.Sprintf("%s: %s",
					response.Detail.ProcessingErrorDetails.Title,
					response.Detail.ProcessingErrorDetails.Detail)
			} else {
				resultReason = "处理错误"
			}
		default:
			resultReason = response.Detail.Status
		}
	}

	// 更新状态
	article.Status = newStatus
	article.ResultReason = resultReason

	// 保存更新
	err = s.googleArticleDAO.Updates(article)
	if err != nil {
		return article, fmt.Errorf("更新GoogleArticle ID: %d 失败: %v", id, err)
	}

	return article, nil
}
