package handler

import (
	"database/sql"
	"net/http"
	"rsoc-system-go/dao"
	"rsoc-system-go/model"
	"rsoc-system-go/service"
	"rsoc-system-go/store"
	"strconv"
	"time"

	"github.com/gin-gonic/gin"
	"github.com/sirupsen/logrus"
)

type PostBackHandler struct {
	postbackService     *service.PostbackService
	facebookInsightsDao *dao.FacebookInsightsDAO
	logger              *logrus.Logger
}

// NewPostBackHandler 创建新的PostBackHandler实例
func NewPostBackHandler() *PostBackHandler {
	logger := logrus.New()
	return &PostBackHandler{
		postbackService:     service.GetPostbackService(),
		facebookInsightsDao: dao.NewFacebookInsightsDAO(store.DB),
		logger:              logger,
	}
}

// RsocHandler 处理第三方postback回调数据接收
// 这是专门用于第三方平台回传转化数据的回调接口
func (h *PostBackHandler) RsocHandler(c *gin.Context) {
	// /rsoc/postback 专门用于第三方回调数据接收
	// 支持的参数格式：campaign, click_id, payout/epayout, country, zip, os_type, browser, device_type, device_brand, s1-s3, subid1-subid5
	h.handlePostbackReceive(c)
}

// SetupHandler 处理postback配置设置
func (h *PostBackHandler) SetupHandler(c *gin.Context) {
	// 专门用于postback配置设置
	h.handlePostbackSetup(c)
}

// SedoTMPCallbackHandler 处理SedoTMP平台的回调数据接收
// 支持SedoTMP标准宏参数格式
func (h *PostBackHandler) SedoTMPCallbackHandler(c *gin.Context) {
	// 专门用于SedoTMP平台回调数据接收
	// 支持的参数：campaign, click_id, epayout, country, country_name, state, city, zip, os_type, browser, device_type, device_brand, subid1-subid5
	h.handlePostbackReceive(c)
}

// handlePostbackSetup 处理postback设置
func (h *PostBackHandler) handlePostbackSetup(c *gin.Context) {
	var request model.PostbackRequest

	// 解析请求参数
	if err := c.ShouldBind(&request); err != nil {
		h.logger.Errorf("解析postback请求参数失败: %v", err)
		c.JSON(http.StatusBadRequest, model.PostbackResponse{
			Code: 400,
			Msg:  "请求参数格式错误: " + err.Error(),
			Time: time.Now().Unix(),
		})
		return
	}

	// 验证请求参数
	if err := h.postbackService.ValidatePostbackRequest(&request); err != nil {
		h.logger.Errorf("postback请求参数验证失败: %v", err)
		c.JSON(http.StatusBadRequest, model.PostbackResponse{
			Code: 400,
			Msg:  err.Error(),
			Time: time.Now().Unix(),
		})
		return
	}

	// 调用AdTech API设置postback
	ctx := c.Request.Context()
	response, err := h.postbackService.SetPostbackDomain(ctx, &request)
	if err != nil {
		h.logger.Errorf("调用AdTech API设置postback失败: %v", err)
		c.JSON(http.StatusInternalServerError, model.PostbackResponse{
			Code: 500,
			Msg:  "设置postback失败: " + err.Error(),
			Time: time.Now().Unix(),
		})
		return
	}

	// 保存配置到数据库
	if err := h.savePostbackConfig(&request); err != nil {
		h.logger.Errorf("保存postback配置失败: %v", err)
		// 不影响API调用结果，只记录日志
	}

	h.logger.Infof("postback设置成功: domain=%s", request.DomainName)
	c.JSON(http.StatusOK, response)
}

// handlePostbackReceive 处理postback数据接收
func (h *PostBackHandler) handlePostbackReceive(c *gin.Context) {
	var data model.PostbackData

	// 解析postback数据
	if err := c.ShouldBind(&data); err != nil {
		h.logger.Errorf("解析postback数据失败: %v", err)
		c.JSON(http.StatusBadRequest, gin.H{"code": 400, "msg": "数据格式错误"})
		return
	}

	// 记录接收到的postback数据
	h.logger.Infof("接收到postback数据: %+v", data)

	// 处理转化数据，更新到 FacebookInsights 表
	if err := h.processPostbackData(&data); err != nil {
		h.logger.Errorf("处理postback数据失败: %v", err)
		c.JSON(http.StatusInternalServerError, gin.H{"code": 500, "msg": "处理数据失败"})
		return
	}

	c.JSON(http.StatusOK, gin.H{"code": 1, "msg": "ok"})
}

// savePostbackConfig 保存postback配置（简化版，仅记录日志）
func (h *PostBackHandler) savePostbackConfig(request *model.PostbackRequest) error {
	// 记录配置信息到日志
	h.logger.Infof("Postback配置已设置: domain=%s, security_key=%s", request.DomainName, request.SecurityKey)

	// 这里可以根据需要将配置信息存储到其他地方
	// 例如：Redis、配置文件等

	return nil
}

// processPostbackData 处理postback数据，更新到FacebookInsights表
func (h *PostBackHandler) processPostbackData(data *model.PostbackData) error {
	// 从postback数据中提取必要信息
	campaignID := data.SubID2
	if campaignID == "" && data.Campaign == "" {
		return nil // 如果没有campaign信息，跳过处理
	}

	// 优先使用Campaign字段，其次使用SubID2
	if campaignID == "" {
		campaignID = data.Campaign
	}

	// 解析收益信息（优先使用payout，其次epayout，最后使用revenue）
	var realPrice *float64
	var revenue *float64

	effectivePayout := data.GetEffectivePayout()
	if effectivePayout != "" {
		if price, err := strconv.ParseFloat(effectivePayout, 64); err == nil {
			realPrice = &price
		}
	}

	// 解析revenue字段
	if data.Revenue != "" {
		if rev, err := strconv.ParseFloat(data.Revenue, 64); err == nil {
			revenue = &rev
		}
	}

	// 获取当前日期
	currentDate := time.Now().Format("2006-01-02")

	// 查找现有记录
	conditions := map[string]interface{}{
		"campaign_id": campaignID,
		"date":        currentDate,
	}
	h.facebookInsightsDao = dao.NewFacebookInsightsDAO(store.DB)
	existingInsight, err := h.facebookInsightsDao.FindOne(conditions)
	if err != nil && err.Error() != "record not found" {
		return err
	}

	if existingInsight != nil {
		// 更新现有记录
		h.updateExistingInsight(existingInsight, data, realPrice, revenue)
		return h.facebookInsightsDao.Update(existingInsight)
	} else {
		// 创建新记录
		newInsight := h.createNewInsight(data, campaignID, currentDate, realPrice, revenue)
		return h.facebookInsightsDao.Create(newInsight)
	}
}

// updateExistingInsight 更新现有的FacebookInsights记录
func (h *PostBackHandler) updateExistingInsight(insight *model.FacebookInsights, data *model.PostbackData, realPrice *float64, revenue *float64) {
	// 更新基础字段
	if realPrice != nil {
		insight.RealPrice = realPrice
	}
	if revenue != nil {
		insight.Revenue = revenue
	}

	// 更新扩展字段
	if data.CampaignName != "" {
		insight.CampaignName = data.CampaignName
	}
	if data.Platform != "" {
		insight.Platform = data.Platform
	}
	if data.Country != "" {
		insight.Country = data.Country
	}

	// 解析并更新数值字段
	h.parseAndUpdateNumericFields(insight, data)

	// 更新计数器
	insight.Clicks++
	insight.UpdateTime = sql.NullTime{Time: time.Now(), Valid: true}
}

// createNewInsight 创建新的FacebookInsights记录
func (h *PostBackHandler) createNewInsight(data *model.PostbackData, campaignID, currentDate string, realPrice *float64, revenue *float64) *model.FacebookInsights {
	insight := &model.FacebookInsights{
		Date:        currentDate,
		UserID:      1, // 默认用户ID，可以根据需要调整
		CampaignID:  campaignID,
		AdID:        campaignID,
		AdsetID:     campaignID,
		Reach:       0,
		Frequency:   0,
		Impressions: 0,
		Clicks:      1, // postback通常表示一次转化/点击
		Spend:       0,
		CPM:         0,
		UpdateTime:  sql.NullTime{Time: time.Now(), Valid: true},
		RealPrice:   realPrice,
		Revenue:     revenue,
	}

	// 设置扩展字段
	if data.CampaignName != "" {
		insight.CampaignName = data.CampaignName
	}
	if data.Platform != "" {
		insight.Platform = data.Platform
	}
	if data.Country != "" {
		insight.Country = data.Country
	}

	// 解析并设置数值字段
	h.parseAndUpdateNumericFields(insight, data)

	// 解析创建时间
	if data.CreateTime != "" {
		if createTime, err := time.Parse("2006-01-02 15:04:05", data.CreateTime); err == nil {
			insight.CreateTime = &createTime
		}
	}

	return insight
}

// parseAndUpdateNumericFields 解析并更新数值字段
func (h *PostBackHandler) parseAndUpdateNumericFields(insight *model.FacebookInsights, data *model.PostbackData) {
	// 解析小时
	if data.Hour != "" {
		if hour, err := strconv.Atoi(data.Hour); err == nil {
			insight.Hour = hour
		}
	}

	// 解析相关链接数据
	if data.RelatedLinksRequests != "" {
		if val, err := strconv.Atoi(data.RelatedLinksRequests); err == nil {
			insight.RelatedLinksRequests = val
		}
	}
	if data.RelatedLinksImpressions != "" {
		if val, err := strconv.Atoi(data.RelatedLinksImpressions); err == nil {
			insight.RelatedLinksImpressions = val
		}
	}
	if data.RelatedLinksClicks != "" {
		if val, err := strconv.Atoi(data.RelatedLinksClicks); err == nil {
			insight.RelatedLinksClicks = val
		}
	}
	if data.RelatedLinksRpm != "" {
		if val, err := strconv.ParseFloat(data.RelatedLinksRpm, 64); err == nil {
			insight.RelatedLinksRpm = val
		}
	}

	// 解析广告数据
	if data.AdRequests != "" {
		if val, err := strconv.Atoi(data.AdRequests); err == nil {
			insight.AdRequests = val
		}
	}
	if data.MatchedAdRequests != "" {
		if val, err := strconv.Atoi(data.MatchedAdRequests); err == nil {
			insight.MatchedAdRequests = val
		}
	}
	if data.AdImpressions != "" {
		if val, err := strconv.Atoi(data.AdImpressions); err == nil {
			insight.AdImpressions = val
		}
	}

	// 解析比率数据
	if data.CTR != "" {
		if val, err := strconv.ParseFloat(data.CTR, 64); err == nil {
			insight.CTR = val
		}
	}
	if data.AdCTR != "" {
		if val, err := strconv.ParseFloat(data.AdCTR, 64); err == nil {
			insight.AdCTR = val
		}
	}
	if data.AdRPM != "" {
		if val, err := strconv.ParseFloat(data.AdRPM, 64); err == nil {
			insight.AdRPM = val
		}
	}
	if data.CR != "" {
		if val, err := strconv.ParseFloat(data.CR, 64); err == nil {
			insight.CR = val
		}
	}
}
