package handler

import (
	"database/sql"
	"net/http"
	"rsoc-system-go/dao"
	"rsoc-system-go/model"
	"rsoc-system-go/service"
	"rsoc-system-go/store"
	"strconv"
	"time"

	"github.com/gin-gonic/gin"
	"github.com/sirupsen/logrus"
)

type PostBackHandler struct {
	postbackService     *service.PostbackService
	facebookInsightsDao *dao.FacebookInsightsDAO
	logger              *logrus.Logger
}

// NewPostBackHandler 创建新的PostBackHandler实例
func NewPostBackHandler() *PostBackHandler {
	logger := logrus.New()
	return &PostBackHandler{
		postbackService:     service.GetPostbackService(),
		facebookInsightsDao: dao.NewFacebookInsightsDAO(store.DB),
		logger:              logger,
	}
}

// RsocHandler 处理第三方postback回调数据接收
// 这是专门用于第三方平台回传转化数据的回调接口
func (h *PostBackHandler) RsocHandler(c *gin.Context) {
	// /rsoc/postback 专门用于第三方回调数据接收
	// 支持的参数格式：campaign, click_id, payout/epayout, country, zip, os_type, browser, device_type, device_brand, s1-s3, subid1-subid5
	h.handlePostbackReceive(c)
}

// SetupHandler 处理postback配置设置
func (h *PostBackHandler) SetupHandler(c *gin.Context) {
	// 专门用于postback配置设置
	h.handlePostbackSetup(c)
}

// SedoTMPCallbackHandler 处理SedoTMP平台的回调数据接收
// 支持SedoTMP标准宏参数格式
func (h *PostBackHandler) SedoTMPCallbackHandler(c *gin.Context) {
	// 专门用于SedoTMP平台回调数据接收
	// 支持的参数：campaign, click_id, epayout, country, country_name, state, city, zip, os_type, browser, device_type, device_brand, subid1-subid5
	h.handlePostbackReceive(c)
}

// handlePostbackSetup 处理postback设置
func (h *PostBackHandler) handlePostbackSetup(c *gin.Context) {
	var request model.PostbackRequest

	// 解析请求参数
	if err := c.ShouldBind(&request); err != nil {
		h.logger.Errorf("解析postback请求参数失败: %v", err)
		c.JSON(http.StatusBadRequest, model.PostbackResponse{
			Code: 400,
			Msg:  "请求参数格式错误: " + err.Error(),
			Time: time.Now().Unix(),
		})
		return
	}

	// 验证请求参数
	if err := h.postbackService.ValidatePostbackRequest(&request); err != nil {
		h.logger.Errorf("postback请求参数验证失败: %v", err)
		c.JSON(http.StatusBadRequest, model.PostbackResponse{
			Code: 400,
			Msg:  err.Error(),
			Time: time.Now().Unix(),
		})
		return
	}

	// 调用AdTech API设置postback
	ctx := c.Request.Context()
	response, err := h.postbackService.SetPostbackDomain(ctx, &request)
	if err != nil {
		h.logger.Errorf("调用AdTech API设置postback失败: %v", err)
		c.JSON(http.StatusInternalServerError, model.PostbackResponse{
			Code: 500,
			Msg:  "设置postback失败: " + err.Error(),
			Time: time.Now().Unix(),
		})
		return
	}

	// 保存配置到数据库
	if err := h.savePostbackConfig(&request); err != nil {
		h.logger.Errorf("保存postback配置失败: %v", err)
		// 不影响API调用结果，只记录日志
	}

	h.logger.Infof("postback设置成功: domain=%s", request.DomainName)
	c.JSON(http.StatusOK, response)
}

// handlePostbackReceive 处理postback数据接收
func (h *PostBackHandler) handlePostbackReceive(c *gin.Context) {
	var data model.PostbackData

	// 解析postback数据
	if err := c.ShouldBind(&data); err != nil {
		h.logger.Errorf("解析postback数据失败: %v", err)
		c.JSON(http.StatusBadRequest, gin.H{"code": 400, "msg": "数据格式错误"})
		return
	}

	// 记录接收到的postback数据
	h.logger.Infof("接收到postback数据: %+v", data)

	// 处理转化数据，更新到 FacebookInsights 表
	if err := h.processPostbackData(&data); err != nil {
		h.logger.Errorf("处理postback数据失败: %v", err)
		c.JSON(http.StatusInternalServerError, gin.H{"code": 500, "msg": "处理数据失败"})
		return
	}

	c.JSON(http.StatusOK, gin.H{"code": 1, "msg": "ok"})
}

// savePostbackConfig 保存postback配置（简化版，仅记录日志）
func (h *PostBackHandler) savePostbackConfig(request *model.PostbackRequest) error {
	// 记录配置信息到日志
	h.logger.Infof("Postback配置已设置: domain=%s, security_key=%s", request.DomainName, request.SecurityKey)

	// 这里可以根据需要将配置信息存储到其他地方
	// 例如：Redis、配置文件等

	return nil
}

// processPostbackData 处理postback数据，更新到FacebookInsights表
func (h *PostBackHandler) processPostbackData(data *model.PostbackData) error {
	// 从postback数据中提取必要信息
	campaignID := data.SubID2
	if campaignID == "" {
		return nil // 如果没有campaign信息，跳过处理
	}

	// 解析收益信息（优先使用payout，其次epayout）
	var realPrice *float64
	effectivePayout := data.GetEffectivePayout()
	if effectivePayout != "" {
		if price, err := strconv.ParseFloat(effectivePayout, 64); err == nil {
			realPrice = &price
		}
	}

	// 获取当前日期
	currentDate := time.Now().Format("2006-01-02")

	// 查找现有记录
	conditions := map[string]interface{}{
		"campaign_id": campaignID,
		"date":        currentDate,
	}
	h.facebookInsightsDao = dao.NewFacebookInsightsDAO(store.DB)
	existingInsight, err := h.facebookInsightsDao.FindOne(conditions)
	if err != nil && err.Error() != "record not found" {
		return err
	}

	if existingInsight != nil {
		// 更新现有记录的真实收益
		if realPrice != nil {
			existingInsight.RealPrice = realPrice
		}
		existingInsight.Clicks++
		existingInsight.UpdateTime = sql.NullTime{Time: time.Now(), Valid: true}

		return h.facebookInsightsDao.Update(existingInsight)
	} else {
		// 创建新记录
		newInsight := &model.FacebookInsights{
			Date:   currentDate,
			UserID: 1, // 默认用户ID，可以根据需要调整
			//AccountID:   campaignID,
			CampaignID:  campaignID,
			AdID:        campaignID,
			AdsetID:     campaignID,
			Reach:       0,
			Frequency:   0,
			Impressions: 0,
			Clicks:      1, // postback通常表示一次转化/点击
			Spend:       0,
			CPM:         0,
			UpdateTime:  sql.NullTime{Time: time.Now(), Valid: true},
			RealPrice:   realPrice,
		}

		return h.facebookInsightsDao.Create(newInsight)
	}
}
