package service

import (
	"context"
	"fmt"
	"os"
	"rsoc-system-go/model"
	"sync"
)

// SedoArticleService Sedo文章服务
type SedoArticleService struct {
	httpClient *SedoHTTPClient
}

var (
	sedoArticleService     *SedoArticleService
	sedoArticleServiceOnce sync.Once
)

// GetSedoArticleService 获取Sedo文章服务单例
func GetSedoArticleService() *SedoArticleService {
	sedoArticleServiceOnce.Do(func() {
		sedoArticleService = NewSedoArticleService(GetSedoHTTPClient())
	})
	// 方法1：使用简化的OAuth初始化
	sedoArticleService.InitializeWithOAuth(
		os.Getenv("SEDO_CLIENT_ID"),
		os.Getenv("SEDO_CLIENT_SECRET"),
	)
	return sedoArticleService
}

// NewSedoArticleService 创建新的Sedo文章服务
func NewSedoArticleService(httpClient *SedoHTTPClient) *SedoArticleService {
	return &SedoArticleService{
		httpClient: httpClient,
	}
}

// GetArticles 获取文章列表
func (s *SedoArticleService) GetArticles(ctx context.Context, page, size int, sort, term string) ([]model.SedoArticle, *model.SedoPageResponse, error) {
	query := s.httpClient.BuildQueryParams(page, size, sort, term)

	resp, err := s.httpClient.Get(ctx, "/articles", query, nil)
	if err != nil {
		return nil, nil, fmt.Errorf("获取文章列表请求失败: %v", err)
	}

	var articles []model.SedoArticle
	if err := s.httpClient.ParseResponse(resp, &articles); err != nil {
		return nil, nil, fmt.Errorf("解析文章列表响应失败: %v", err)
	}

	totalCount, totalPages, err := s.httpClient.GetPageHeaders(resp)
	if err != nil {
		return nil, nil, fmt.Errorf("获取分页信息失败: %v", err)
	}

	pageResponse := &model.SedoPageResponse{
		TotalCount: totalCount,
		TotalPages: totalPages,
	}

	return articles, pageResponse, nil
}

// GetArticleByID 根据ID获取文章
func (s *SedoArticleService) GetArticleByID(ctx context.Context, id string) (*model.SedoArticle, error) {
	resp, err := s.httpClient.Get(ctx, fmt.Sprintf("/articles/%s", id), nil, nil)
	if err != nil {
		return nil, fmt.Errorf("获取文章详情请求失败: %v", err)
	}

	var article model.SedoArticle
	if err := s.httpClient.ParseResponse(resp, &article); err != nil {
		return nil, fmt.Errorf("解析文章详情响应失败: %v", err)
	}

	return &article, nil
}

// CreateArticle 创建文章
func (s *SedoArticleService) CreateArticle(ctx context.Context, request *model.SedoCreateArticleRequest) (*model.SedoArticle, error) {
	resp, err := s.httpClient.Post(ctx, "/articles", nil, request, nil)
	if err != nil {
		return nil, fmt.Errorf("创建文章请求失败: %v", err)
	}

	var article model.SedoArticle
	if err := s.httpClient.ParseResponse(resp, &article); err != nil {
		return nil, fmt.Errorf("解析创建文章响应失败: %v", err)
	}

	return &article, nil
}

// UpdateArticle 更新文章
func (s *SedoArticleService) UpdateArticle(ctx context.Context, id string, request *model.SedoUpdateArticleRequest) (*model.SedoArticle, error) {
	resp, err := s.httpClient.Put(ctx, fmt.Sprintf("/articles/%s", id), nil, request, nil)
	if err != nil {
		return nil, fmt.Errorf("更新文章请求失败: %v", err)
	}

	var article model.SedoArticle
	if err := s.httpClient.ParseResponse(resp, &article); err != nil {
		return nil, fmt.Errorf("解析更新文章响应失败: %v", err)
	}

	return &article, nil
}

// PatchArticle 部分更新文章
func (s *SedoArticleService) PatchArticle(ctx context.Context, id string, request *model.SedoUpdateArticleRequest) (*model.SedoArticle, error) {
	resp, err := s.httpClient.Patch(ctx, fmt.Sprintf("/articles/%s", id), nil, request, nil)
	if err != nil {
		return nil, fmt.Errorf("部分更新文章请求失败: %v", err)
	}

	var article model.SedoArticle
	if err := s.httpClient.ParseResponse(resp, &article); err != nil {
		return nil, fmt.Errorf("解析部分更新文章响应失败: %v", err)
	}

	return &article, nil
}

// DeleteArticle 删除文章
func (s *SedoArticleService) DeleteArticle(ctx context.Context, id string) error {
	resp, err := s.httpClient.Delete(ctx, fmt.Sprintf("/articles/%s", id), nil, nil)
	if err != nil {
		return fmt.Errorf("删除文章请求失败: %v", err)
	}

	if err := s.httpClient.ParseResponse(resp, nil); err != nil {
		return fmt.Errorf("解析删除文章响应失败: %v", err)
	}

	return nil
}

// GenerateArticle 生成文章
func (s *SedoArticleService) GenerateArticle(ctx context.Context, request *model.SedoGenerateArticleRequest, async bool, referenceID string) (*model.SedoArticle, error) {
	headers := make(map[string]string)

	// 设置请求流程头
	if async {
		headers["X-Sedo-Request-Flow"] = "ASYNC"
	} else {
		headers["X-Sedo-Request-Flow"] = "SYNC"
	}

	// 设置引用ID头
	if referenceID != "" {
		headers["X-Sedo-Reference-Id"] = referenceID
	}

	resp, err := s.httpClient.Post(ctx, "/generated-articles", nil, request, headers)
	if err != nil {
		return nil, fmt.Errorf("生成文章请求失败: %v", err)
	}

	// 如果是异步请求且返回202状态码，则返回nil表示请求已接受
	if async && resp.StatusCode == 202 {
		return nil, nil
	}

	var article model.SedoArticle
	if err := s.httpClient.ParseResponse(resp, &article); err != nil {
		return nil, fmt.Errorf("解析生成文章响应失败: %v", err)
	}

	return &article, nil
}

// GetPublishedArticles 获取已发布文章列表
func (s *SedoArticleService) GetPublishedArticles(ctx context.Context, page, size int, sort, term string) ([]model.SedoPublishedArticle, *model.SedoPageResponse, error) {
	query := s.httpClient.BuildQueryParams(page, size, sort, term)

	resp, err := s.httpClient.Get(ctx, "/published-articles", query, nil)
	if err != nil {
		return nil, nil, fmt.Errorf("获取已发布文章列表请求失败: %v", err)
	}

	var articles []model.SedoPublishedArticle
	if err := s.httpClient.ParseResponse(resp, &articles); err != nil {
		return nil, nil, fmt.Errorf("解析已发布文章列表响应失败: %v", err)
	}

	totalCount, totalPages, err := s.httpClient.GetPageHeaders(resp)
	if err != nil {
		return nil, nil, fmt.Errorf("获取分页信息失败: %v", err)
	}

	pageResponse := &model.SedoPageResponse{
		TotalCount: totalCount,
		TotalPages: totalPages,
	}

	return articles, pageResponse, nil
}

// GetPublishedArticleByID 根据ID获取已发布文章
func (s *SedoArticleService) GetPublishedArticleByID(ctx context.Context, id string) (*model.SedoPublishedArticle, error) {
	resp, err := s.httpClient.Get(ctx, fmt.Sprintf("/published-articles/%s", id), nil, nil)
	if err != nil {
		return nil, fmt.Errorf("获取已发布文章详情请求失败: %v", err)
	}

	var article model.SedoPublishedArticle
	if err := s.httpClient.ParseResponse(resp, &article); err != nil {
		return nil, fmt.Errorf("解析已发布文章详情响应失败: %v", err)
	}

	return &article, nil
}

// PublishArticle 发布文章
func (s *SedoArticleService) PublishArticle(ctx context.Context, request *model.SedoPublishArticleRequest, async bool, referenceID string) (*model.SedoPublishedArticle, error) {
	headers := make(map[string]string)

	// 设置请求流程头
	if async {
		headers["X-Sedo-Request-Flow"] = "ASYNC"
	} else {
		headers["X-Sedo-Request-Flow"] = "SYNC"
	}

	// 设置引用ID头
	if referenceID != "" {
		headers["X-Sedo-Reference-Id"] = referenceID
	}

	resp, err := s.httpClient.Post(ctx, "/published-articles", nil, request, headers)
	if err != nil {
		return nil, fmt.Errorf("发布文章请求失败: %v", err)
	}

	// 如果是异步请求且返回202状态码，则返回nil表示请求已接受
	if async && resp.StatusCode == 202 {
		return nil, nil
	}

	var article model.SedoPublishedArticle
	if err := s.httpClient.ParseResponse(resp, &article); err != nil {
		return nil, fmt.Errorf("解析发布文章响应失败: %v", err)
	}

	return &article, nil
}

// UnpublishArticle 取消发布文章
func (s *SedoArticleService) UnpublishArticle(ctx context.Context, id string) error {
	resp, err := s.httpClient.Delete(ctx, fmt.Sprintf("/published-articles/%s", id), nil, nil)
	if err != nil {
		return fmt.Errorf("取消发布文章请求失败: %v", err)
	}

	if err := s.httpClient.ParseResponse(resp, nil); err != nil {
		return fmt.Errorf("解析取消发布文章响应失败: %v", err)
	}

	return nil
}

// GetDetailedArticles 获取详细文章列表（包含发布信息）
func (s *SedoArticleService) GetDetailedArticles(ctx context.Context, page, size int, sort, term string) ([]model.SedoDetailedArticleResponse, *model.SedoPageResponse, error) {
	query := s.httpClient.BuildQueryParams(page, size, sort, term)

	resp, err := s.httpClient.Get(ctx, "/detailed-articles", query, nil)
	if err != nil {
		return nil, nil, fmt.Errorf("获取详细文章列表请求失败: %v", err)
	}

	var articles []model.SedoDetailedArticleResponse
	if err := s.httpClient.ParseResponse(resp, &articles); err != nil {
		return nil, nil, fmt.Errorf("解析详细文章列表响应失败: %v", err)
	}

	totalCount, totalPages, err := s.httpClient.GetPageHeaders(resp)
	if err != nil {
		return nil, nil, fmt.Errorf("获取分页信息失败: %v", err)
	}

	pageResponse := &model.SedoPageResponse{
		TotalCount: totalCount,
		TotalPages: totalPages,
	}

	return articles, pageResponse, nil
}

// GetDetailedArticleByID 根据ID获取详细文章（包含发布信息）
func (s *SedoArticleService) GetDetailedArticleByID(ctx context.Context, id string) (*model.SedoDetailedArticleResponse, error) {
	resp, err := s.httpClient.Get(ctx, fmt.Sprintf("/detailed-articles/%s", id), nil, nil)
	if err != nil {
		return nil, fmt.Errorf("获取详细文章详情请求失败: %v", err)
	}

	var article model.SedoDetailedArticleResponse
	if err := s.httpClient.ParseResponse(resp, &article); err != nil {
		return nil, fmt.Errorf("解析详细文章详情响应失败: %v", err)
	}

	return &article, nil
}

// SetBearerToken 设置Bearer令牌
func (s *SedoArticleService) SetBearerToken(token string) {
	s.httpClient.SetBearerToken(token)
}

// UseOAuth 启用OAuth身份验证
func (s *SedoArticleService) UseOAuth(enable bool) {
	s.httpClient.UseOAuth(enable)
}

// SetOAuthCredentials 设置OAuth凭据
func (s *SedoArticleService) SetOAuthCredentials(clientID, clientSecret string) {
	s.httpClient.SetOAuthCredentials(clientID, clientSecret)
}

// SetOAuthConfig 设置OAuth配置
func (s *SedoArticleService) SetOAuthConfig(tokenEndpoint, clientID, clientSecret, audience, grantType string) {
	oauthClient := GetSedoOAuthClient()
	oauthClient.SetConfig(SedoOAuthConfig{
		TokenEndpoint: tokenEndpoint,
		ClientID:      clientID,
		ClientSecret:  clientSecret,
		Audience:      audience,
		GrantType:     grantType,
		Logger:        oauthClient.logger,
	})
	s.httpClient.UseOAuth(true)
}

// -----------------------------
// 辅助方法
// -----------------------------

// InitializeWithToken 使用令牌初始化服务
func (s *SedoArticleService) InitializeWithToken(token string) {
	s.SetBearerToken(token)
}

// InitializeWithOAuth 使用OAuth凭据初始化服务
func (s *SedoArticleService) InitializeWithOAuth(clientID, clientSecret string) {
	s.SetOAuthCredentials(clientID, clientSecret)
}

// InitializeWithFullOAuth 使用完整OAuth配置初始化服务
func (s *SedoArticleService) InitializeWithFullOAuth(tokenEndpoint, clientID, clientSecret, audience, grantType string) {
	s.SetOAuthConfig(tokenEndpoint, clientID, clientSecret, audience, grantType)
}
