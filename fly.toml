# fly.toml app configuration file generated for replay-go on 2024-03-24T16:05:07+08:00
#
# See https://fly.io/docs/reference/configuration/ for information about how to use this file.
#

app = 'replay-go'
primary_region = 'nrt'

[build]
  [build.args]
    GO_VERSION = '1.22.1'

[env]
  PORT = '8080'

[http_service]
  internal_port = 8080
  force_https = true
  auto_stop_machines = true
  auto_start_machines = true
  min_machines_running = 0
  processes = ['app']

[[vm]]
  memory = '1gb'
  cpu_kind = 'shared'
  cpus = 1
