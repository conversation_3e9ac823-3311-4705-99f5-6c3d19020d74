package scheduler

import (
	"context"
	"log"
	"rsoc-system-go/model/system"
	"sync"

	"github.com/robfig/cron/v3"
)

// JobFunc 定义任务执行函数类型
type JobFunc func(ctx context.Context) error

// Scheduler 定时任务调度器
type Scheduler struct {
	cron     *cron.Cron
	jobs     map[int64]cron.EntryID
	jobFuncs map[string]JobFunc
	mu       sync.RWMutex
	wg       *sync.WaitGroup
	ctx      context.Context
	cancel   context.CancelFunc
}

var (
	scheduler *Scheduler
	once      sync.Once
)

// GetScheduler 获取调度器单例
func GetScheduler() *Scheduler {
	once.Do(func() {
		ctx, cancel := context.WithCancel(context.Background())
		scheduler = &Scheduler{
			cron:     cron.New(cron.WithSeconds()),
			jobs:     make(map[int64]cron.EntryID),
			jobFuncs: make(map[string]JobFunc),
			wg:       &sync.WaitGroup{},
			ctx:      ctx,
			cancel:   cancel,
		}
		scheduler.cron.Start()
	})
	return scheduler
}

// RegisterJob 注册任务执行函数
func (s *Scheduler) RegisterJob(jobClass string, fn JobFunc) {
	s.mu.Lock()
	defer s.mu.Unlock()
	s.jobFuncs[jobClass] = fn
}

// AddJob 添加定时任务
func (s *Scheduler) AddJob(task *system.CronEntity) error {
	s.mu.Lock()
	defer s.mu.Unlock()

	// 如果任务已存在，先移除
	if entryID, exists := s.jobs[task.Id]; exists {
		s.cron.Remove(entryID)
		delete(s.jobs, task.Id)
	}

	// 获取任务执行函数
	jobFunc, exists := s.jobFuncs[task.JobClass]
	if !exists {
		log.Printf("Job class not found: %s", task.JobClass)
		return nil
	}

	// 添加新任务
	entryID, err := s.cron.AddFunc(task.CronExpr, func() {
		// 检查上下文是否已取消
		if s.ctx.Err() != nil {
			return
		}

		s.wg.Add(1)
		defer s.wg.Done()

		if err := jobFunc(s.ctx); err != nil {
			log.Printf("Job execution failed: %v", err)
		}
	})

	if err != nil {
		return err
	}

	s.jobs[task.Id] = entryID
	return nil
}

// RemoveJob 移除定时任务
func (s *Scheduler) RemoveJob(taskID int64) {
	s.mu.Lock()
	defer s.mu.Unlock()

	if entryID, exists := s.jobs[taskID]; exists {
		s.cron.Remove(entryID)
		delete(s.jobs, taskID)
	}
}

// UpdateJobStatus 更新任务状态
func (s *Scheduler) UpdateJobStatus(task *system.CronEntity) {
	if task.Status == 1 { // 启用
		s.AddJob(task)
	} else { // 禁用
		s.RemoveJob(task.Id)
	}
}

// Stop 停止调度器
func (s *Scheduler) Stop() {
	// 发送取消信号
	s.cancel()

	// 停止cron调度器
	s.cron.Stop()

	// 等待所有正在执行的任务完成
	s.wg.Wait()
}
