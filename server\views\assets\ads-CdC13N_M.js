import{x as m,d as l,y as U,z as Ne,A as De,_ as Ae,u as Be,o as je,r as O,a as ze,M as p,j as J,c as H,b as g,w as r,B as Le,e as i,k as Q,f as w,h as b,n as T,m as W,F as Fe,g as Ke,q as Re,t as ne,v as Je,I as Ue,p as Ee,s as C}from"./index-DlVegDiC.js";import{P as qe}from"./PlusOutlined-Cg2o2XQN.js";import{B as E,b as X,T as _,P as Y}from"./Paragraph-DEgIVM3w.js";import{S as Me}from"./index-CSU5nP3m.js";import{_ as Ve}from"./index-1uCBjWky.js";import{_ as Ge}from"./index-BrFZluVG.js";var Ze=function(a,d){var o={};for(var s in a)Object.prototype.hasOwnProperty.call(a,s)&&d.indexOf(s)<0&&(o[s]=a[s]);if(a!=null&&typeof Object.getOwnPropertySymbols=="function")for(var n=0,s=Object.getOwnPropertySymbols(a);n<s.length;n++)d.indexOf(s[n])<0&&Object.prototype.propertyIsEnumerable.call(a,s[n])&&(o[s[n]]=a[s[n]]);return o};const He=()=>U(m(m({},X()),{ellipsis:{type:Boolean,default:void 0}}),["component"]),N=(a,d)=>{let{slots:o,attrs:s}=d;const n=m(m({},a),s),{ellipsis:y,rel:f}=n,h=Ze(n,["ellipsis","rel"]),v=m(m({},h),{rel:f===void 0&&h.target==="_blank"?"noopener noreferrer":f,ellipsis:!!y,component:"a"});return delete v.navigate,l(E,v,o)};N.displayName="ATypographyLink";N.inheritAttrs=!1;N.props=He();const Qe=()=>m(m({},U(X(),["component"])),{ellipsis:{type:[Boolean,Object],default:void 0}}),D=(a,d)=>{let{slots:o,attrs:s}=d;const{ellipsis:n}=a,y=m(m(m({},a),{ellipsis:n&&typeof n=="object"?U(n,["expandable","rows"]):n,component:"span"}),s);return l(E,y,o)};D.displayName="ATypographyText";D.inheritAttrs=!1;D.props=Qe();var We=function(a,d){var o={};for(var s in a)Object.prototype.hasOwnProperty.call(a,s)&&d.indexOf(s)<0&&(o[s]=a[s]);if(a!=null&&typeof Object.getOwnPropertySymbols=="function")for(var n=0,s=Object.getOwnPropertySymbols(a);n<s.length;n++)d.indexOf(s[n])<0&&Object.prototype.propertyIsEnumerable.call(a,s[n])&&(o[s[n]]=a[s[n]]);return o};const Xe=Ne(1,2,3,4,5),Ye=()=>m(m({},U(X(),["component","strong"])),{level:Number}),A=(a,d)=>{let{slots:o,attrs:s}=d;const{level:n=1}=a,y=We(a,["level"]);let f;Xe.includes(n)?f=`h${n}`:f="h1";const h=m(m(m({},y),{component:f}),s);return l(E,h,o)};A.displayName="ATypographyTitle";A.inheritAttrs=!1;A.props=Ye();_.Text=D;_.Title=A;_.Paragraph=Y;_.Link=N;_.Base=E;_.install=function(a){return a.component(_.name,_),a.component(_.Text.displayName,D),a.component(_.Title.displayName,A),a.component(_.Paragraph.displayName,Y),a.component(_.Link.displayName,N),a};var et={icon:{tag:"svg",attrs:{viewBox:"64 64 896 896",focusable:"false"},children:[{tag:"path",attrs:{d:"M168 504.2c1-43.7 10-86.1 26.9-126 17.3-41 42.1-77.7 73.7-109.4S337 212.3 378 195c42.4-17.9 87.4-27 133.9-27s91.5 9.1 133.8 27A341.5 341.5 0 01755 268.8c9.9 9.9 19.2 20.4 27.8 31.4l-60.2 47a8 8 0 003 14.1l175.7 43c5 1.2 9.9-2.6 9.9-7.7l.8-180.9c0-6.7-7.7-10.5-12.9-6.3l-56.4 44.1C765.8 155.1 646.2 92 511.8 92 282.7 92 96.3 275.6 92 503.8a8 8 0 008 8.2h60c4.4 0 7.9-3.5 8-7.8zm756 7.8h-60c-4.4 0-7.9 3.5-8 7.8-1 43.7-10 86.1-26.9 126-17.3 41-42.1 77.8-73.7 109.4A342.45 342.45 0 01512.1 856a342.24 342.24 0 01-243.2-100.8c-9.9-9.9-19.2-20.4-27.8-31.4l60.2-47a8 8 0 00-3-14.1l-175.7-43c-5-1.2-9.9 2.6-9.9 7.7l-.7 181c0 6.7 7.7 10.5 12.9 6.3l56.4-44.1C258.2 868.9 377.8 932 512.2 932c229.2 0 415.5-183.7 419.8-411.8a8 8 0 00-8-8.2z"}}]},name:"sync",theme:"outlined"};function oe(a){for(var d=1;d<arguments.length;d++){var o=arguments[d]!=null?Object(arguments[d]):{},s=Object.keys(o);typeof Object.getOwnPropertySymbols=="function"&&(s=s.concat(Object.getOwnPropertySymbols(o).filter(function(n){return Object.getOwnPropertyDescriptor(o,n).enumerable}))),s.forEach(function(n){tt(a,n,o[n])})}return a}function tt(a,d,o){return d in a?Object.defineProperty(a,d,{value:o,enumerable:!0,configurable:!0,writable:!0}):a[d]=o,a}var ee=function(d,o){var s=oe({},d,o.attrs);return l(De,oe({},s,{icon:et}),null)};ee.displayName="SyncOutlined";ee.inheritAttrs=!1;const nt={class:"main"},ot={class:"filter"},at={class:"pb-10"},lt={class:"inner_table"},st={class:"app_text"},it={class:"app_text"},dt={class:"pb-10"},rt={class:"pb-10"},ct={__name:"ads",setup(a){const d="",o=Be(),s=ze();je(()=>{B()});const n=O({id:0,data:[],loading:!1,expandedRowKeys:[""],innerData:[],innerLoading:!1,adsetData:[],adsetLoading:!1,fbadsData:[],fbadsLoading:!1}),y=O({total:0,current:1,pageSize:10,position:["bottomCenter"],showSizeChanger:!0,pageSizeOptions:["10","20","30"],showTotal:e=>`共 ${e} 项`}),f=O({total:0,current:1,pageSize:10,position:["bottomCenter"],showSizeChanger:!1,showTotal:e=>`共 ${e} 项`}),h=O({total:0,current:1,pageSize:10,position:["bottomCenter"],showSizeChanger:!1,showTotal:e=>`共 ${e} 项`}),v=O({total:0,current:1,pageSize:10,position:["bottomCenter"],showSizeChanger:!1,showTotal:e=>`共 ${e} 项`}),B=()=>{n.loading=!0,fetch(`${d}/FaceBook/adsList`,{method:"POST",body:JSON.stringify({page:y.current,limit:y.pageSize,directoryStatus:1}),headers:{token:o.token}}).then(e=>e.json()).then(e=>{console.log(e),e.code==1?(n.data=e.data.data,y.total=Number(e.data.total)):e.code==3e3?(o.$patch({token:!1}),s.push("/login"),p.error({title:e.msg})):(n.data=[],p.error({title:e.msg})),n.loading=!1}).catch(e=>{p.error({title:"服务器错误",content:`${e}`})})},ae=e=>{y.current=e.current,y.pageSize=e.pageSize,B()},le=(e,t)=>{console.log(e),console.log(t),e?(n.expandedRowKeys[0]=t.accountId,n.id=t.id,n.innerData=[],f.current=1,j()):n.expandedRowKeys=[]},j=()=>{n.innerLoading=!0,fetch(`${d}/FaceBook/campainList`,{method:"POST",body:JSON.stringify({accountId:n.expandedRowKeys[0],page:f.current,limit:f.pageSize,directoryStatus:1}),headers:{token:o.token}}).then(e=>e.json()).then(e=>{console.log(e),e.code==1?(n.innerData=e.data.data,f.total=Number(e.data.total)):e.code==3e3?(o.$patch({token:!1}),s.push("/login"),p.error({title:e.msg})):(n.innerData=[],p.error({title:e.msg})),n.innerLoading=!1}).catch(e=>{p.error({title:"服务器错误",content:`${e}`})})},se=e=>{f.current=e.current,f.pageSize=e.pageSize,j()},z=J(!1),ie=()=>{z.value=!0,fetch(`${d}/FaceBookInfo/updateCampain`,{method:"POST",body:JSON.stringify({adsId:n.id}),headers:{token:o.token}}).then(e=>e.json()).then(e=>{console.log(e),e.code==1?(C.success(e.msg),n.expandedRowKeys[0]&&j()):p.error({title:e.msg}),z.value=!1}).catch(e=>{z.value=!1,p.error({title:"服务器错误",content:`${e}`})})},de=e=>{fetch(`${d}/FaceBook/campainDel`,{method:"POST",body:JSON.stringify({id:e.id}),headers:{token:o.token}}).then(t=>t.json()).then(t=>{console.log(t),t.code==1?(C.success(t.msg),n.expandedRowKeys[0]&&j()):p.error({title:t.msg})}).catch(t=>{p.error({title:"服务器错误",content:`${t}`})})},I=O({values:{},open:!1}),re=e=>{I.open=!0,I.values=e,h.current=1,L()},L=()=>{fetch(`${d}/FaceBook/adSetList`,{method:"POST",body:JSON.stringify({campaignId:I.values.campaignId,page:h.current,limit:h.pageSize,directoryStatus:1}),headers:{token:o.token}}).then(e=>e.json()).then(e=>{console.log(e),e.code==1?(n.adsetData=e.data.data,h.total=Number(e.data.total)):e.code==3e3?(o.$patch({token:!1}),s.push("/login"),p.error({title:e.msg})):(n.adsetData=[],p.error({title:e.msg}))}).catch(e=>{p.error({title:"服务器错误",content:`${e}`})})},ce=e=>{h.current=e.current,h.pageSize=e.pageSize,L()},q=J(!1),pe=()=>{q.value=!0,fetch(`${d}/FaceBookInfo/updateAdset`,{method:"POST",body:JSON.stringify({campaginDbId:I.values.id,adsId:n.id}),headers:{token:o.token}}).then(e=>e.json()).then(e=>{console.log(e),e.code==1?(C.success(e.msg),L()):p.error({title:e.msg}),q.value=!1}).catch(e=>{p.error({title:"服务器错误",content:`${e}`})})},ue=e=>{fetch(`${d}/FaceBook/adSetDel`,{method:"POST",body:JSON.stringify({id:e.id}),headers:{token:o.token}}).then(t=>t.json()).then(t=>{console.log(t),t.code==1?(C.success(t.msg),L()):p.error({title:t.msg})}).catch(t=>{p.error({title:"服务器错误",content:`${t}`})})},x=O({values:{},open:!1}),me=e=>{x.open=!0,x.values=e,v.current=1,F()},F=()=>{fetch(`${d}/FaceBook/fbAdsList`,{method:"POST",body:JSON.stringify({adset_id:x.values.adset_id,page:v.current,limit:v.pageSize,directoryStatus:1}),headers:{token:o.token}}).then(e=>e.json()).then(e=>{console.log(e),e.code==1?(n.fbadsData=e.data.data,v.total=Number(e.data.total)):e.code==3e3?(o.$patch({token:!1}),s.push("/login"),p.error({title:e.msg})):(n.fbadsData=[],p.error({title:e.msg}))}).catch(e=>{p.error({title:"服务器错误",content:`${e}`})})},ge=e=>{v.current=e.current,v.pageSize=e.pageSize,F()},M=J(!1),fe=()=>{M.value=!0,fetch(`${d}/FaceBookInfo/updateAdsInfo`,{method:"POST",body:JSON.stringify({adSetDbId:x.values.id,adsId:n.id}),headers:{token:o.token}}).then(e=>e.json()).then(e=>{console.log(e),e.code==1?(C.success(e.msg),F()):p.error({title:e.msg}),M.value=!1}).catch(e=>{p.error({title:"服务器错误",content:`${e}`})})},ye=e=>{fetch(`${d}/FaceBook/fbAdsDel`,{method:"POST",body:JSON.stringify({id:e.id}),headers:{token:o.token}}).then(t=>t.json()).then(t=>{console.log(t),t.code==1?(C.success(t.msg),F()):p.error({title:t.msg})}).catch(t=>{p.error({title:"服务器错误",content:`${t}`})})},te=J(),c=O({open:!1,app:void 0,app_option:{},app_list:[],id:"",name:"",token:"",note:"",loading:!1}),he=()=>{c.open=!0,_e()},_e=()=>{fetch(`${d}/FaceBook/appList`,{method:"POST",headers:{token:o.token}}).then(e=>e.json()).then(e=>{console.log(e),c.app_list=e.data}).catch(e=>{p.error({title:"服务器错误",content:`${e}`})})},ke=(e,t)=>{console.log(t),c.app_option=t},be=()=>{c.loading=!0,fetch(`${d}/FaceBook/addAdsAccount`,{method:"POST",body:JSON.stringify({appId:c.app_option.appId,appKey:c.app_option.appKey,accountId:c.id,accountName:c.name,access_token:c.token,note:c.note}),headers:{token:o.token}}).then(e=>e.json()).then(e=>{console.log(e),e.code==1?(C.success("添加成功"),B(),te.value.resetFields()):p.error({title:e.msg}),c.open=!1,c.loading=!1}).catch(e=>{p.error({title:"服务器错误",content:`${e}`})})},ve=e=>{fetch(`${d}/FaceBook/delAds`,{method:"POST",body:JSON.stringify({id:e.id}),headers:{token:o.token}}).then(t=>t.json()).then(t=>{console.log(t),t.code==1?(C.success(t.msg),B()):p.error({title:t.msg})}).catch(t=>{p.error({title:"服务器错误",content:`${t}`})})},Se=[{title:"ID",dataIndex:"id",key:"id",align:"center"},{title:"账户名称",dataIndex:"accountName",key:"accountName",align:"center"},{title:"账户时区",dataIndex:"timeZone",key:"timeZone",align:"center"},{title:"Token",dataIndex:"access_token",key:"access_token",align:"center",width:300},{title:"备注",dataIndex:"note",key:"note",align:"center"},{title:"操作",key:"action",align:"center"}],Oe=[{title:"ID",dataIndex:"id",key:"id",align:"center"},{title:"广告组名称",dataIndex:"campaignName",key:"campaignName",align:"center"},{title:"创建时间",dataIndex:"created_time",key:"created_time",align:"center"},{title:"操作",key:"action",align:"center"}],Ce=[{title:"ID",dataIndex:"id",key:"id",align:"center"},{title:"广告组名称",dataIndex:"adset_name",key:"adset_name",align:"center"},{title:"创建时间",dataIndex:"created_time",key:"created_time",align:"center"},{title:"操作",key:"action",align:"center"}],Ie=[{title:"ID",dataIndex:"id",key:"id",align:"center"},{title:"广告名称",dataIndex:"ad_name",key:"ad_name",align:"center"},{title:"创建时间",dataIndex:"created_time",key:"created_time",align:"center"},{title:"操作",key:"action",align:"center"}];return(e,t)=>{const xe=qe,k=Le,Pe=Y,K=Ge,V=ee,R=Ve,we=Me,P=Ke,G=Ue,$e=Ee,Te=Fe,Z=p;return w(),H(Q,null,[g("div",nt,[g("div",null,[g("div",ot,[t[9]||(t[9]=g("div",{class:"filter_item"},null,-1)),g("div",null,[l(k,{type:"primary",onClick:he},{icon:r(()=>[l(xe)]),default:r(()=>[t[8]||(t[8]=b(" 添加广告账户 "))]),_:1})])])]),l(R,{columns:Se,"data-source":i(n).data,rowKey:"accountId",pagination:i(y),loading:i(n).loading,expandedRowKeys:i(n).expandedRowKeys,onChange:ae,onExpand:le,expandRowByClick:""},{bodyCell:r(({column:u,record:S})=>[u.key==="access_token"?(w(),W(Pe,{key:0,content:S.access_token,copyable:"",ellipsis:"",style:{width:"300px",margin:"0"}},null,8,["content"])):T("",!0),u.key==="action"?(w(),W(K,{key:1,title:"确认删除？",onConfirm:$=>ve(S)},{default:r(()=>[l(k,{type:"link",danger:""},{default:r(()=>t[10]||(t[10]=[b("删除")])),_:1})]),_:2},1032,["onConfirm"])):T("",!0)]),expandedRowRender:r(()=>[g("div",at,[l(k,{type:"primary",onClick:ie,loading:i(z)},{icon:r(()=>[l(V)]),default:r(()=>[t[11]||(t[11]=b(" 更新Campaigns "))]),_:1},8,["loading"])]),g("div",lt,[l(R,{columns:Oe,"data-source":i(n).innerData,pagination:i(f),loading:i(n).innerLoading,onChange:se,rowKey:"id",size:"small"},{bodyCell:r(({column:u,record:S})=>[u.key==="action"?(w(),H(Q,{key:0},[l(k,{type:"link",onClick:$=>re(S)},{default:r(()=>t[12]||(t[12]=[b(" 获取adset列表 ")])),_:2},1032,["onClick"]),l(K,{title:"确认删除？",onConfirm:$=>de(S)},{default:r(()=>[l(k,{type:"link",danger:""},{default:r(()=>t[13]||(t[13]=[b("删除")])),_:1})]),_:2},1032,["onConfirm"])],64)):T("",!0)]),_:1},8,["data-source","pagination","loading"])])]),_:1},8,["data-source","pagination","loading","expandedRowKeys"])]),l(Z,{open:i(c).open,"onUpdate:open":t[5]||(t[5]=u=>i(c).open=u),title:"添加广告账户",footer:null,maskClosable:!1},{default:r(()=>[t[15]||(t[15]=g("div",{style:{height:"20px"}},null,-1)),l(Te,{ref_key:"add_form",ref:te,model:i(c),onFinish:be,"label-col":{span:4},"wrapper-col":{span:18}},{default:r(()=>[l(P,{label:"总账号",name:"app",rules:[{required:!0,message:"请选择总账号"}]},{default:r(()=>[l(we,{value:i(c).app,"onUpdate:value":t[0]||(t[0]=u=>i(c).app=u),placeholder:"请选择",onChange:ke,options:i(c).app_list,"field-names":{label:"note",value:"appKey"}},null,8,["value","options"]),Re(g("div",null,[g("p",st,"APP ID："+ne(i(c).app_option.appId),1),g("p",it,"APP Key："+ne(i(c).app_option.appKey),1)],512),[[Je,i(c).app]])]),_:1}),l(P,{label:"账户ID",name:"id",rules:[{required:!0,message:"请输入Account ID"}]},{default:r(()=>[l(G,{value:i(c).id,"onUpdate:value":t[1]||(t[1]=u=>i(c).id=u),placeholder:"Account ID"},null,8,["value"])]),_:1}),l(P,{label:"账户名称",name:"name",rules:[{required:!0,message:"请输入Account Name"}]},{default:r(()=>[l(G,{value:i(c).name,"onUpdate:value":t[2]||(t[2]=u=>i(c).name=u),placeholder:"Account Name"},null,8,["value"])]),_:1}),l(P,{label:"Token",name:"token",rules:[{required:!0,message:"请输入Token"}]},{default:r(()=>[l(G,{value:i(c).token,"onUpdate:value":t[3]||(t[3]=u=>i(c).token=u),placeholder:"Token"},null,8,["value"])]),_:1}),l(P,{label:"备注",name:"note"},{default:r(()=>[l($e,{value:i(c).note,"onUpdate:value":t[4]||(t[4]=u=>i(c).note=u),placeholder:"备注",rows:2},null,8,["value"])]),_:1}),l(P,{"wrapper-col":{offset:4,span:18}},{default:r(()=>[l(k,{type:"primary","html-type":"submit",loading:i(c).loading},{default:r(()=>t[14]||(t[14]=[b(" 提交 ")])),_:1},8,["loading"])]),_:1})]),_:1},8,["model"])]),_:1},8,["open"]),l(Z,{open:i(I).open,"onUpdate:open":t[6]||(t[6]=u=>i(I).open=u),title:"adset列表",footer:null,maskClosable:!1,width:900},{default:r(()=>[g("div",dt,[l(k,{type:"primary",onClick:pe,loading:i(q)},{icon:r(()=>[l(V)]),default:r(()=>[t[16]||(t[16]=b(" 更新adset "))]),_:1},8,["loading"])]),g("div",null,[l(R,{columns:Ce,"data-source":i(n).adsetData,rowKey:"id",pagination:i(h),loading:i(n).adsetLoading,bordered:"",size:"small",onChange:ce},{bodyCell:r(({column:u,record:S})=>[u.key==="action"?(w(),H(Q,{key:0},[l(k,{type:"link",onClick:$=>me(S)},{default:r(()=>t[17]||(t[17]=[b(" facebook 广告列表 ")])),_:2},1032,["onClick"]),l(K,{title:"确认删除？",onConfirm:$=>ue(S)},{default:r(()=>[l(k,{type:"link",danger:""},{default:r(()=>t[18]||(t[18]=[b("删除")])),_:1})]),_:2},1032,["onConfirm"])],64)):T("",!0)]),_:1},8,["data-source","pagination","loading"])])]),_:1},8,["open"]),l(Z,{open:i(x).open,"onUpdate:open":t[7]||(t[7]=u=>i(x).open=u),title:"facebook 广告列表",footer:null,maskClosable:!1,width:900},{default:r(()=>[g("div",rt,[l(k,{type:"primary",onClick:fe,loading:i(M)},{icon:r(()=>[l(V)]),default:r(()=>[t[19]||(t[19]=b(" 更新Facebook 广告信息 "))]),_:1},8,["loading"])]),g("div",null,[l(R,{columns:Ie,"data-source":i(n).fbadsData,rowKey:"id",pagination:i(v),loading:i(n).fbadsLoading,bordered:"",size:"small",onChange:ge},{bodyCell:r(({column:u,record:S})=>[u.key==="action"?(w(),W(K,{key:0,title:"确认删除？",onConfirm:$=>ye(S)},{default:r(()=>[l(k,{type:"link",danger:""},{default:r(()=>t[20]||(t[20]=[b("删除")])),_:1})]),_:2},1032,["onConfirm"])):T("",!0)]),_:1},8,["data-source","pagination","loading"])])]),_:1},8,["open"])],64)}}},ht=Ae(ct,[["__scopeId","data-v-d834e54c"]]);export{ht as default};
