package jobs

import (
	"rsoc-system-go/middleware/pkg/scheduler"
)

type Job struct {
}

func (j *Job) RegisterJob() {
	sch := scheduler.GetScheduler()
	//sch.RegisterJob(GenDataSendJobClass, GenDataSendJobFunc)
	sch.RegisterJob(UserTokenJobClass, UserTokenFunc)
	//sch.RegisterJob(GoogleArticleSyncJobClass, GoogleArticleSyncJobFunc)
	sch.RegisterJob(GoogleReportSyncJobClass, GoogleReportSyncJobFunc)
}
