package handler

import (
	"context"
	"database/sql"
	"log"
	"net/http"
	"os"
	"rsoc-system-go/config"
	"rsoc-system-go/dao"
	"rsoc-system-go/middleware/pkg/redis"
	"rsoc-system-go/middleware/service"
	"rsoc-system-go/model"
	"rsoc-system-go/store"
	"rsoc-system-go/tools"
	"rsoc-system-go/tools/constants"

	"strconv"
	"strings"
	"time"

	"github.com/gin-gonic/gin"
)

type RePlay struct {
	service *service.RePlayService
}

// RePlayHandler 获取历史重放曲线数据
func (r *RePlay) RePlayHandler(c *gin.Context) {
	date := c.Query("date")
	if date == "" {
		c.JSON(http.StatusOK, config.Result{500, "date is empty", nil})
		return
	}
	line := r.service.RePlayLineRedis(date)
	c.JSON(http.StatusOK, config.Result{1, "OK", line})
}

// RePlayTaskLineHandler 获取正在执行的重放曲线数据
func (r *RePlay) RePlayTaskLineHandler(c *gin.Context) {
	//date := c.Query("date")
	task_id := c.Query("task_id")
	/*if date == "" {
		c.JSON(http.StatusOK, config.Result{500, "date is empty", nil})
		return
	}*/
	line := r.service.RePlayTaskLineRedis("", task_id)
	c.JSON(http.StatusOK, config.Result{1, "OK", line})
}

// Reload 重新加载token
func (r *RePlay) Reload(c *gin.Context) {
	userDao := dao.NewUserDao(store.DB)
	list, err := userDao.List()
	if err != nil {
		c.JSON(http.StatusOK, config.Result{500, "date is empty", nil})
		return
	}
	/*redisManager := store.GetRedisManager()
	err = redisManager.ConnectRedis("task")
	client := redisManager.GetRedisClient("task")*/
	indexdbStr := os.Getenv("redisdb")
	indexdb, _ := strconv.Atoi(indexdbStr)
	client := redis.SelectDB(indexdb)
	//client := redis.SelectDB(1)
	_, err = client.Del(context.Background(), "Token").Result()
	if err != nil {
		c.JSON(http.StatusOK, config.Result{500, "reload err", nil})
		return
	}

	for _, user := range list {
		_, err = client.HSet(context.Background(), "Token", user.Token, user.Token).Result()
		if err != nil {
			c.JSON(http.StatusOK, config.Result{500, "date is empty", nil})
			return
		}
	}
	c.JSON(http.StatusOK, config.Result{1, "OK", nil})
}
func (r *RePlay) RePlayTask(c *gin.Context) {

}
func (r *RePlay) SetReport(c *gin.Context) {
	token := c.Query("token")
	user_id := c.Query("user_id")
	click_id := c.Query("click_id")
	task_id := c.Query("task_id")
	campaign_id := c.Query("campaign_id")

	if token == "" {
		c.JSON(http.StatusOK, config.Result{500, "token is empty", nil})
		return
	}
	sysToken := os.Getenv("token")
	if token != sysToken {
		c.JSON(http.StatusOK, config.Result{500, "token error", nil})
		return
	}
	if click_id == "" {
		c.JSON(http.StatusOK, config.Result{500, "click_id is empty", nil})
		return
	}
	if user_id == "" {
		c.JSON(http.StatusOK, config.Result{500, "user_id is empty", nil})
		return
	}
	if campaign_id == "" {
		c.JSON(http.StatusOK, config.Result{500, "campaign_id is empty", nil})
		return
	}
	if task_id == "" {
		c.JSON(http.StatusOK, config.Result{500, "task_id is empty", nil})
		return
	}
	var param map[string]interface{}
	param = map[string]interface{}{
		"campaign_id": campaign_id,
		"date":        time.Now().Format(time.DateOnly), // time.Now()
	}
	insightsDAO := dao.NewFacebookInsightsDAO(store.DB)
	one, err := insightsDAO.FindOne(param)
	if err != nil && err.Error() != "record not found" {
		log.Println(err)
		c.JSON(http.StatusOK, config.Result{500, "find error", nil})
		return
	}
	/*redisManager := store.GetRedisManager()
	err = redisManager.ConnectRedis("task")
	client := redisManager.GetRedisClient("task")*/
	indexdbStr := os.Getenv("redisdb")
	indexdb, _ := strconv.Atoi(indexdbStr)
	client := redis.SelectDB(indexdb)
	//client := redis.SelectDB(1)
	countStr, err := client.HGet(context.Background(), campaign_id, time.Now().Format(time.DateOnly)).Result()
	if err != nil {
		if err.Error() == "redis: nil" {
			_, err := client.HSet(context.Background(), campaign_id, time.Now().Format(time.DateOnly), 1).Result()
			if err != nil {
				c.JSON(http.StatusOK, config.Result{500, "first set error", nil})
				return
			}
		}
	}
	if countStr != "" {
		count, _ := strconv.Atoi(countStr)
		_, err := client.HSet(context.Background(), campaign_id, time.Now().Format(time.DateOnly), count+1).Result()
		if err != nil {
			c.JSON(http.StatusOK, config.Result{500, "second set error", nil})
			return
		}
	} else {
		countStr = "1"
	}
	var insights model.FacebookInsights
	if one == nil {
		insights.Clicks = 1
		insights.Reach = 0
		insights.Impressions = 0
		insights.Spend = 0
		insights.CPM = 0
		insights.Frequency = 0
		insights.CampaignID = campaign_id
		insights.AdID = campaign_id
		insights.AdsetID = campaign_id
		insights.UserID, _ = strconv.Atoi(user_id)
		insights.UpdateTime = sql.NullTime{Time: time.Now(), Valid: true}
		insights.Date = time.Now().Format(time.DateOnly)
		err = insightsDAO.Create(&insights)
		if err != nil {
			c.JSON(http.StatusOK, config.Result{500, "insert error", nil})
			return
		}
	} else {
		one.Clicks, _ = strconv.Atoi(countStr)

		err = insightsDAO.Update(one)
		if err != nil {
			c.JSON(http.StatusOK, config.Result{500, "update error", nil})
			return
		}
	}
	c.JSON(http.StatusOK, config.Result{1, "OK", nil})
}

// GetProxy 获取代理
func (r *RePlay) GetProxy(c *gin.Context) {

	// 接收参数
	userID := c.Query("userId")
	country := c.Query("country")
	task_id := c.Query("task_id")
	click_id := c.Query("click_id")
	if userID == "" {
		c.JSON(http.StatusOK, config.Result{500, "userId is empty", nil})
		return
	}
	if country == "" {
		country = "US"
	}
	if task_id == "" {
		c.JSON(http.StatusOK, config.Result{500, "task_id is empty", nil})
		return
	}
	if click_id == "" {
		c.JSON(http.StatusOK, config.Result{500, "click_id is empty", nil})
		return
	}
	taskDao := dao.NewReplayTaskDao(store.DB)
	taskParam := map[string]interface{}{
		"task_id": task_id,
	}
	task, err := taskDao.FindOne(taskParam)
	if err != nil {
		c.JSON(http.StatusOK, config.Result{500, "get task error", nil})
		return
	}
	if task.ProxySetId == 0 {
		c.JSON(http.StatusOK, config.Result{500, "proxy_set_id is empty", nil})
		return
	}

	proxySetDao := dao.NewProxySetDAO(store.DB)

	param := map[string]interface{}{
		"id": task.ProxySetId,
	}
	proxySet, err := proxySetDao.FindOne(param)
	if err != nil {
		c.JSON(http.StatusOK, config.Result{500, "get proxy error", nil})
		return
	}

	// 处理代理用户名中的动态参数
	if proxySet.ProxyUser != "" {
		// 提取randstring参数
		if strings.Contains(proxySet.ProxyUser, "randstring:") {
			parts := strings.Split(proxySet.ProxyUser, "randstring:")
			if len(parts) > 1 {
				randPart := strings.Split(parts[1], "}")[0]

				// 构建搜索和替换的参数
				search := []string{
					"{COUNTRY}",
					"{country}",
					"{randstring:" + randPart + "}",
				}
				randInt, _ := strconv.Atoi(randPart)
				replace := []string{
					strings.ToUpper(country),
					strings.ToLower(country),
					tools.RandStr(randInt), // 需要实现一个生成随机字符串的方法
				}

				// 替换所有参数
				proxyUser := proxySet.ProxyUser
				for i := range search {
					proxyUser = strings.ReplaceAll(proxyUser, search[i], replace[i])
				}
				proxySet.ProxyUser = proxyUser
			}
		}
	}

	timez := constants.CountryTimeZones[country]
	if timez == "" {
		timez = "America/New_York"
	}

	result := map[string]interface{}{
		"proxy_url":  proxySet.ProxyURL,
		"proxy_port": proxySet.ProxyPort,
		"proxy_user": proxySet.ProxyUser,
		"proxy_pass": proxySet.ProxyPass,
		"proxy_type": proxySet.ProxyType,
		"timezone":   timez,
	}
	c.JSON(http.StatusOK, result)
}
