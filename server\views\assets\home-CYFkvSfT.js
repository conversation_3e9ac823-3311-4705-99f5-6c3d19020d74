import{_ as R,u as O,o as D,a as Y,M as c,r as v,c as M,b as f,d as o,w as n,e as d,F as P,f as B,g as C,h as r,B as U,i as $}from"./index-DlVegDiC.js";import{_ as j}from"./index-1uCBjWky.js";import{S as q,a as N}from"./index-CSU5nP3m.js";import{R as T}from"./dayjs-BF6WblrD.js";const A={class:"main"},F={__name:"home",setup(V){const _="",p=O(),k=Y();D(()=>{fetch(`${_}/user/info`,{method:"POST",headers:{token:p.token}}).then(l=>l.json()).then(l=>{l.code==1?console.log(l):l.code==3e3?(p.token=!1,k.push("/login"),c.error({title:l.msg})):c.error({title:l.msg})}).catch(l=>{c.error({title:"服务器错误",content:`${l}`})}),m(a.sedo)});const i=v({data:[],loading:!1}),a=v({open:!1,loading:!1,date:void 0,word:void 0,platform_id:void 0,sedo_list:[],sedo:"sedo"}),y=()=>{a.loading=!0,i.loading=!0;let l;a.sedo=="sedo"?l=`${_}/sedo/getReport`:l=`${_}/sedo2/getReport`,fetch(l,{method:"POST",body:JSON.stringify({day:$(a.date[0]).format("YYYY-MM-DD"),end_day:$(a.date[1]).format("YYYY-MM-DD"),selectWord:a.word,platform_id:a.platform_id}),headers:{token:p.token}}).then(t=>t.json()).then(t=>{console.log(t),t.code==1?(i.data=[t.all,...t.data],i.data=i.data.map(e=>(e.main||(e.main=`总计${t.data.length}个`),e.pre_click_rate=String(e.pre_click_rate).replace(/^(.*\..{3}).*$/,"$1")+"%",e.pre_cost=String(e.pre_cost).replace(/^(.*\..{3}).*$/,"$1"),e.pre_epc=String(e.pre_epc).replace(/^(.*\..{3}).*$/,"$1"),e.click_rate=String(e.click_rate).replace(/^(.*\..{3}).*$/,"$1")+"%",e.cov_cost=String(e.cov_cost).replace(/^(.*\..{3}).*$/,"$1"),e.epc=String(e.epc).replace(/^(.*\..{3}).*$/,"$1"),e.confirm_cov_rate=String(e.confirm_cov_rate).replace(/^(.*\..{3}).*$/,"$1")+"%",e.cpc=String(e.cpc).replace(/^(.*\..{3}).*$/,"$1"),e.total_ad_click_cpc=String(e.total_ad_click_cpc).replace(/^(.*\..{3}).*$/,"$1"),e.total_real_price=String(e.total_real_price).replace(/^(.*\..{3}).*$/,"$1"),e.total_spend=String(e.total_spend).replace(/^(.*\..{3}).*$/,"$1"),e.income=String(e.income).replace(/^(.*\..{3}).*$/,"$1"),e.roas=String(e.roas).replace(/^(.*\..{3}).*$/,"$1")+"%",e.ROI=String(e.ROI).replace(/^(.*\..{3}).*$/,"$1")+"%",e))):t.code==3e3?(p.$patch({token:!1}),k.push("/login"),c.error({title:t.msg})):(i.data=[],c.error({title:t.msg})),a.loading=!1,i.loading=!1}).catch(t=>{c.error({title:"服务器错误",content:`${t}`})})},S=l=>{l&&(a.platform_id=void 0,a.sedo_list=[],m(l))},m=l=>{let t;l=="sedo"?t=`${_}/SeDo/list`:t=`${_}/SeDo2/list`,fetch(t,{method:"POST",headers:{token:p.token}}).then(e=>e.json()).then(e=>{console.log(e),a.sedo_list=e.data}).catch(e=>{c.error({title:"服务器错误",content:`${e}`})})},x=[{title:"标题",dataIndex:"main",key:"main",align:"center"},{title:"流量",dataIndex:"total_fb_clicks",key:"total_fb_clicks",align:"center"},{title:"点击",dataIndex:"total_sedo_cov",key:"total_sedo_cov",align:"center"},{title:"点击率",dataIndex:"pre_click_rate",key:"pre_click_rate",align:"center"},{title:"点击成本",dataIndex:"pre_cost",key:"pre_cost",align:"center"},{title:"点击均价",dataIndex:"pre_epc",key:"pre_epc",align:"center"},{title:"转化",dataIndex:"total_sedo_clicks",key:"total_sedo_clicks",align:"center"},{title:"转化率",dataIndex:"click_rate",key:"click_rate",align:"center"},{title:"转化成本",dataIndex:"cov_cost",key:"cov_cost",align:"center"},{title:"转化均价",dataIndex:"epc",key:"epc",align:"center"},{title:"转化确认率",dataIndex:"confirm_cov_rate",key:"confirm_cov_rate",align:"center"},{title:"CPC",dataIndex:"cpc",key:"cpc",align:"center"},{title:"预估收益",dataIndex:"total_ad_click_cpc",key:"total_ad_click_cpc",align:"center"},{title:"转化收益",dataIndex:"total_real_price",key:"total_real_price",align:"center"},{title:"成本",dataIndex:"total_spend",key:"total_spend",align:"center"},{title:"利润",dataIndex:"income",key:"income",align:"center"},{title:"ROAS",dataIndex:"roas",key:"roas",align:"center"},{title:"ROI",dataIndex:"ROI",key:"ROI",align:"center"}];return(l,t)=>{const e=N,g=q,u=C,I=T,w=U,b=P,h=j;return B(),M("div",A,[f("div",null,[o(b,{model:d(a),onFinish:y,layout:"inline"},{default:n(()=>[o(u,{label:"SeDo平台",name:"sedo",rules:[{required:!0,message:"请选择SeDo平台"}]},{default:n(()=>[o(g,{value:d(a).sedo,"onUpdate:value":t[0]||(t[0]=s=>d(a).sedo=s),style:{width:"220px"},placeholder:"请选择SeDo平台",onChange:S},{default:n(()=>[o(e,{value:"sedo"},{default:n(()=>t[4]||(t[4]=[r("SeDo")])),_:1})]),_:1},8,["value"])]),_:1}),o(u,{label:"平台账户",name:"platform_id",rules:[{required:!0,message:"请选择平台账户"}]},{default:n(()=>[o(g,{value:d(a).platform_id,"onUpdate:value":t[1]||(t[1]=s=>d(a).platform_id=s),style:{width:"220px"},placeholder:"请选择平台账户",options:d(a).sedo_list,"field-names":{label:"note",value:"id"}},null,8,["value","options"])]),_:1}),o(u,{label:"分组字段",name:"word",rules:[{required:!0,message:"请选择分组字段"}]},{default:n(()=>[o(g,{value:d(a).word,"onUpdate:value":t[2]||(t[2]=s=>d(a).word=s),style:{width:"220px"},placeholder:"请选择分组字段"},{default:n(()=>[o(e,{value:"campaign_id"},{default:n(()=>t[5]||(t[5]=[r("campaign_id")])),_:1}),o(e,{value:"adset_id"},{default:n(()=>t[6]||(t[6]=[r("adset_id")])),_:1}),o(e,{value:"ad_id"},{default:n(()=>t[7]||(t[7]=[r("ad_id")])),_:1}),o(e,{value:"task_id"},{default:n(()=>t[8]||(t[8]=[r("task_id")])),_:1}),o(e,{value:"two_directory"},{default:n(()=>t[9]||(t[9]=[r(" two_directory ")])),_:1}),o(e,{value:"keyword"},{default:n(()=>t[10]||(t[10]=[r("keyword")])),_:1}),o(e,{value:"domain"},{default:n(()=>t[11]||(t[11]=[r("domain")])),_:1}),o(e,{value:"placement"},{default:n(()=>t[12]||(t[12]=[r("placement")])),_:1})]),_:1},8,["value"])]),_:1}),o(u,{label:"日期",name:"date",rules:[{required:!0,message:"请选择日期"}]},{default:n(()=>[o(I,{value:d(a).date,"onUpdate:value":t[3]||(t[3]=s=>d(a).date=s)},null,8,["value"])]),_:1}),o(u,{"wrapper-col":{offset:4,span:18}},{default:n(()=>[o(w,{type:"primary","html-type":"submit",loading:d(a).loading},{default:n(()=>t[13]||(t[13]=[r(" 提交 ")])),_:1},8,["loading"])]),_:1})]),_:1},8,["model"])]),t[14]||(t[14]=f("div",{style:{height:"20px"}},null,-1)),f("div",null,[o(h,{columns:x,"data-source":d(i).data,rowKey:"id",pagination:!1,loading:d(i).loading,bordered:"",size:"middle"},null,8,["data-source","loading"])])])}}},K=R(F,[["__scopeId","data-v-4f9c4ce9"]]);export{K as default};
