package model

import (
	"database/sql"
)

// AutoTask 自动任务表
type AutoTask struct {
	ID            int             `json:"id" gorm:"column:id;primaryKey;autoIncrement"`
	TaskID        sql.NullString  `json:"task_id" gorm:"column:task_id;not null"`
	ClickID       sql.NullString  `json:"click_id" gorm:"column:click_id;not null"`
	Status        sql.NullInt32   `json:"status" gorm:"column:status;default:1"` // 1-未执行 2-正在操作 3.完成 4.失败
	AddTime       sql.NullString  `json:"add_time" gorm:"column:add_time"`
	Country       sql.NullString  `json:"country" gorm:"column:country"`
	IPCountry     sql.NullString  `json:"ip_country" gorm:"column:ip_country"`
	Viewport      sql.NullString  `json:"viewport" gorm:"column:viewport;type:mediumtext"`
	UserAgent     sql.NullString  `json:"useragent" gorm:"column:useragent;type:mediumtext"`
	Lang          sql.NullString  `json:"lang" gorm:"column:lang"`
	UserIP        sql.NullString  `json:"user_ip" gorm:"column:user_ip"`
	Ref           sql.NullString  `json:"ref" gorm:"column:ref"`
	Keywords      sql.NullString  `json:"keywords" gorm:"column:keywords;type:mediumtext"`
	Conver        sql.NullInt32   `json:"conver" gorm:"column:conver;default:1"` // 1-未转化 2-转化
	StartTime     sql.NullTime    `json:"start_time" gorm:"column:start_time"`
	OverTime      sql.NullTime    `json:"over_time" gorm:"column:over_time"`
	Fingerprintjs sql.NullString  `json:"fingerprintjs" gorm:"column:fingerprintjs;type:longtext"`
	CampaignID    sql.NullString  `json:"campaign_id" gorm:"column:campaign_id"`
	AdsetID       sql.NullString  `json:"adset_id" gorm:"column:adset_id"`
	AdID          sql.NullString  `json:"ad_id" gorm:"column:ad_id"`
	View          int             `json:"view" gorm:"column:view;default:0"`
	Click1        int             `json:"click1" gorm:"column:click1;default:0"`
	Click2        int             `json:"click2" gorm:"column:click2;default:0"`
	Click3        int             `json:"click3" gorm:"column:click3;default:0"`
	Traffic       sql.NullString  `json:"traffic" gorm:"column:traffic"`                           // 流量平台
	TrafficData   sql.NullString  `json:"traffic_data" gorm:"column:traffic_data;type:mediumtext"` // 流量源参数
	ProxyName     sql.NullString  `json:"proxy_name" gorm:"column:proxy_name"`
	ProxyInfo     sql.NullString  `json:"proxy_info" gorm:"column:proxy_info;type:mediumtext"`
	TrafficURL    sql.NullString  `json:"traffic_url" gorm:"column:traffic_url;type:mediumtext"`
	RealPrice     sql.NullFloat64 `json:"real_price" gorm:"column:real_price;type:decimal(10,2)"`     // 真实收益
	OraclePrice   sql.NullFloat64 `json:"oracle_price" gorm:"column:oracle_price;type:decimal(10,2)"` // 预估收益
	ClickURL      sql.NullString  `json:"click_url" gorm:"column:click_url;type:mediumtext"`
	Date          sql.NullString  `json:"date" gorm:"column:date"`
}

// TableName 指定表名
func (AutoTask) TableName() string {
	return "auto_task"
}
