package service

import (
	"fmt"
	"log"
	"net/http"
	"net/url"
	"os"
	"sync"
	"time"
)

// TelegramConfig Telegram配置
type TelegramConfig struct {
	BotToken string
	ChatID   string
	Retries  int
	Timeout  time.Duration
}

// TelegramService Telegram服务
type TelegramService struct {
	config TelegramConfig
	client *http.Client
}

var (
	telegramService     *TelegramService
	telegramServiceOnce sync.Once
)

// GetTelegramService 获取Telegram服务实例
func GetTelegramService() *TelegramService {
	telegramServiceOnce.Do(func() {
		telegramService = NewTelegramService(TelegramConfig{
			BotToken: getEnvOrDefault("TELEGRAM_BOT_TOKEN", ""),
			ChatID:   getEnvOrDefault("TELEGRAM_CHAT_ID", ""),
			Retries:  3,
			Timeout:  10 * time.Second,
		})
	})
	return telegramService
}

// NewTelegramService 创建新的Telegram服务
func NewTelegramService(config TelegramConfig) *TelegramService {
	return &TelegramService{
		config: config,
		client: &http.Client{
			Timeout: config.Timeout,
		},
	}
}

// SendMessage 发送消息到Telegram
func (s *TelegramService) SendMessage(message string) error {
	if s.config.BotToken == "" || s.config.ChatID == "" {
		return fmt.Errorf("Telegram配置不完整")
	}

	apiURL := fmt.Sprintf("https://api.telegram.org/bot%s/sendMessage", s.config.BotToken)

	// 格式化消息，支持Markdown
	formattedMsg := fmt.Sprintf("*错误报告*\n```\n%s\n```", message)

	for i := 0; i < s.config.Retries; i++ {
		params := url.Values{
			"chat_id":    {s.config.ChatID},
			"text":       {formattedMsg},
			"parse_mode": {"Markdown"},
		}

		resp, err := s.client.PostForm(apiURL, params)
		if err != nil {
			log.Printf("Telegram消息发送失败(尝试 %d/%d): %v", i+1, s.config.Retries, err)
			if i < s.config.Retries-1 {
				time.Sleep(2 * time.Second)
				continue
			}
			return err
		}
		defer resp.Body.Close()

		if resp.StatusCode != http.StatusOK {
			log.Printf("Telegram API返回错误(尝试 %d/%d): 状态码 %d", i+1, s.config.Retries, resp.StatusCode)
			if i < s.config.Retries-1 {
				time.Sleep(2 * time.Second)
				continue
			}
			return fmt.Errorf("Telegram API返回错误状态码: %d", resp.StatusCode)
		}

		log.Printf("Telegram消息发送成功")
		return nil
	}

	return fmt.Errorf("发送Telegram消息失败，已达到最大重试次数")
}

// SendErrorReport 发送错误报告
func (s *TelegramService) SendErrorReport(err interface{}, stack string) error {
	message := fmt.Sprintf("程序发生错误:\n%v\n\n堆栈信息:\n%s", err, stack)
	return s.SendMessage(message)
}

// 从环境变量获取值，如果不存在则返回默认值
func getEnvOrDefault(key, defaultValue string) string {
	if value, exists := os.LookupEnv(key); exists {
		return value
	}
	return defaultValue
}
