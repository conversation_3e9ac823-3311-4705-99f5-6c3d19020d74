package handler

import (
	"bytes"
	"encoding/json"
	"fmt"
	"io"
	"log"
	"net/http"
	"net/url"
	"rsoc-system-go/config"
	"rsoc-system-go/dao"
	"rsoc-system-go/model"
	"rsoc-system-go/service"
	"rsoc-system-go/store"
	"strconv"
	"strings"
	"time"

	"github.com/gin-gonic/gin"
	"github.com/mitchellh/mapstructure"
)

const (
	baseURL = "https://adtechapi.com" // "https://adtechapi.com"
)

type GoogleHandler struct {
	articleDao *dao.GoogleArticleDAO
	reportDao  *dao.GoogleReportDAO
}

func NewGoogleHandler() *GoogleHandler {
	return &GoogleHandler{
		articleDao: dao.NewGoogleArticleDAO(store.DB),
		reportDao:  dao.NewGoogleReportDAO(store.DB),
	}
}

// UpdateLaunch 单条文章创建
func (h *GoogleHandler) UpdateLaunch(c *gin.Context) {
	h.articleDao = dao.NewGoogleArticleDAO(store.DB)
	apiURL := fmt.Sprintf("%s/v2/google/addLaunch", baseURL)
	// 改为从post json获取
	var paramsStr map[string]interface{}
	if err := c.ShouldBindJSON(&paramsStr); err != nil {
		c.JSON(http.StatusBadRequest, gin.H{
			"msg":  "invalid request body",
			"code": 400,
			"time": time.Now().Unix(),
		})
		return
	}
	params := url.Values{}
	for key, value := range paramsStr {
		if value == nil {
			continue
		}
		params.Add(key, fmt.Sprint(value))
	}
	// 发送POST请求
	resp, err := http.PostForm(apiURL, params)
	if err != nil {
		c.JSON(http.StatusInternalServerError, gin.H{
			"msg":  "failed to call external API",
			"code": 500,
			"time": time.Now().Unix(),
		})
		return
	}
	defer resp.Body.Close()

	// 读取响应
	body, err := io.ReadAll(resp.Body)
	if err != nil {
		c.JSON(http.StatusInternalServerError, gin.H{
			"msg":  "failed to read response",
			"code": 500,
			"time": time.Now().Unix(),
		})
		return
	}

	// 解析响应
	var result map[string]interface{}
	if err := json.Unmarshal(body, &result); err != nil {
		c.JSON(http.StatusInternalServerError, gin.H{
			"msg":  "failed to parse response",
			"code": 500,
			"time": time.Now().Unix(),
		})
		return
	}
	result["data"] = "{\"final_url\":\"https://pulse-topic.com/wissenswertes-uber-privatkredite-in-deutschland/?campaign=20470&terms=Sofort+Geld+Ohne+Bonita%CC%88tspru%CC%88fung%2CKlein+Kredit+Sofort+auf+Konto%2CSparkassen+Sofortkredit%2CKredit+Ohne+Schufa+Sofortauszahlung%2CBlitzkredit+Auszahlung+Sofort%2CPrivatkredit+in+der+Na%CC%88he+und+Schnell%2CKredit+Trotz+Negativ+Schufa%2CSofortkredit+Ohne+Schufa%2CKredite+Sofort&utm_medium=paid&utm_source=ig&utm_id=120220193676370628&utm_content=120220193710580628&utm_term=120220193679710628&utm_campaign=120220193676370628\"}"
	// 如果API调用成功，保存到数据库
	if /*result["code"].(float64) == 200 && */ result["data"] != nil {
		var data map[string]interface{}
		if dataStr, ok := result["data"].(string); ok {
			if err := json.Unmarshal([]byte(dataStr), &data); err != nil {
				c.JSON(http.StatusInternalServerError, gin.H{
					"msg":  "failed to parse data",
					"code": 500,
					"time": time.Now().Unix(),
				})
				return
			}
		}
		marshal, err := json.Marshal(paramsStr)
		articleParama := &model.GoogleArticle{}
		err = json.Unmarshal(marshal, articleParama)
		article, err := h.articleDao.GetByCampaignName(articleParama.CampaignName)
		article.Status = 0
		article.OfferCategory = articleParama.OfferCategory
		// 使用 mapstructure 库进行优雅的属性赋值
		if err := mapstructure.Decode(articleParama, article); err != nil {
			log.Printf("Failed to decode article params: %v", err)
		}
		if err != nil {
			log.Println(err)
		}
		// 从API响应中获取URL信息
		if urlWP, ok := data["url_wp_post"].(string); ok {
			article.URLWPPost = urlWP
		}
		if finalURL, ok := data["final_url"].(string); ok {
			article.FinalURL = finalURL
		}
		if channel, ok := data["available_channel"].(string); ok {
			article.Channel = channel
		}
		// 保存到数据库
		if err := h.articleDao.Updates(article); err != nil {
			c.JSON(http.StatusInternalServerError, gin.H{
				"msg":  "failed to save article to database",
				"code": 500,
				"time": time.Now().Unix(),
			})
			return
		}
		if err = h.articleDao.Update(&model.GoogleArticle{ID: article.ID, Status: 0}); err != nil {
			log.Println(err)
		}
	}

	c.JSON(http.StatusOK, config.Result{1, "", result})
}

// AddLaunch 单条文章创建
func (h *GoogleHandler) AddLaunch(c *gin.Context) {

	// 构建请求URL和参数
	//apiURL := fmt.Sprintf("%s/v2/google/addLaunch", baseURL)
	// 改为从post json获取
	var paramsStr map[string]interface{}
	if err := c.ShouldBindJSON(&paramsStr); err != nil {
		c.JSON(http.StatusBadRequest, gin.H{
			"msg":  "invalid request body",
			"code": 400,
			"time": time.Now().Unix(),
		})
		return
	}

	//TODO 重写Rsoc接口
	client := service.GetAdTechHTTPClient()
	client.SetSecurityKey(paramsStr["key"].(string))

	// 构建符合API要求的数据结构
	// 1. 构建article部分
	articleTopics := []string{}
	if keywords, ok := paramsStr["keywords"].(string); ok && keywords != "" {
		// 假设keywords是逗号分隔的字符串
		// 如果需要不同的分隔方式，可以在此处修改
		if keywords != "" {
			articleTopics = append(articleTopics, keywords)
		}
	}
	// 添加一些默认的主题，以匹配图中的格式
	if len(articleTopics) == 0 {
		articleTopics = []string{
			"Summer vacation",
			"All inclusive resort in Thailand",
			"Cheap flights from USA",
		}
	}

	article := map[string]interface{}{
		"topics":  articleTopics,
		"type":    "CreateArticle",
		"country": paramsStr["country"],  // 使用language作为country
		"locale":  paramsStr["language"], //"en-US",              // 默认locale
		"title":   paramsStr["article_title"],
		"excerpt": paramsStr["article_intro"],
		// "categoryId": "2e5c8fbb-f078-498b-82e5-d45263e21f67", // 默认类别ID或从请求中获取
	}

	// 如果提供了特定的locale，使用它
	if locale, ok := paramsStr["article_locale"].(string); ok && locale != "" {
		article["locale"] = locale
	}

	// 2. 构建campaign部分
	trackingSettings := map[string]interface{}{
		"type":           "S2sMetaTrackingSettings",
		"s2sMetaToken":   paramsStr["token"],
		"s2sMetaPixelId": paramsStr["pixel_id"],
		//"s2sMetaLandingPageEvent": "Lead",     // 默认事件
		//"s2sMetaClickEvent":       "Search",   // 默认事件
		"s2sMetaSearchEvent": "Purchase", // 默认事件
		/*
		 */
	}

	// 如果提供了特定的事件名称，更新它们
	if eventName, ok := paramsStr["event_name"].(string); ok && eventName != "" {
		trackingSettings["s2sMetaLandingPageEvent"] = eventName
	}

	trackingData := map[string]interface{}{
		"trafficSource":    paramsStr["traffic_source"],
		"trackingMethod":   "S2S", //paramsStr["tracking_method"],
		"trackingSettings": trackingSettings,
	}

	campaign := map[string]interface{}{
		"name":         paramsStr["campaign_name"],
		"type":         "CreateCampaign",
		"trackingData": trackingData,
	}

	// 3. 调用API
	resp, err := client.CreateCampaignWithFormData(
		c.Request.Context(),
		paramsStr["publishDomainName"].(string),
		article,
		campaign,
	)
	log.Println(resp)
	if err != nil {
		c.JSON(http.StatusInternalServerError, gin.H{
			"msg":  "failed to call external API",
			"code": 500,
			"time": time.Now().Unix(),
		})
		return
	}

	// 解析API响应
	var apiResponse model.AdTechAPIResponse
	if err := client.ParseResponse(resp, &apiResponse); err != nil {
		c.JSON(http.StatusInternalServerError, gin.H{
			"msg":  fmt.Sprintf("failed to parse API response: %v", err),
			"code": 500,
			"time": time.Now().Unix(),
		})
		return
	}

	// 将返回的数据保存到数据库
	if apiResponse.Code == 200 && apiResponse.Data != nil {
		// 将data字段解析为AdTechCampaignDetail
		var campaignDetail model.AdTechCampaignDetail
		dataBytes, err := json.Marshal(apiResponse.Data)
		if err != nil {
			c.JSON(http.StatusInternalServerError, gin.H{
				"msg":  fmt.Sprintf("failed to marshal data: %v", err),
				"code": 500,
				"time": time.Now().Unix(),
			})
			return
		}

		if err := json.Unmarshal(dataBytes, &campaignDetail); err != nil {
			c.JSON(http.StatusInternalServerError, gin.H{
				"msg":  fmt.Sprintf("failed to unmarshal campaign detail: %v", err),
				"code": 500,
				"time": time.Now().Unix(),
			})
			return
		}
		userId, exists := c.Get("userId")
		if !exists {
			c.JSON(http.StatusInternalServerError, gin.H{
				"msg":  "userId not found",
				"code": 500,
				"time": time.Now().Unix(),
			})
			return
		}
		// 创建GoogleArticle对象并保存到数据库
		googleArticle := &model.GoogleArticle{
			Campaign:       campaignDetail.Campaign.Id,
			CampaignId:     campaignDetail.ID,
			CampaignName:   campaignDetail.Campaign.Name,
			OfferCategory:  paramsStr["offer_category"].(string),
			Language:       campaignDetail.Article.Country,
			TrafficSource:  campaignDetail.Campaign.TrackingData.TrafficSource,
			TrackingMethod: campaignDetail.Campaign.TrackingData.TrackingMethod,
			PixelID:        "", // 这些字段在返回中可能没有，使用请求中的值
			Token:          "", // 这些字段在返回中可能没有，使用请求中的值
			EventName:      campaignDetail.Campaign.TrackingData.TrackingSettings.S2sMetaLandingPageEvent,
			Keywords:       strings.Join(campaignDetail.Article.Topics, ","),
			ArticleTitle:   campaignDetail.Article.Title,
			ArticleIntro:   campaignDetail.Article.Excerpt,
			Status:         0, // 0:待审核
			Key:            paramsStr["key"].(string),
			RsocId:         0,
			CreatedBy:      userId.(int),
			Type:           1,
		}

		// 保存Landing URL (如果有)
		if campaignDetail.TrackingUrl != "" {
			googleArticle.FinalURL = campaignDetail.TrackingUrl
		}

		// 用户请求中提到的数据格式可能有所不同，尝试从结构体中提取更多字段
		// 将完整的campaignDetail转换为JSON字符串以便于日志记录
		detailBytes, _ := json.Marshal(campaignDetail)
		log.Printf("Campaign detail: %s", string(detailBytes))

		// 如果请求中有这些字段，则使用请求中的值
		if pixelID, ok := paramsStr["pixel_id"].(string); ok {
			googleArticle.PixelID = pixelID
		}
		if token, ok := paramsStr["token"].(string); ok {
			googleArticle.Token = token
		}
		if twoDirectoryId, ok := paramsStr["two_directory_id"].(float64); ok {
			googleArticle.TwoDirectoryId = int(twoDirectoryId)
		} else if twoDirectoryIdInt, ok := paramsStr["two_directory_id"].(int); ok {
			googleArticle.TwoDirectoryId = twoDirectoryIdInt
		} else if twoDirectoryIdStr, ok := paramsStr["two_directory_id"].(string); ok {
			if id, err := strconv.Atoi(twoDirectoryIdStr); err == nil {
				googleArticle.TwoDirectoryId = id
			}
		}
		if twoDirectoryName, ok := paramsStr["two_directory_name"].(string); ok {
			googleArticle.TwoDirectoryName = twoDirectoryName
		}

		// 安全获取offer_category
		if googleArticle.OfferCategory == "" {
			if offerCategory, ok := paramsStr["offer_category"].(string); ok {
				googleArticle.OfferCategory = offerCategory
			}
		}

		// 安全获取country/language
		if googleArticle.Language == "" {
			if language, ok := paramsStr["language"].(string); ok {
				googleArticle.Language = language
			} else if country, ok := paramsStr["country"].(string); ok {
				googleArticle.Language = country
			}
		}

		// 验证必填字段
		if googleArticle.CampaignName == "" {
			log.Println("Missing required field: campaign_name")
			googleArticle.CampaignName = "未命名活动"
		}

		if googleArticle.OfferCategory == "" {
			log.Println("Missing required field: offer_category")
			googleArticle.OfferCategory = "未分类"
		}

		if googleArticle.Language == "" {
			log.Println("Missing required field: language/country")
			googleArticle.Language = "unknown"
		}

		if googleArticle.TrafficSource == "" {
			log.Println("Missing required field: traffic_source")
			googleArticle.TrafficSource = "unknown"
		}

		// 生成一个ID记录
		googleArticle.RsocId = int(time.Now().Unix()) % 1000000

		// 保存到数据库
		h.articleDao = dao.NewGoogleArticleDAO(store.DB)
		if err := h.articleDao.Create(googleArticle); err != nil {
			c.JSON(http.StatusInternalServerError, gin.H{
				"msg":  fmt.Sprintf("failed to save article to database: %v", err),
				"code": 500,
				"time": time.Now().Unix(),
			})
			return
		}
	}

	c.JSON(http.StatusOK, config.Result{1, "", apiResponse})
}

// BatchLaunch 批量文章创建
func (h *GoogleHandler) BatchLaunch(c *gin.Context) {
	type LaunchItem struct {
		CampaignName     string `json:"campaign_name"`
		OfferCategory    string `json:"offer_category"`
		Language         string `json:"language"`
		TrafficSource    string `json:"traffic_source"`
		TrackingMethod   string `json:"tracking_method"`
		PixelID          string `json:"pixel_id"`
		Token            string `json:"token"`
		EventName        string `json:"event_name"`
		Keywords         string `json:"keywords"`
		ArticleTitle     string `json:"article_title"`
		ArticleIntro     string `json:"article_intro"`
		Key              string `json:"key"`
		TwoDirectoryId   int    `json:"two_directory_id" `
		TwoDirectoryName string `json:"two_directory_name"`
	}

	var items []LaunchItem
	if err := c.BindJSON(&items); err != nil {
		c.JSON(http.StatusBadRequest, gin.H{
			"msg":  "invalid request body",
			"code": 400,
			"time": time.Now().Unix(),
		})
		return
	}

	// 构建请求URL
	apiURL := fmt.Sprintf("%s/v2/google/batchLaunch?security_key=%s", baseURL, c.Query("security_key"))

	// 发送请求
	jsonData, err := json.Marshal(items)
	if err != nil {
		c.JSON(http.StatusInternalServerError, gin.H{
			"msg":  "failed to marshal request body",
			"code": 500,
			"time": time.Now().Unix(),
		})
		return
	}

	resp, err := http.Post(apiURL, "application/json", bytes.NewBuffer(jsonData))
	if err != nil {
		c.JSON(http.StatusInternalServerError, gin.H{
			"msg":  "failed to call external API",
			"code": 500,
			"time": time.Now().Unix(),
		})
		return
	}
	defer resp.Body.Close()

	// 读取响应
	body, err := io.ReadAll(resp.Body)
	if err != nil {
		c.JSON(http.StatusInternalServerError, gin.H{
			"msg":  "failed to read response",
			"code": 500,
			"time": time.Now().Unix(),
		})
		return
	}

	// 解析响应
	var result map[string]interface{}
	if err := json.Unmarshal(body, &result); err != nil {
		c.JSON(http.StatusInternalServerError, gin.H{
			"msg":  "failed to parse response",
			"code": 500,
			"time": time.Now().Unix(),
		})
		return
	}

	// 如果API调用成功，保存到数据库
	if result["code"].(float64) == 200 && result["data"] != nil {
		data := result["data"].([]interface{})
		articles := make([]*model.GoogleArticle, 0, len(data))

		for i, item := range data {
			itemData := item.(map[string]interface{})
			article := &model.GoogleArticle{
				CampaignName:     items[i].CampaignName,
				OfferCategory:    items[i].OfferCategory,
				Language:         items[i].Language,
				TrafficSource:    items[i].TrafficSource,
				TrackingMethod:   items[i].TrackingMethod,
				PixelID:          items[i].PixelID,
				Token:            items[i].Token,
				EventName:        items[i].EventName,
				Keywords:         items[i].Keywords,
				ArticleTitle:     items[i].ArticleTitle,
				ArticleIntro:     items[i].ArticleIntro,
				Status:           1,
				Key:              items[i].Key,
				TwoDirectoryId:   items[i].TwoDirectoryId,
				TwoDirectoryName: items[i].TwoDirectoryName,
			}

			// 从API响应中获取URL信息
			if urlWP, ok := itemData["url_wp_post"].(string); ok {
				article.URLWPPost = urlWP
			}
			if finalURL, ok := itemData["final_url"].(string); ok {
				article.FinalURL = finalURL
			}
			if channel, ok := itemData["available_channel"].(string); ok {
				article.Channel = channel
			}

			articles = append(articles, article)
		}

		// 批量保存到数据库
		if err := h.articleDao.BatchCreate(articles); err != nil {
			c.JSON(http.StatusInternalServerError, gin.H{
				"msg":  "failed to save articles to database",
				"code": 500,
				"time": time.Now().Unix(),
			})
			return
		}
	}

	c.JSON(resp.StatusCode, result)
}

// ResultList 文章获取
func (h *GoogleHandler) ResultList(c *gin.Context) {
	// 首先从本地数据库获取
	req := &model.GoogleArticleListRequest{
		Page:     1,
		PageSize: 10,
	}
	err := c.ShouldBindJSON(&req)
	userId, exists := c.Get("userId")
	if !exists {
		c.JSON(http.StatusUnauthorized, gin.H{
			"msg":  "unauthorized",
			"code": 401,
			"time": time.Now().Unix(),
		})
		return
	}
	req.CreatedBy = userId.(int)
	articles, total, err := dao.NewGoogleArticleDAO(store.DB).List(req)
	if err != nil {
		// 如果本地数据库查询失败，尝试从远程API获取
		apiURL := fmt.Sprintf("%s/v2/google/resultList", baseURL)
		params := url.Values{}
		params.Add("security_key", c.Query("key"))
		if limit := c.Query("limit"); limit != "" {
			params.Add("limit", limit)
		}
		if page := c.Query("page"); page != "" {
			params.Add("page", page)
		}

		resp, err := http.Get(apiURL + "?" + params.Encode())
		if err != nil {
			c.JSON(http.StatusInternalServerError, gin.H{
				"msg":  "failed to get data from both local and remote",
				"code": 500,
				"time": time.Now().Unix(),
			})
			return
		}
		defer resp.Body.Close()

		body, err := io.ReadAll(resp.Body)
		if err != nil {
			c.JSON(http.StatusInternalServerError, gin.H{
				"msg":  "failed to read response",
				"code": 500,
				"time": time.Now().Unix(),
			})
			return
		}

		var result map[string]interface{}
		if err := json.Unmarshal(body, &result); err != nil {
			c.JSON(http.StatusInternalServerError, gin.H{
				"msg":  "failed to parse response",
				"code": 500,
				"time": time.Now().Unix(),
			})
			return
		}

		c.JSON(resp.StatusCode, result)
		return
	}

	// 返回本地数据库的结果
	c.JSON(http.StatusOK, gin.H{
		"msg":  "success",
		"code": 1,
		"time": time.Now().Unix(),
		"data": gin.H{
			"code": 0,
			"data": articles,
			"extend": gin.H{
				"count": total,
				"limit": req.PageSize,
			},
		},
	})
}

// SyncData 同步文章数据
func (h *GoogleHandler) SyncData(c *gin.Context) {

	// 如果本地数据库查询失败，尝试从远程API获取
	apiURL := fmt.Sprintf("%s/v2/google/resultList", baseURL)
	params := url.Values{}
	params.Add("security_key", "4p2HQQD84Jm2pQDn") // c.Query("key")
	if limit := c.Query("limit"); limit != "" {
		params.Add("limit", limit)
	}
	if page := c.Query("page"); page != "" {
		params.Add("page", page)
	}

	resp, err := http.Get(apiURL + "?" + params.Encode())
	if err != nil {
		c.JSON(http.StatusInternalServerError, gin.H{
			"msg":  "failed to get data from both local and remote",
			"code": 500,
			"time": time.Now().Unix(),
		})
		return
	}
	defer resp.Body.Close()

	body, err := io.ReadAll(resp.Body)
	if err != nil {
		c.JSON(http.StatusInternalServerError, gin.H{
			"msg":  "failed to read response",
			"code": 500,
			"time": time.Now().Unix(),
		})
		return
	}

	var result map[string]interface{}
	if err := json.Unmarshal(body, &result); err != nil {
		c.JSON(http.StatusInternalServerError, gin.H{
			"msg":  "failed to parse response",
			"code": 500,
			"time": time.Now().Unix(),
		})
		return
	}
	if /*result["code"].(float64) == 200 && */ result["data"] != nil {
		// 将data重新序列化为JSON字符串
		dataJSON, err := json.Marshal(result["data"])
		if err != nil {
			c.JSON(http.StatusInternalServerError, gin.H{
				"msg":  "failed to marshal data",
				"code": 500,
				"time": time.Now().Unix(),
			})
			return
		}
		var dataResult map[string]interface{}

		err = json.Unmarshal(dataJSON, &dataResult)

		dataResultJson, err := json.Marshal(dataResult["data"])

		// 定义一个临时结构来接收数据
		var dataArray []struct {
			URLWPPost      string `json:"url_wp_post"`
			FinalURL       string `json:"final_url"`
			Channel        string `json:"available_channel"`
			CampaignName   string `json:"campaign_name"`
			OfferCategory  string `json:"offer_category"`
			Language       string `json:"language"`
			TrafficSource  string `json:"traffic_source"`
			TrackingMethod string `json:"tracking_method"`
			PixelID        string `json:"pixel_id"`
			Token          string `json:"token"`
			EventName      string `json:"event_name"`
			Keywords       string `json:"keywords"`
			ArticleTitle   string `json:"article_title"`
			ArticleIntro   string `json:"article_intro"`
		}

		// 解析JSON到结构体数组
		if err := json.Unmarshal(dataResultJson, &dataArray); err != nil {
			c.JSON(http.StatusInternalServerError, gin.H{
				"msg":  "failed to parse data array",
				"code": 500,
				"time": time.Now().Unix(),
			})
			return
		}

		// 转换为GoogleArticle数组
		articles := make([]*model.GoogleArticle, 0, len(dataArray))
		campaignNames := make([]string, 0, len(dataArray))

		// 收集所有的campaign_name
		for _, item := range dataArray {
			campaignNames = append(campaignNames, item.CampaignName)
			article := &model.GoogleArticle{
				Status:         1,
				URLWPPost:      item.URLWPPost,
				FinalURL:       item.FinalURL,
				Channel:        item.Channel,
				CampaignName:   item.CampaignName,
				OfferCategory:  item.OfferCategory,
				Language:       item.Language,
				TrafficSource:  item.TrafficSource,
				TrackingMethod: item.TrackingMethod,
				PixelID:        item.PixelID,
				Token:          item.Token,
				EventName:      item.EventName,
				Keywords:       item.Keywords,
				ArticleTitle:   item.ArticleTitle,
				ArticleIntro:   item.ArticleIntro,
			}
			articles = append(articles, article)
		}

		h.articleDao = dao.NewGoogleArticleDAO(store.DB)

		// 批量获取现有记录
		existingArticles, err := h.articleDao.GetByCampaignNames(campaignNames)
		if err != nil {
			log.Printf("Failed to get existing articles: %v", err)
			c.JSON(http.StatusInternalServerError, gin.H{
				"msg":  "failed to get existing articles",
				"code": 500,
				"time": time.Now().Unix(),
			})
			return
		}

		// 分离需要更新和需要创建的记录
		var toUpdate []*model.GoogleArticle
		var toCreate []*model.GoogleArticle

		for _, article := range articles {
			if existing, ok := existingArticles[article.CampaignName]; ok {
				// 如果记录存在，只更新FinalURL
				existing.FinalURL = article.FinalURL
				toUpdate = append(toUpdate, existing)
			} else {
				// 如果记录不存在，加入待创建列表
				toCreate = append(toCreate, article)
			}
		}

		// 批量更新
		if len(toUpdate) > 0 {
			if err := h.articleDao.BatchUpdate(toUpdate); err != nil {
				log.Printf("Failed to batch update articles: %v", err)
			}
		}

		// 批量创建
		if len(toCreate) > 0 {
			if err := h.articleDao.BatchCreate(toCreate); err != nil {
				log.Printf("Failed to batch create articles: %v", err)
			}
		}
	}
	c.JSON(resp.StatusCode, config.Result{1, "", nil})
	return
}

// RequestLog 未通过审核文章查询
func (h *GoogleHandler) RequestLog(c *gin.Context) {
	// 从本地数据库查询未通过审核的文章
	req := &model.GoogleArticleListRequest{}
	if err := c.ShouldBindJSON(&req); err != nil {
		c.JSON(http.StatusBadRequest, gin.H{
			"msg":  "failed to parse request",
			"code": 400,
			"time": time.Now().Unix(),
		})
		return
	}
	userId, exists := c.Get("userId")
	if !exists {
		c.JSON(http.StatusUnauthorized, gin.H{
			"msg":  "unauthorized",
			"code": 401,
			"time": time.Now().Unix(),
		})
		return
	}
	req.CreatedBy = userId.(int)
	articles, total, err := dao.NewGoogleArticleDAO(store.DB).List(req)
	if err != nil {
		// 如果本地数据库查询失败，尝试从远程API获取
		apiURL := fmt.Sprintf("%s/v2/google/requestLog", baseURL)
		params := url.Values{}
		params.Add("security_key", c.Query("security_key"))

		resp, err := http.Get(apiURL + "?" + params.Encode())
		if err != nil {
			c.JSON(http.StatusInternalServerError, gin.H{
				"msg":  "failed to get data from both local and remote",
				"code": 500,
				"time": time.Now().Unix(),
			})
			return
		}
		defer resp.Body.Close()

		body, err := io.ReadAll(resp.Body)
		if err != nil {
			c.JSON(http.StatusInternalServerError, gin.H{
				"msg":  "failed to read response",
				"code": 500,
				"time": time.Now().Unix(),
			})
			return
		}

		var result map[string]interface{}
		if err := json.Unmarshal(body, &result); err != nil {
			c.JSON(http.StatusInternalServerError, gin.H{
				"msg":  "failed to parse response",
				"code": 500,
				"time": time.Now().Unix(),
			})
			return
		}

		// 如果API调用成功，更新本地数据库
		if result["code"].(float64) == 200 && result["data"] != nil {
			data := result["data"].(map[string]interface{})
			if items, ok := data["data"].([]interface{}); ok {
				for _, item := range items {
					itemData := item.(map[string]interface{})
					article := &model.GoogleArticle{
						CampaignName: itemData["campaign_name"].(string),
						ReviewResult: itemData["review_result"].(string),
						ResultReason: itemData["reason"].(string),
						Status:       2, // 已拒绝状态
					}
					h.articleDao.Create(article)
				}
			}
		}

		c.JSON(resp.StatusCode, result)
		return
	}

	// 返回本地数据库的结果
	c.JSON(http.StatusOK, gin.H{
		"msg":  "success",
		"code": 1,
		"time": time.Now().Unix(),
		"data": gin.H{
			"code": 0,
			"data": articles,
			"extend": gin.H{
				"count": total,
				"limit": nil,
			},
		},
	})
}

// ReportDataSource 收益报告
func (h *GoogleHandler) ReportDataSource(c *gin.Context) {
	dataDate := c.Query("data_date")
	if dataDate == "" {
		c.JSON(http.StatusBadRequest, gin.H{
			"msg":  "data_date is required",
			"code": 400,
			"time": time.Now().Unix(),
		})
		return
	}

	// 首先从本地数据库获取
	req := &model.GoogleReportRequest{
		DataDate: dataDate,
		Page:     1,
		PageSize: 10,
	}
	if limit := c.Query("limit"); limit != "" {
		if pageSize, err := strconv.Atoi(limit); err == nil {
			req.PageSize = pageSize
		}
	}
	if page := c.Query("page"); page != "" {
		if pageNum, err := strconv.Atoi(page); err == nil {
			req.Page = pageNum
		}
	}

	reports, total, err := h.reportDao.List(req)
	if err != nil || total == 0 {
		// 如果本地数据库查询失败或没有数据，从远程API获取
		apiURL := fmt.Sprintf("%s/v2/google/reportDataSource", baseURL)
		params := url.Values{}
		params.Add("security_key", c.Query("security_key"))
		params.Add("data_date", dataDate)
		if limit := c.Query("limit"); limit != "" {
			params.Add("limit", limit)
		}
		if page := c.Query("page"); page != "" {
			params.Add("page", page)
		}

		resp, err := http.Get(apiURL + "?" + params.Encode())
		if err != nil {
			c.JSON(http.StatusInternalServerError, gin.H{
				"msg":  "failed to get data from remote",
				"code": 500,
				"time": time.Now().Unix(),
			})
			return
		}
		defer resp.Body.Close()

		body, err := io.ReadAll(resp.Body)
		if err != nil {
			c.JSON(http.StatusInternalServerError, gin.H{
				"msg":  "failed to read response",
				"code": 500,
				"time": time.Now().Unix(),
			})
			return
		}

		var result map[string]interface{}
		if err := json.Unmarshal(body, &result); err != nil {
			c.JSON(http.StatusInternalServerError, gin.H{
				"msg":  "failed to parse response",
				"code": 500,
				"time": time.Now().Unix(),
			})
			return
		}

		// 如果API调用成功，保存到数据库
		if result["code"].(float64) == 200 && result["data"] != nil {
			data := result["data"].(map[string]interface{})
			if list, ok := data["list"].([]interface{}); ok {
				// 删除当天的旧数据
				h.reportDao.DeleteByDate(dataDate)

				// 保存新数据
				reports := make([]*model.GoogleReport, 0, len(list))
				for _, item := range list {
					itemData := item.(map[string]interface{})
					report := &model.GoogleReport{
						CampaignName:            itemData["campaign_name"].(string),
						Platform:                itemData["platform"].(string),
						Country:                 itemData["country"].(string),
						Hour:                    int(itemData["hour"].(float64)),
						RelatedLinksRequests:    int64(itemData["relatedLinksRequests"].(float64)),
						RelatedLinksImpressions: int64(itemData["relatedLinksImpressions"].(float64)),
						RelatedLinksClicks:      int64(itemData["relatedLinksClicks"].(float64)),
						RelatedLinksRpm:         itemData["relatedLinksRpm"].(float64),
						AdRequests:              int64(itemData["adRequests"].(float64)),
						MatchedAdRequests:       int64(itemData["matchedAdRequests"].(float64)),
						AdImpressions:           int64(itemData["adImpressions"].(float64)),
						Impressions:             int64(itemData["impressions"].(float64)),
						Clicks:                  int64(itemData["clicks"].(float64)),
						Ctr:                     itemData["ctr"].(float64),
						AdCtr:                   itemData["adCtr"].(float64),
						AdRpm:                   itemData["adRpm"].(float64),
						Cr:                      itemData["cr"].(float64),
						Revenue:                 itemData["revenue"].(float64),
						DataDate:                dataDate,
					}
					reports = append(reports, report)
				}

				if err := h.reportDao.BatchCreate(reports); err != nil {
					// 保存失败时仍返回API结果
					c.JSON(resp.StatusCode, result)
					return
				}
			}
		}

		c.JSON(resp.StatusCode, result)
		return
	}

	// 返回本地数据库的结果
	c.JSON(http.StatusOK, gin.H{
		"msg":  "success",
		"code": 200,
		"time": time.Now().Unix(),
		"data": gin.H{
			"list":      reports,
			"total":     total,
			"page":      req.Page,
			"page_size": req.PageSize,
			"max_page":  (total + int64(req.PageSize) - 1) / int64(req.PageSize),
		},
	})
}

// CateList 大盘方向和类别
func (h *GoogleHandler) CateList(c *gin.Context) {
	// 构建请求URL和参数
	apiURL := fmt.Sprintf("%s/v2/campaign/cateList", baseURL)
	params := url.Values{}
	params.Add("security_key", c.Query("security_key"))

	// 发送请求
	resp, err := http.Get(apiURL + "?" + params.Encode())
	if err != nil {
		c.JSON(http.StatusInternalServerError, gin.H{
			"msg":  "failed to call external API",
			"code": 500,
			"time": time.Now().Unix(),
		})
		return
	}
	defer resp.Body.Close()

	// 读取响应
	body, err := io.ReadAll(resp.Body)
	if err != nil {
		c.JSON(http.StatusInternalServerError, gin.H{
			"msg":  "failed to read response",
			"code": 500,
			"time": time.Now().Unix(),
		})
		return
	}

	// 转发响应
	var result map[string]interface{}
	if err := json.Unmarshal(body, &result); err != nil {
		c.JSON(http.StatusInternalServerError, gin.H{
			"msg":  "failed to parse response",
			"code": 500,
			"time": time.Now().Unix(),
		})
		return
	}

	c.JSON(resp.StatusCode, result)
}

// GetCampaign 查询广告系列状态
func (h *GoogleHandler) GetCampaign(c *gin.Context) {
	// 处理从请求中获取campaign ID的两种可能方式
	var campaignID string

	// 尝试从URL查询参数获取
	campaignID = c.Query("campaign_id")

	// 如果查询参数为空，尝试从请求体获取
	if campaignID == "" {
		var req struct {
			CampaignID string `json:"campaign_id"`
			Key        string `json:"key"`
		}

		if err := c.ShouldBindJSON(&req); err == nil {
			campaignID = req.CampaignID

			// 如果从请求体获取到了key，设置安全密钥
			if req.Key != "" {
				service.GetAdTechHTTPClient().SetSecurityKey(req.Key)
			}
		}
	}

	// 如果campaign ID仍为空，返回错误
	if campaignID == "" {
		c.JSON(http.StatusBadRequest, gin.H{
			"msg":  "campaign_id is required",
			"code": 400,
			"time": time.Now().Unix(),
		})
		return
	}

	// 如果从请求体没有获取到key，则尝试从URL查询参数获取
	if key := c.Query("key"); key != "" {
		service.GetAdTechHTTPClient().SetSecurityKey(key)
	}

	// 获取AdTechCampaignService实例
	campaignService := service.GetAdTechCampaignService()

	// 调用GetCampaign方法查询广告系列状态
	response, err := campaignService.GetCampaign(c.Request.Context(), campaignID, "")
	if err != nil {
		c.JSON(http.StatusInternalServerError, gin.H{
			"msg":  fmt.Sprintf("failed to get campaign status: %v", err),
			"code": 500,
			"time": time.Now().Unix(),
		})
		return
	}

	// 返回成功响应
	c.JSON(http.StatusOK, config.Result{
		Code: 1,
		Msg:  "",
		Data: response,
	})
}
