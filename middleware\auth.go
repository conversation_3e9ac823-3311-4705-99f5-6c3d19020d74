package middleware

import (
	"context"
	"os"
	"rsoc-system-go/dao"
	"rsoc-system-go/middleware/pkg/redis"
	"rsoc-system-go/store"
	"strconv"
	"strings"

	"net/http"

	"github.com/gin-gonic/gin"
	"github.com/golang-jwt/jwt/v5"
)

var JwtKey = []byte("rosc")

func ReplayAuthMiddleware() gin.HandlerFunc {
	return func(c *gin.Context) {
		var tokenStr string
		tokenStr = c.GetHeader("Token")
		if tokenStr == "" {
			//c.Redirect(301, "/admin/login")
			//c.HTML(http.StatusOK, "login.html", gin.H{})
			c.JSON(200, gin.H{
				"code": 3000,
				"msg":  "未登录",
			})
			c.Abort()
			return
		}
		/*redisManager := store.GetRedisManager()
		err := redisManager.ConnectRedis("task")
		client := redisManager.GetRedisClient("task")*/
		indexdbStr := os.Getenv("redisdb")
		indexdb, _ := strconv.Atoi(indexdbStr)
		client := redis.SelectDB(indexdb)
		//client := redis.SelectDB(1)
		ctx := context.Background()
		result, err := client.HGet(ctx, "Token", tokenStr).Result()

		if err != nil {
			c.JSON(200, gin.H{
				"code": 3000,
				"msg":  "token校验出错",
			})
			c.Abort()
			return
		}
		if result == "" {
			c.JSON(200, gin.H{
				"code": 3001,
				"msg":  "未登录",
			})
			c.Abort()
		}
		if result == tokenStr {
			userDao := dao.NewUserDao(store.DB)
			userDate, err := userDao.FindByToken(tokenStr)
			if err != nil {
				c.JSON(200, gin.H{
					"code": 3000,
					"msg":  "token校验出错",
				})
			}
			c.Set("userId", userDate.ID)
			c.Next()
		}

	}
}

func AuthMiddleware() gin.HandlerFunc {
	return func(c *gin.Context) {
		// 从请求头中获取JWT令牌
		var tokenStr string
		tokenStr = c.GetHeader("Authorization")
		if tokenStr == "" {
			tokenStr, _ = c.Cookie("Authorization")
		}
		if tokenStr == "" {
			//c.Redirect(301, "/admin/login")
			//c.HTML(http.StatusOK, "login.html", gin.H{})
			c.JSON(200, gin.H{
				"code": 3000,
				"msg":  "未登录",
			})
			c.Abort()
			return
		}

		// 解析JWT令牌
		token, err := jwt.ParseWithClaims(tokenStr, &jwt.RegisteredClaims{}, func(token *jwt.Token) (interface{}, error) {
			return JwtKey, nil
		})
		if err != nil {
			//c.Redirect(301, "/admin/login") //c.JSON(http.StatusUnauthorized, gin.H{
			//    "error": "Invalid authorization token",
			//})
			c.JSON(200, gin.H{
				"code": 3000,
				"msg":  "未登录",
			})
			c.Abort()
			return
		}

		// 验证令牌是否有效
		if claims, ok := token.Claims.(*jwt.RegisteredClaims); ok && token.Valid {
			// 将用户ID存储在gin.Context中,供后续使用
			c.Set("userId", claims.Subject)
			c.Next()
		} else {
			//c.Redirect(301, "/admin/login") //c.JSON(http.StatusUnauthorized, gin.H{
			//    "error": "Invalid authorization token",
			//})
			c.JSON(200, gin.H{
				"code": 3000,
				"msg":  "未登录",
			})
			c.Abort()
		}
	}
}

func MiddleWare() gin.HandlerFunc {
	return func(c *gin.Context) {
		// 只拦截 /admin/ 路径的请求
		if !strings.HasPrefix(c.Request.URL.Path, "/admin/") {
			c.Next()
			return
		}

		// 登录页面不需要验证
		if c.Request.URL.Path == "/admin/login" || c.Request.URL.Path == "/admin/do-login" {
			c.Next()
			return
		}

		var tokenStr string
		tokenStr = c.GetHeader("Authorization")
		if tokenStr == "" {
			tokenStr, _ = c.Cookie("Authorization")
		}
		if tokenStr == "" {
			c.Redirect(301, "/admin/login")
			c.Abort()
			return
		}

		// 解析JWT令牌
		token, err := jwt.ParseWithClaims(tokenStr, &jwt.RegisteredClaims{}, func(token *jwt.Token) (interface{}, error) {
			return JwtKey, nil
		})
		if err != nil {
			if c.Writer.Status() == 404 {
				c.HTML(404, "404.html", nil)
			} else {
				c.Redirect(301, "/admin/login")
			}
			c.Abort()
			return
		}

		// 验证令牌是否有效
		if claims, ok := token.Claims.(*jwt.RegisteredClaims); ok && token.Valid {
			if c.Writer.Status() == 404 {
				c.HTML(404, "404.html", nil)
			}
			// 将用户ID存储在gin.Context中,供后续使用
			c.Set("userId", claims.Subject)
			c.Next()
		} else {
			if c.Writer.Status() == 404 {
				c.HTML(404, "404.html", nil)
			} else {
				c.Redirect(301, "/admin/login")
			}
			c.Abort()
		}
	}
}

// Auth 认证中间件
func Auth() gin.HandlerFunc {
	return func(c *gin.Context) {
		// 从请求头中获取token
		token := c.GetHeader("Authorization")
		if token == "" {
			c.JSON(http.StatusUnauthorized, gin.H{"error": "未登录"})
			c.Abort()
			return
		}

		// TODO: 实现token验证逻辑
		// 这里应该验证token的有效性，并从token中获取用户信息
		// 为了示例，这里简单地设置一个模拟的用户ID
		c.Set("user_id", uint(1))

		c.Next()
	}
}
