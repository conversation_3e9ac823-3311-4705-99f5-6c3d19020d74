package model

// PostbackRequest Postback设置请求结构
type PostbackRequest struct {
	SecurityKey string `form:"security_key" json:"security_key" binding:"required"`
	DomainName  string `form:"domain_name" json:"domain_name" binding:"required"`
	Campaign    string `form:"campaign" json:"campaign"`
	ClickID     string `form:"click_id" json:"click_id"`
	Payout      string `form:"payout" json:"payout"`
	Country     string `form:"country" json:"country"`
	Zip         string `form:"zip" json:"zip"`
	OSType      string `form:"os_type" json:"os_type"`
	Browser     string `form:"browser" json:"browser"`
	DeviceType  string `form:"device_type" json:"device_type"`
	DeviceBrand string `form:"device_brand" json:"device_brand"`
	S1          string `form:"s1" json:"s1"`
	S2          string `form:"s2" json:"s2"`
	S3          string `form:"s3" json:"s3"`
}

// PostbackFieldMapping Postback字段映射
type PostbackFieldMapping struct {
	Campaign    string `json:"campaign,omitempty"`
	ClickID     string `json:"click_id,omitempty"`
	Payout      string `json:"payout,omitempty"`
	Country     string `json:"country,omitempty"`
	Zip         string `json:"zip,omitempty"`
	OSType      string `json:"os_type,omitempty"`
	Browser     string `json:"browser,omitempty"`
	DeviceType  string `json:"device_type,omitempty"`
	DeviceBrand string `json:"device_brand,omitempty"`
	S1          string `json:"s1,omitempty"`
	S2          string `json:"s2,omitempty"`
	S3          string `json:"s3,omitempty"`
}

// PostbackResponse Postback响应结构
type PostbackResponse struct {
	Code int         `json:"code"`
	Msg  string      `json:"msg"`
	Time int64       `json:"time"`
	Data interface{} `json:"data,omitempty"`
}

// PostbackData 接收postback数据的结构（兼容AdTech和SedoTMP格式）
type PostbackData struct {
	// 基础参数
	Campaign   string `json:"campaign" form:"campaign"`
	AdClickCPC string `json:"ad_click_cpc" form:"ad_click_cpc"`
	ClickID    string `json:"click_id" form:"click_id"`
	Payout     string `json:"payout" form:"payout"`
	EPayout    string `json:"epayout" form:"epayout"` // SedoTMP 估算收益

	// 地理位置参数
	Country     string `json:"country" form:"country"`
	CountryName string `json:"country_name" form:"country_name"`
	State       string `json:"state" form:"state"`
	City        string `json:"city" form:"city"`
	Zip         string `json:"zip" form:"zip"`

	// 设备和浏览器参数
	OSType      string `json:"os_type" form:"os_type"`
	Browser     string `json:"browser" form:"browser"`
	DeviceType  string `json:"device_type" form:"device_type"`
	DeviceBrand string `json:"device_brand" form:"device_brand"`

	// 自定义子ID参数（AdTech格式）
	S1 string `json:"s1" form:"s1"`
	S2 string `json:"s2" form:"s2"`
	S3 string `json:"s3" form:"s3"`

	// 自定义子ID参数（SedoTMP格式）
	SubID1 string `json:"subid1" form:"subid1"`
	SubID2 string `json:"subid2" form:"subid2"`
	SubID3 string `json:"subid3" form:"subid3"`
	SubID4 string `json:"subid4" form:"subid4"`
	SubID5 string `json:"subid5" form:"subid5"`
}

// GetEffectivePayout 获取有效的收益值（优先使用payout，其次epayout）
func (p *PostbackData) GetEffectivePayout() string {
	if p.Payout != "" {
		return p.Payout
	}
	return p.EPayout
}

// GetEffectiveSubID 获取有效的子ID（合并s1-s3和subid1-subid5）
func (p *PostbackData) GetEffectiveSubID(index int) string {
	switch index {
	case 1:
		if p.S1 != "" {
			return p.S1
		}
		return p.SubID1
	case 2:
		if p.S2 != "" {
			return p.S2
		}
		return p.SubID2
	case 3:
		if p.S3 != "" {
			return p.S3
		}
		return p.SubID3
	case 4:
		return p.SubID4
	case 5:
		return p.SubID5
	default:
		return ""
	}
}
