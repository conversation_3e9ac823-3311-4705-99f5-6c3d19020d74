package service

import (
	"context"
	"encoding/json"
	"fmt"
	"net/url"
	"rsoc-system-go/model"
	"sync"
)

// PostbackService Postback服务
type PostbackService struct {
	httpClient *AdTechHTTPClient
}

var (
	postbackService     *PostbackService
	postbackServiceOnce sync.Once
)

// GetPostbackService 获取Postback服务单例
func GetPostbackService() *PostbackService {
	postbackServiceOnce.Do(func() {
		postbackService = NewPostbackService(GetAdTechHTTPClient())
	})
	return postbackService
}

// NewPostbackService 创建新的Postback服务
func NewPostbackService(httpClient *AdTechHTTPClient) *PostbackService {
	return &PostbackService{
		httpClient: httpClient,
	}
}

// SetPostbackDomain 设置Postback域名配置
func (s *PostbackService) SetPostbackDomain(ctx context.Context, request *model.PostbackRequest) (*model.PostbackResponse, error) {
	// 设置安全密钥
	s.httpClient.SetSecurityKey(request.SecurityKey)

	// 构建查询参数
	query := url.Values{}
	query.Set("domain_name", request.DomainName)

	// 添加字段映射参数
	if request.Campaign != "" {
		query.Set("campaign", request.Campaign)
	}
	if request.ClickID != "" {
		query.Set("click_id", request.ClickID)
	}
	if request.Payout != "" {
		query.Set("payout", request.Payout)
	}
	if request.Country != "" {
		query.Set("country", request.Country)
	}
	if request.Zip != "" {
		query.Set("zip", request.Zip)
	}
	if request.OSType != "" {
		query.Set("os_type", request.OSType)
	}
	if request.Browser != "" {
		query.Set("browser", request.Browser)
	}
	if request.DeviceType != "" {
		query.Set("device_type", request.DeviceType)
	}
	if request.DeviceBrand != "" {
		query.Set("device_brand", request.DeviceBrand)
	}
	if request.S1 != "" {
		query.Set("s1", request.S1)
	}
	if request.S2 != "" {
		query.Set("s2", request.S2)
	}
	if request.S3 != "" {
		query.Set("s3", request.S3)
	}

	// 发送POST请求到AdTech API
	resp, err := s.httpClient.Request(ctx, "POST", "/campaign/postbackDomain", query, nil, nil)
	if err != nil {
		return nil, fmt.Errorf("设置postback域名请求失败: %v", err)
	}

	// 解析响应
	var response model.PostbackResponse
	if err := s.httpClient.ParseResponse(resp, &response); err != nil {
		return nil, fmt.Errorf("解析postback响应失败: %v", err)
	}

	return &response, nil
}

// BuildPostbackURL 构建Postback URL
func (s *PostbackService) BuildPostbackURL(baseURL string, mapping *model.PostbackFieldMapping) string {
	if baseURL == "" {
		return ""
	}

	// 解析基础URL
	u, err := url.Parse(baseURL)
	if err != nil {
		return baseURL
	}

	// 获取现有查询参数
	query := u.Query()

	// 添加字段映射参数
	if mapping.Campaign != "" {
		query.Set("campaign", mapping.Campaign)
	}
	if mapping.ClickID != "" {
		query.Set("click_id", mapping.ClickID)
	}
	if mapping.Payout != "" {
		query.Set("payout", mapping.Payout)
	}
	if mapping.Country != "" {
		query.Set("country", mapping.Country)
	}
	if mapping.Zip != "" {
		query.Set("zip", mapping.Zip)
	}
	if mapping.OSType != "" {
		query.Set("os_type", mapping.OSType)
	}
	if mapping.Browser != "" {
		query.Set("browser", mapping.Browser)
	}
	if mapping.DeviceType != "" {
		query.Set("device_type", mapping.DeviceType)
	}
	if mapping.DeviceBrand != "" {
		query.Set("device_brand", mapping.DeviceBrand)
	}
	if mapping.S1 != "" {
		query.Set("s1", mapping.S1)
	}
	if mapping.S2 != "" {
		query.Set("s2", mapping.S2)
	}
	if mapping.S3 != "" {
		query.Set("s3", mapping.S3)
	}

	// 重新构建URL
	u.RawQuery = query.Encode()
	return u.String()
}

// ValidatePostbackRequest 验证Postback请求
func (s *PostbackService) ValidatePostbackRequest(request *model.PostbackRequest) error {
	if request.SecurityKey == "" {
		return fmt.Errorf("security_key 不能为空")
	}
	if request.DomainName == "" {
		return fmt.Errorf("domain_name 不能为空")
	}

	// 验证域名格式
	if _, err := url.Parse(request.DomainName); err != nil {
		return fmt.Errorf("domain_name 格式无效: %v", err)
	}

	return nil
}

// ConvertToFieldMapping 将请求转换为字段映射
func (s *PostbackService) ConvertToFieldMapping(request *model.PostbackRequest) *model.PostbackFieldMapping {
	return &model.PostbackFieldMapping{
		Campaign:    request.Campaign,
		ClickID:     request.ClickID,
		Payout:      request.Payout,
		Country:     request.Country,
		Zip:         request.Zip,
		OSType:      request.OSType,
		Browser:     request.Browser,
		DeviceType:  request.DeviceType,
		DeviceBrand: request.DeviceBrand,
		S1:          request.S1,
		S2:          request.S2,
		S3:          request.S3,
	}
}

// ConvertFieldMappingToJSON 将字段映射转换为JSON字符串
func (s *PostbackService) ConvertFieldMappingToJSON(mapping *model.PostbackFieldMapping) (string, error) {
	data, err := json.Marshal(mapping)
	if err != nil {
		return "", fmt.Errorf("转换字段映射为JSON失败: %v", err)
	}
	return string(data), nil
}

// ParseFieldMappingFromJSON 从JSON字符串解析字段映射
func (s *PostbackService) ParseFieldMappingFromJSON(jsonStr string) (*model.PostbackFieldMapping, error) {
	var mapping model.PostbackFieldMapping
	if err := json.Unmarshal([]byte(jsonStr), &mapping); err != nil {
		return nil, fmt.Errorf("解析字段映射JSON失败: %v", err)
	}
	return &mapping, nil
}
