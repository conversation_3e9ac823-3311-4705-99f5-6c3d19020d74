package logger

import (
	"fmt"
	"io"
	"log"
	"os"
	"path/filepath"
	"time"

	"github.com/gin-gonic/gin"
	"github.com/sirupsen/logrus"
)

var (
	Log    *logrus.Logger
	logDir = "logs"
	writer io.Writer
)

// InitLogger 初始化日志系统
func InitLogger() error {
	Log = logrus.New()
	Log.SetFormatter(&logrus.JSONFormatter{
		TimestampFormat: "2006-01-02 15:04:05",
	})

	// 创建日志目录
	today := time.Now().Format("2006-01-02")
	dailyLogDir := filepath.Join(logDir, today)
	if err := os.MkdirAll(dailyLogDir, 0755); err != nil {
		return fmt.Errorf("创建日志目录失败: %v", err)
	}

	// 设置日志文件
	logFile := filepath.Join(dailyLogDir, "app.log")
	file, err := os.OpenFile(logFile, os.O_CREATE|os.O_WRONLY|os.O_APPEND, 0666)
	if err != nil {
		return fmt.Errorf("打开日志文件失败: %v", err)
	}

	// 创建多重写入器
	writer = io.MultiWriter(file, os.Stdout)

	// 设置logrus输出
	Log.SetOutput(writer)

	// 设置标准库log输出
	log.SetOutput(writer)
	log.SetFlags(log.Ldate | log.Ltime | log.Lshortfile)

	// 设置gin的日志输出
	gin.DefaultWriter = writer
	gin.DefaultErrorWriter = writer

	// 启动定时清理任务
	go cleanOldLogs()

	return nil
}

// GetGinLogger 获取gin的日志中间件
func GetGinLogger() gin.HandlerFunc {
	return gin.LoggerWithConfig(gin.LoggerConfig{
		Output: writer,
		Formatter: func(param gin.LogFormatterParams) string {
			return fmt.Sprintf("[GIN] %s | %s | %d | %s | %s | %s | %s | %s\n",
				param.TimeStamp.Format("2006-01-02 15:04:05"),
				param.ClientIP,
				param.StatusCode,
				param.Method,
				param.Path,
				param.Request.Proto,
				param.Latency,
				param.ErrorMessage,
			)
		},
	})
}

// ConsoleHook 用于同时输出到控制台的Hook
type ConsoleHook struct{}

func NewConsoleHook() *ConsoleHook {
	return &ConsoleHook{}
}

func (hook *ConsoleHook) Fire(entry *logrus.Entry) error {
	line, err := entry.String()
	if err != nil {
		return err
	}
	fmt.Printf("%s", line)
	return nil
}

func (hook *ConsoleHook) Levels() []logrus.Level {
	return logrus.AllLevels
}

// cleanOldLogs 清理30天前的日志
func cleanOldLogs() {
	ticker := time.NewTicker(24 * time.Hour)
	for range ticker.C {
		log.Println("清理日志")
		threshold := time.Now().AddDate(0, 0, -30)

		err := filepath.Walk(logDir, func(path string, info os.FileInfo, err error) error {
			if err != nil {
				return err
			}

			if info.IsDir() && path != logDir {
				dirDate, err := time.Parse("2006-01-02", filepath.Base(path))
				if err != nil {
					return nil
				}

				if dirDate.Before(threshold) {
					if err := os.RemoveAll(path); err != nil {
						Log.Errorf("删除旧日志目录失败: %v", err)
					} else {
						Log.Infof("已删除旧日志目录: %s", path)
					}
				}
			}
			return nil
		})

		if err != nil {
			Log.Errorf("清理旧日志失败: %v", err)
		}
	}
}
