# SedoTMP 专用回调接口使用示例

## 概述

`/rsoc/sedotmp/callback` 是专门为 SedoTMP 平台设计的回调接口，完全支持 SedoTMP 官方的所有宏参数。这个接口与通用的 `/rsoc/postback` 接口业务逻辑相同，但专门针对 SedoTMP 的参数格式进行了优化。

## 接口对比

| 特性 | `/rsoc/postback` | `/rsoc/sedotmp/callback` |
|------|------------------|--------------------------|
| 用途 | 通用第三方回调 | SedoTMP 专用回调 |
| 参数格式 | 混合格式支持 | 纯 SedoTMP 格式 |
| 收益参数 | payout/epayout | epayout |
| 自定义参数 | s1-s3 + subid1-subid5 | subid1-subid5 |
| 地理参数 | 基础支持 | 完整支持（包含 country_name, state, city） |
| 设备参数 | 基础支持 | 完整支持 |

## 使用场景

### 场景1：SedoTMP 平台集成

当您在 SedoTMP 平台投放广告时，可以使用专用的回调接口来接收转化数据。

#### 在 SedoTMP 平台配置

1. **登录 SedoTMP 管理后台**
2. **找到 Postback 设置页面**
3. **配置回调 URL**：

**基础配置（推荐）：**
```
http://your-domain.com/rsoc/sedotmp/callback?campaign={campaign}&click_id={click_id}&epayout={epayout}&country={country}&subid1={subid1}&subid2={subid2}
```

**完整配置（包含所有参数）：**
```
http://your-domain.com/rsoc/sedotmp/callback?campaign={campaign}&click_id={click_id}&epayout={epayout}&country={country}&country_name={country_name}&state={state}&city={city}&zip={zip}&os_type={os_type}&browser={browser}&device_type={device_type}&device_brand={device_brand}&subid1={subid1}&subid2={subid2}&subid3={subid3}&subid4={subid4}&subid5={subid5}
```

### 场景2：移动端流量优化

针对移动端流量，您可以配置特定的参数来更好地跟踪转化：

```
http://your-domain.com/rsoc/sedotmp/callback?campaign={campaign}&click_id={click_id}&epayout={epayout}&country={country}&os_type={os_type}&device_type={device_type}&device_brand={device_brand}&subid1={subid1}&subid2={subid2}
```

### 场景3：地理位置细分

如果您需要详细的地理位置数据进行分析：

```
http://your-domain.com/rsoc/sedotmp/callback?campaign={campaign}&click_id={click_id}&epayout={epayout}&country={country}&country_name={country_name}&state={state}&city={city}&zip={zip}&subid1={subid1}
```

## 实际回调示例

### 示例1：美国桌面端用户转化

**SedoTMP 回调：**
```
GET /rsoc/sedotmp/callback?campaign=12345&click_id=us_desktop_001&epayout=1.25&country=US&country_name=United%20States&state=CA&city=Los%20Angeles&zip=90001&os_type=WINDOWS&browser=CHROME&device_type=DESKTOP&device_brand=DELL&subid1=AdSetUS001&subid2=CreativeDesktop&subid3=AudienceRetargeting
```

**数据存储结果：**
- `campaign_id`: 12345
- `real_price`: 1.25
- `date`: 当前日期
- `clicks`: 1
- `update_time`: 当前时间

### 示例2：英国移动端用户转化

**SedoTMP 回调：**
```
GET /rsoc/sedotmp/callback?campaign=67890&click_id=uk_mobile_002&epayout=0.85&country=GB&country_name=United%20Kingdom&state=London&city=London&zip=SW1A&os_type=ANDROID&browser=CHROME&device_type=MOBILE&device_brand=SAMSUNG&subid1=AdSetUK001&subid2=CreativeMobile&subid3=AudienceLookalike&subid4=CampaignTypeConversion&subid5=SeasonSummer
```

**数据存储结果：**
- `campaign_id`: 67890
- `real_price`: 0.85
- `date`: 当前日期
- `clicks`: 1
- `update_time`: 当前时间

### 示例3：加拿大用户转化（POST 方式）

**SedoTMP 回调：**
```
POST /rsoc/sedotmp/callback
Content-Type: application/x-www-form-urlencoded

campaign=99999&click_id=ca_user_003&epayout=2.10&country=CA&country_name=Canada&state=Ontario&city=Toronto&zip=M5V&os_type=MACOS&browser=SAFARI&device_type=DESKTOP&device_brand=APPLE&subid1=AdSetCA001&subid2=CreativeVideo&subid3=AudienceInterest
```

## 监控和分析

### 查看 SedoTMP 转化数据

```sql
-- 查看最近的 SedoTMP 转化数据
SELECT 
    campaign_id,
    date,
    real_price,
    clicks,
    update_time
FROM facebook_insights 
WHERE real_price IS NOT NULL 
ORDER BY update_time DESC 
LIMIT 20;

-- 按广告系列统计转化数据
SELECT 
    campaign_id,
    COUNT(*) as conversion_count,
    SUM(real_price) as total_revenue,
    AVG(real_price) as avg_revenue,
    MAX(update_time) as last_conversion
FROM facebook_insights 
WHERE real_price IS NOT NULL 
GROUP BY campaign_id 
ORDER BY total_revenue DESC;

-- 按日期统计转化趋势
SELECT 
    date,
    COUNT(*) as daily_conversions,
    SUM(real_price) as daily_revenue,
    AVG(real_price) as avg_conversion_value
FROM facebook_insights 
WHERE real_price IS NOT NULL 
  AND date >= DATE_SUB(CURDATE(), INTERVAL 30 DAY)
GROUP BY date 
ORDER BY date DESC;
```

### 日志监控

```bash
# 查看 SedoTMP 回调日志
tail -f /var/log/your-app.log | grep "sedotmp"

# 查看转化处理日志
tail -f /var/log/your-app.log | grep "处理postback数据"

# 实时监控回调请求
tail -f /var/log/your-app.log | grep "接收到postback数据"
```

## 测试和验证

### 手动测试回调接口

```bash
# 测试基础参数
curl "http://your-domain.com/rsoc/sedotmp/callback?campaign=test001&click_id=manual_test&epayout=1.00&country=US"

# 测试完整参数
curl "http://your-domain.com/rsoc/sedotmp/callback?campaign=test002&click_id=full_test&epayout=1.50&country=US&country_name=United%20States&state=CA&city=Los%20Angeles&zip=90001&os_type=WINDOWS&browser=CHROME&device_type=DESKTOP&device_brand=DELL&subid1=TestAd&subid2=TestCreative&subid3=TestAudience&subid4=TestCampaign&subid5=TestSeason"

# 测试 POST 方式
curl -X POST "http://your-domain.com/rsoc/sedotmp/callback" \
  -H "Content-Type: application/x-www-form-urlencoded" \
  -d "campaign=test003&click_id=post_test&epayout=2.00&country=CA&subid1=PostTest"
```

### 验证数据存储

```bash
# 检查测试数据是否正确存储
mysql -u user -p'password' -D database -e "
SELECT campaign_id, real_price, date, update_time 
FROM facebook_insights 
WHERE campaign_id IN ('test001', 'test002', 'test003') 
ORDER BY update_time DESC;"
```

## 故障排除

### 常见问题

1. **回调没有收到**
   - 检查 SedoTMP 平台的回调 URL 配置
   - 确认网络连接和防火墙设置
   - 查看应用日志是否有错误

2. **数据没有存储**
   - 确认 `campaign` 参数是否传递
   - 检查 `epayout` 参数格式是否正确
   - 查看数据库连接状态

3. **收益数据不准确**
   - 确认 SedoTMP 传递的 `epayout` 值
   - 检查数值格式（应为数字）
   - 查看参数解析日志

### 调试步骤

1. **检查回调 URL 配置**
   ```bash
   # 测试回调接口是否可访问
   curl -I "http://your-domain.com/rsoc/sedotmp/callback"
   ```

2. **查看详细日志**
   ```bash
   # 开启详细日志模式
   tail -f /var/log/your-app.log | grep -E "(sedotmp|postback|error)"
   ```

3. **验证参数解析**
   ```bash
   # 测试参数解析
   curl -v "http://your-domain.com/rsoc/sedotmp/callback?campaign=debug&epayout=1.00"
   ```

## 最佳实践

1. **参数优化**：根据业务需求选择必要的参数，避免过多不需要的参数
2. **URL 编码**：确保特殊字符正确编码，特别是空格和特殊符号
3. **监控告警**：设置转化数据异常的告警机制
4. **定期验证**：定期检查转化数据的准确性和完整性
5. **性能优化**：对于高流量场景，考虑使用异步处理

## 与其他接口的配合使用

SedoTMP 专用回调接口可以与其他功能配合使用：

1. **与 Google 报告同步配合**：SedoTMP 回调提供真实转化数据，Google 报告提供流量数据
2. **与通用回调接口配合**：不同平台使用不同的专用接口，便于数据分析和监控
3. **与配置接口配合**：使用 `/rsoc/postback/setup` 配置字段映射，使用专用回调接收数据
