# FacebookInsights模型优化和processPostbackData方法完善

## 任务概述

根据提供的JSON数据格式，优化FacebookInsights模型并完善processPostbackData方法，以支持更丰富的数据字段。

## 提供的JSON数据格式

```json
{
  "campaign_name": "Apply for credit cards",
  "platform": "HighEndMobile",
  "country": "US",
  "hour": 0,
  "relatedLinksRequests": 1,
  "relatedLinksImpressions": 1,
  "relatedLinksClicks": 1,
  "relatedLinksRpm": 1,
  "adRequests": 1,
  "matchedAdRequests": 1,
  "adImpressions": 1,
  "impressions": 1,
  "clicks": 0,
  "ctr": 0,
  "adCtr": 0,
  "adRpm": 640,
  "cr": 0,
  "revenue": "0.6016",
  "create_time": "2025-01-01 16:00:02"
}
```

## 实施内容

### 1. 扩展FacebookInsights模型 ✅

**文件：** `model/facebook_insights.go`

新增字段：
- `CampaignName` - 广告系列名称
- `Platform` - 平台名称  
- `Country` - 国家
- `Hour` - 小时
- `RelatedLinksRequests` - 相关链接请求数
- `RelatedLinksImpressions` - 相关链接展示数
- `RelatedLinksClicks` - 相关链接点击数
- `RelatedLinksRpm` - 相关链接RPM
- `AdRequests` - 广告请求数
- `MatchedAdRequests` - 匹配的广告请求数
- `AdImpressions` - 广告展示数
- `CTR` - 点击率
- `AdCTR` - 广告点击率
- `AdRPM` - 广告RPM
- `CR` - 转化率
- `Revenue` - 收益
- `CreateTime` - 创建时间

### 2. 扩展PostbackData模型 ✅

**文件：** `model/postback.go`

在PostbackData结构中添加对应的接收字段，支持字符串格式接收以便进行类型转换。

### 3. 完善processPostbackData方法 ✅

**文件：** `handler/postback_handler.go`

主要改进：
- 支持多种campaign来源（Campaign字段优先，SubID2备用）
- 增加revenue字段处理
- 重构为模块化方法：
  - `updateExistingInsight()` - 更新现有记录
  - `createNewInsight()` - 创建新记录
  - `parseAndUpdateNumericFields()` - 解析数值字段

### 4. 新增辅助方法

#### updateExistingInsight()
- 更新基础字段（realPrice, revenue）
- 更新扩展字段（campaignName, platform, country）
- 解析并更新数值字段
- 增加点击计数

#### createNewInsight()
- 创建完整的新记录
- 设置所有基础和扩展字段
- 解析创建时间

#### parseAndUpdateNumericFields()
- 安全解析字符串到数值类型
- 支持所有新增的数值字段
- 错误处理确保数据完整性

## 技术特性

### 数据类型处理
- 字符串字段直接映射
- 数值字段安全转换（strconv.Atoi, strconv.ParseFloat）
- 时间字段解析（time.Parse）
- 指针类型支持空值

### 向后兼容性
- 保持现有字段不变
- 新字段为可选，不影响现有功能
- 现有DAO方法无需修改

### 错误处理
- 数值转换失败时跳过该字段
- 时间解析失败时使用默认值
- 保持数据完整性

## 验证结果

✅ 编译测试通过
✅ 保持向后兼容性
✅ 支持完整JSON数据格式
✅ 模块化代码结构

## 使用示例

现在系统可以处理包含所有新字段的postback数据：

```bash
curl -X POST http://localhost:8080/rsoc/postback \
  -H "Content-Type: application/json" \
  -d '{
    "campaign": "123",
    "campaign_name": "Apply for credit cards",
    "platform": "HighEndMobile",
    "country": "US",
    "hour": "0",
    "relatedLinksRequests": "1",
    "adRequests": "1",
    "revenue": "0.6016",
    "create_time": "2025-01-01 16:00:02"
  }'
```

## 数据库影响

新字段将自动添加到facebook_insights表中，GORM会处理表结构的自动迁移。

## 数据库迁移

### 4. 创建SQL迁移脚本 ✅

**文件：** `migrations/001_add_facebook_insights_extended_fields.sql`

- 添加17个新字段的ALTER TABLE语句
- 创建性能优化索引
- 包含字段验证查询

**文件：** `migrations/001_rollback_facebook_insights_extended_fields.sql`

- 完整的回滚脚本
- 删除新增字段和索引
- 数据安全警告

### 5. 更新自动迁移配置 ✅

**文件：** `config/mysql.go`

- 添加FacebookInsights到AutoMigrate列表
- 确保应用启动时自动执行迁移

### 6. 创建迁移执行脚本 ✅

**文件：** `scripts/migrate_facebook_insights.sh` (Linux/macOS)
**文件：** `scripts/migrate_facebook_insights.ps1` (Windows)

功能特性：
- 自动检查MySQL连接
- 备份表结构
- 执行迁移和验证
- 支持回滚操作
- 彩色日志输出

### 7. 创建迁移文档 ✅

**文件：** `docs/database_migration_guide.md`

包含内容：
- 详细的迁移步骤
- 字段映射说明
- 验证方法
- 故障排除指南
- 测试示例

## 迁移方式

### 自动迁移（推荐）
```bash
# 重启应用程序，GORM自动执行迁移
go run main.go
```

### 手动迁移
```bash
# Linux/macOS
./scripts/migrate_facebook_insights.sh migrate

# Windows
.\scripts\migrate_facebook_insights.ps1 migrate

# 直接执行SQL
mysql -u user -p database < migrations/001_add_facebook_insights_extended_fields.sql
```

## 完成状态

✅ FacebookInsights模型扩展完成
✅ PostbackData模型扩展完成
✅ processPostbackData方法完善完成
✅ 辅助方法实现完成
✅ 编译验证通过
✅ 向后兼容性保证
✅ SQL迁移脚本创建完成
✅ 自动迁移配置更新完成
✅ 跨平台执行脚本完成
✅ 完整迁移文档完成
