package model

// GoogleReportDataResponse Google收益报告API响应结构
type GoogleReportDataResponse struct {
	Msg  string               `json:"msg"`
	Code int                  `json:"code"`
	Time int64                `json:"time"`
	Data GoogleReportDataList `json:"data"`
}

// GoogleReportDataList Google收益报告数据列表
type GoogleReportDataList struct {
	List     []GoogleReportDataItem `json:"list"`
	Total    int                    `json:"total"`
	Page     string                 `json:"page"`
	PageSize string                 `json:"page_size"`
	MaxPage  int                    `json:"max_page"`
}

// GoogleReportDataItem Google收益报告数据项
type GoogleReportDataItem struct {
	CampaignId              string  `json:"campaign_id"`
	CampaignName            string  `json:"campaign_name"`
	Platform                string  `json:"platform"`
	Country                 string  `json:"country"`
	Hour                    int     `json:"hour"`
	RelatedLinksRequests    int     `json:"relatedLinksRequests"`
	RelatedLinksImpressions int     `json:"relatedLinksImpressions"`
	RelatedLinksClicks      int     `json:"relatedLinksClicks"`
	RelatedLinksRpm         float64 `json:"relatedLinksRpm"`
	AdRequests              int     `json:"adRequests"`
	MatchedAdRequests       int     `json:"matchedAdRequests"`
	AdImpressions           int     `json:"adImpressions"`
	Impressions             int     `json:"impressions"`
	Clicks                  int     `json:"clicks"`
	CTR                     float64 `json:"ctr"`
	AdCTR                   float64 `json:"adCtr"`
	AdRPM                   float64 `json:"adRpm"`
	CR                      float64 `json:"cr"`
	Revenue                 string  `json:"revenue"`
	CreateTime              string  `json:"create_time"`
}

// GoogleReportSyncRequest Google报告同步请求
type GoogleReportSyncRequest struct {
	SecurityKey string `form:"security_key" json:"security_key" binding:"required"`
	DataDate    string `form:"data_date" json:"data_date" binding:"required"`
	Limit       int    `form:"limit" json:"limit"`
	Page        int    `form:"page" json:"page"`
}

// GoogleReportSyncResponse Google报告同步响应
type GoogleReportSyncResponse struct {
	Code        int    `json:"code"`
	Msg         string `json:"msg"`
	Time        int64  `json:"time"`
	SyncedCount int    `json:"synced_count"`
	TotalCount  int    `json:"total_count"`
	ErrorCount  int    `json:"error_count"`
}
