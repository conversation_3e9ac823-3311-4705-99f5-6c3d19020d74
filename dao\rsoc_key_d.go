package dao

import (
	"gorm.io/gorm"
	"rsoc-system-go/middleware/pkg/query"
	"rsoc-system-go/model"
	"rsoc-system-go/store"
)

type RsocKeyDao struct {
	db *gorm.DB
}

func NewRoscKeyDao(db *gorm.DB) *RsocKeyDao {
	if db == nil {
		db = store.DB
	}
	return &RsocKeyDao{
		db: db,
	}
}

// Create 创建重放任务
func (d *RsocKeyDao) Create(task *model.RsocKey) error {
	d = NewRoscKeyDao(store.DB)
	return d.db.Save(task).Error
}

// Update 更新重放任务
func (d *RsocKeyDao) Update(task *model.RsocKey) error {
	d = NewRoscKeyDao(store.DB)

	return d.db.Updates(task).Error
}

// Delete 删除重放任务
func (d *RsocKeyDao) Delete(id int) error {
	d = NewRoscKeyDao(store.DB)

	return d.db.Delete(&model.RsocKey{}, id).Error
}

// GetByID 根据ID获取重放任务
func (d *RsocKeyDao) GetByID(id int) (*model.RsocKey, error) {
	d = NewRoscKeyDao(store.DB)

	var task model.RsocKey
	err := d.db.First(&task, id).Error
	if err != nil {
		return nil, err
	}
	return &task, nil
}

// List 获取重放任务列表
func (d *RsocKeyDao) List(task *model.RsocKey, conditions map[string]interface{}) ([]*model.RsocKey, error) {
	d = NewRoscKeyDao(store.DB)

	items, err := query.List[model.RsocKey](d.db, *task, conditions)
	if err != nil {
		return nil, err
	}

	// 转换为指针切片
	tasks := make([]*model.RsocKey, len(items))
	for i := range items {
		tasks[i] = &items[i]
	}
	return tasks, nil
}

// Page 分页获取重放任务列表
func (d *RsocKeyDao) Page(task *model.RsocKey, conditions map[string]interface{}, page, size int) ([]*model.RsocKey, int64, error) {
	d = NewRoscKeyDao(store.DB)
	items, total, err := query.Page[model.RsocKey](d.db, *task, conditions, page, size)
	if err != nil {
		return nil, 0, err
	}

	// 转换为指针切片
	tasks := make([]*model.RsocKey, len(items))
	for i := range items {
		tasks[i] = &items[i]
	}
	return tasks, total, nil
}

// UpdateStatus 更新任务状态
func (d *RsocKeyDao) UpdateStatus(id int, status int) error {
	d = NewRoscKeyDao(store.DB)

	return d.db.Model(&model.RsocKey{}).Where("id = ?", id).Update("status", status).Error
}

// UpdateNumber 更新任务数量
func (d *RsocKeyDao) UpdateNumber(id int, taskNumber int64) error {
	d = NewRoscKeyDao(store.DB)

	return d.db.Model(&model.RsocKey{}).Where("id = ?", id).Update("task_number", taskNumber).Error
}

// FindOne 根据条件查询单条数据记录
func (d *RsocKeyDao) FindOne(conditions map[string]interface{}) (*model.RsocKey, error) {
	var data model.RsocKey
	err := d.db.Where(conditions).First(&data).Error
	if err != nil {
		return nil, err
	}
	return &data, nil
}
