package service

import (
	"context"
	"fmt"
	"os"
	"rsoc-system-go/model"
	"sync"
)

// SedoCampaignService Sedo广告系列服务
type SedoCampaignService struct {
	httpClient *SedoHTTPClient
}

var (
	sedoCampaignService     *SedoCampaignService
	sedoCampaignServiceOnce sync.Once
)

// GetSedoCampaignService 获取Sedo广告系列服务单例
func GetSedoCampaignService() *SedoCampaignService {
	sedoCampaignServiceOnce.Do(func() {
		sedoCampaignService = NewSedoCampaignService(GetSedoHTTPClient())
	})
	// 方法1：使用简化的OAuth初始化
	sedoCampaignService.InitializeWithOAuth(
		os.Getenv("SEDO_CLIENT_ID"),
		os.Getenv("SEDO_CLIENT_SECRET"),
	)
	return sedoCampaignService
}

// NewSedoCampaignService 创建新的Sedo广告系列服务
func NewSedoCampaignService(httpClient *SedoHTTPClient) *SedoCampaignService {
	return &SedoCampaignService{
		httpClient: httpClient,
	}
}

// CampaignReport 获取报表
func (s *SedoCampaignService) CampaignReport(ctx context.Context, page, size int, sort, term string) ([]model.SedoCampaign, *model.SedoPageResponse, error) {
	query := s.httpClient.BuildQueryParams(page, size, sort, term)

	resp, err := s.httpClient.Get(ctx, "/campaign-report", query, nil)
	if err != nil {
		return nil, nil, fmt.Errorf("获取广告系列列表请求失败: %v", err)
	}

	var campaigns []model.SedoCampaign
	if err := s.httpClient.ParseResponse(resp, &campaigns); err != nil {
		return nil, nil, fmt.Errorf("解析广告系列列表响应失败: %v", err)
	}

	totalCount, totalPages, err := s.httpClient.GetPageHeaders(resp)
	if err != nil {
		return nil, nil, fmt.Errorf("获取分页信息失败: %v", err)
	}

	pageResponse := &model.SedoPageResponse{
		TotalCount: totalCount,
		TotalPages: totalPages,
	}

	return campaigns, pageResponse, nil
}

// GetCampaigns 获取广告系列列表
func (s *SedoCampaignService) GetCampaigns(ctx context.Context, page, size int, sort, term string) ([]model.SedoCampaign, *model.SedoPageResponse, error) {
	query := s.httpClient.BuildQueryParams(page, size, sort, term)

	resp, err := s.httpClient.Get(ctx, "/content-campaigns", query, nil)
	if err != nil {
		return nil, nil, fmt.Errorf("获取广告系列列表请求失败: %v", err)
	}

	var campaigns []model.SedoCampaign
	if err := s.httpClient.ParseResponse(resp, &campaigns); err != nil {
		return nil, nil, fmt.Errorf("解析广告系列列表响应失败: %v", err)
	}

	totalCount, totalPages, err := s.httpClient.GetPageHeaders(resp)
	if err != nil {
		return nil, nil, fmt.Errorf("获取分页信息失败: %v", err)
	}

	pageResponse := &model.SedoPageResponse{
		TotalCount: totalCount,
		TotalPages: totalPages,
	}

	return campaigns, pageResponse, nil
}

// GetCampaignByID 根据ID获取广告系列
func (s *SedoCampaignService) GetCampaignByID(ctx context.Context, id string) (*model.SedoCampaign, error) {
	s.httpClient.SetBaseURL("https://api.sedotmp.com/platform/v1")
	resp, err := s.httpClient.Get(ctx, fmt.Sprintf("/content-campaigns/%s", id), nil, nil)
	if err != nil {
		return nil, fmt.Errorf("获取广告系列详情请求失败: %v", err)
	}

	var campaign model.SedoCampaign
	if err := s.httpClient.ParseResponse(resp, &campaign); err != nil {
		return nil, fmt.Errorf("解析广告系列详情响应失败: %v", err)
	}

	return &campaign, nil
}

// CreateCampaign 创建广告系列
func (s *SedoCampaignService) CreateCampaign(ctx context.Context, request *model.SedoCreateCampaignRequest) (*model.SedoCampaign, error) {
	s.httpClient.SetBaseURL("https://api.sedotmp.com/platform/v1")
	resp, err := s.httpClient.Post(ctx, "/content-campaigns", nil, request, nil)
	if err != nil {
		return nil, fmt.Errorf("创建广告系列请求失败: %v", err)
	}

	var campaign model.SedoCampaign
	if err := s.httpClient.ParseResponse(resp, &campaign); err != nil {
		return nil, fmt.Errorf("解析创建广告系列响应失败: %v", err)
	}

	return &campaign, nil
}

// SetBearerToken 设置Bearer令牌
func (s *SedoCampaignService) SetBearerToken(token string) {
	s.httpClient.SetBearerToken(token)
}

// UseOAuth 启用OAuth身份验证
func (s *SedoCampaignService) UseOAuth(enable bool) {
	s.httpClient.UseOAuth(enable)
}

// SetOAuthCredentials 设置OAuth凭据
func (s *SedoCampaignService) SetOAuthCredentials(clientID, clientSecret string) {
	s.httpClient.SetOAuthCredentials(clientID, clientSecret)
}

// SetOAuthConfig 设置OAuth配置
func (s *SedoCampaignService) SetOAuthConfig(tokenEndpoint, clientID, clientSecret, audience, grantType string) {
	oauthClient := GetSedoOAuthClient()
	oauthClient.SetConfig(SedoOAuthConfig{
		TokenEndpoint: tokenEndpoint,
		ClientID:      clientID,
		ClientSecret:  clientSecret,
		Audience:      audience,
		GrantType:     grantType,
		Logger:        oauthClient.logger,
	})
	s.httpClient.UseOAuth(true)
}

// -----------------------------
// 辅助方法
// -----------------------------

// InitializeWithToken 使用令牌初始化服务
func (s *SedoCampaignService) InitializeWithToken(token string) {
	s.SetBearerToken(token)
}

// InitializeWithOAuth 使用OAuth凭据初始化服务
func (s *SedoCampaignService) InitializeWithOAuth(clientID, clientSecret string) {
	s.SetOAuthCredentials(clientID, clientSecret)
}

// InitializeWithFullOAuth 使用完整OAuth配置初始化服务
func (s *SedoCampaignService) InitializeWithFullOAuth(tokenEndpoint, clientID, clientSecret, audience, grantType string) {
	s.SetOAuthConfig(tokenEndpoint, clientID, clientSecret, audience, grantType)
}
