#!/usr/bin/env python3
"""
Postback 回调接口性能测试脚本
使用方法: python3 performance_test.py [base_url] [concurrent_users] [total_requests]
"""

import asyncio
import aiohttp
import sys
import time
import random
from statistics import mean, median

class PerformanceTester:
    def __init__(self, base_url="http://localhost:8080", concurrent_users=10, total_requests=100):
        self.base_url = base_url
        self.concurrent_users = concurrent_users
        self.total_requests = total_requests
        self.results = []
        
    async def make_request(self, session, url, params):
        """发送单个请求"""
        start_time = time.time()
        try:
            async with session.get(url, params=params) as response:
                await response.text()
                end_time = time.time()
                return {
                    'status_code': response.status,
                    'response_time': end_time - start_time,
                    'success': response.status == 200,
                    'error': None
                }
        except Exception as e:
            end_time = time.time()
            return {
                'status_code': 0,
                'response_time': end_time - start_time,
                'success': False,
                'error': str(e)
            }
    
    def generate_test_data(self):
        """生成测试数据"""
        campaigns = [f"PERF{i:03d}" for i in range(1, 1001)]
        payouts = [round(random.uniform(0.1, 5.0), 2) for _ in range(1000)]
        countries = ['US', 'GB', 'DE', 'CA', 'AU', 'FR', 'IT', 'ES', 'NL', 'SE']
        
        test_data = []
        for i in range(self.total_requests):
            # 通用回调接口测试数据
            general_data = {
                'url': f"{self.base_url}/rsoc/postback",
                'params': {
                    'campaign': random.choice(campaigns),
                    'click_id': f"perf_test_{i}",
                    'payout': str(random.choice(payouts)),
                    'country': random.choice(countries)
                }
            }
            
            # SedoTMP回调接口测试数据
            sedotmp_data = {
                'url': f"{self.base_url}/rsoc/sedotmp/callback",
                'params': {
                    'campaign': random.choice(campaigns),
                    'click_id': f"sedo_perf_{i}",
                    'epayout': str(random.choice(payouts)),
                    'country': random.choice(countries),
                    'subid1': f"subid1_{i}",
                    'subid2': f"subid2_{i}"
                }
            }
            
            # 随机选择接口
            test_data.append(random.choice([general_data, sedotmp_data]))
        
        return test_data
    
    async def worker(self, session, test_data_chunk):
        """工作协程"""
        results = []
        for test_data in test_data_chunk:
            result = await self.make_request(session, test_data['url'], test_data['params'])
            results.append(result)
        return results
    
    async def run_performance_test(self):
        """运行性能测试"""
        print("=== Postback 回调接口性能测试 ===")
        print(f"基础URL: {self.base_url}")
        print(f"并发用户数: {self.concurrent_users}")
        print(f"总请求数: {self.total_requests}")
        print(f"开始时间: {time.strftime('%Y-%m-%d %H:%M:%S')}")
        print("")
        
        # 生成测试数据
        test_data = self.generate_test_data()
        
        # 将测试数据分配给不同的工作协程
        chunk_size = self.total_requests // self.concurrent_users
        chunks = [test_data[i:i + chunk_size] for i in range(0, len(test_data), chunk_size)]
        
        # 记录开始时间
        start_time = time.time()
        
        # 创建HTTP会话并运行测试
        async with aiohttp.ClientSession() as session:
            tasks = [self.worker(session, chunk) for chunk in chunks]
            results = await asyncio.gather(*tasks)
        
        # 记录结束时间
        end_time = time.time()
        total_time = end_time - start_time
        
        # 合并所有结果
        all_results = []
        for result_chunk in results:
            all_results.extend(result_chunk)
        
        self.results = all_results
        
        # 生成性能报告
        self.generate_performance_report(total_time)
    
    def generate_performance_report(self, total_time):
        """生成性能测试报告"""
        print("=== 性能测试报告 ===")
        print("")
        
        # 基本统计
        total_requests = len(self.results)
        successful_requests = len([r for r in self.results if r['success']])
        failed_requests = total_requests - successful_requests
        
        print(f"总请求数: {total_requests}")
        print(f"成功请求数: {successful_requests}")
        print(f"失败请求数: {failed_requests}")
        print(f"成功率: {(successful_requests/total_requests*100):.2f}%")
        print(f"总耗时: {total_time:.2f} 秒")
        print(f"QPS (每秒请求数): {total_requests/total_time:.2f}")
        print("")
        
        # 响应时间统计
        response_times = [r['response_time'] for r in self.results if r['success']]
        if response_times:
            print("响应时间统计 (成功请求):")
            print(f"平均响应时间: {mean(response_times)*1000:.2f} ms")
            print(f"中位数响应时间: {median(response_times)*1000:.2f} ms")
            print(f"最小响应时间: {min(response_times)*1000:.2f} ms")
            print(f"最大响应时间: {max(response_times)*1000:.2f} ms")
            
            # 响应时间分布
            response_times_ms = [rt * 1000 for rt in response_times]
            percentiles = [50, 75, 90, 95, 99]
            print("")
            print("响应时间百分位数:")
            for p in percentiles:
                index = int(len(response_times_ms) * p / 100) - 1
                if index >= 0:
                    sorted_times = sorted(response_times_ms)
                    print(f"P{p}: {sorted_times[index]:.2f} ms")
        
        print("")
        
        # 错误统计
        if failed_requests > 0:
            print("错误统计:")
            error_counts = {}
            for result in self.results:
                if not result['success']:
                    error = result.get('error', f"HTTP {result['status_code']}")
                    error_counts[error] = error_counts.get(error, 0) + 1
            
            for error, count in error_counts.items():
                print(f"- {error}: {count} 次")
        
        print("")
        print(f"结束时间: {time.strftime('%Y-%m-%d %H:%M:%S')}")
    
    def run(self):
        """运行测试"""
        asyncio.run(self.run_performance_test())

def main():
    """主函数"""
    base_url = sys.argv[1] if len(sys.argv) > 1 else "http://localhost:8080"
    concurrent_users = int(sys.argv[2]) if len(sys.argv) > 2 else 10
    total_requests = int(sys.argv[3]) if len(sys.argv) > 3 else 100
    
    tester = PerformanceTester(base_url, concurrent_users, total_requests)
    tester.run()

if __name__ == "__main__":
    main()
