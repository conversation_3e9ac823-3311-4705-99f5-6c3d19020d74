package service

import (
	"fmt"
)

// SedoContentServiceGuide Sedo内容服务使用指南
func SedoContentServiceGuide() string {
	guide := `
# Sedo内容服务使用指南

## 1. 服务概述

Sedo内容服务是与Sedo内容管理API交互的Go语言客户端。它提供以下功能：

- 文章管理：创建、查询、更新、删除和发布文章
- 分类管理：查询和创建分类
- 域名管理：查询域名信息
- 媒体资源管理：上传、下载和查询媒体文件
- OAuth身份验证：支持使用客户端凭据进行身份验证

## 2. 安装与配置

### 2.1 环境变量

服务支持以下环境变量：

| 环境变量                | 描述                        | 默认值                              |
|------------------------|----------------------------|-------------------------------------|
| SEDO_API_BASE_URL      | API基础URL                 | https://api.sedotmp.com/content/v1 |
| SEDO_API_TOKEN         | Bearer令牌（直接认证方式）   | 无                                  |
| SEDO_API_TIMEOUT       | API请求超时时间（秒）        | 30                                  |
| SEDO_USE_OAUTH         | 是否使用OAuth认证           | false                               |
| SEDO_TOKEN_ENDPOINT    | OAuth令牌端点               | https://auth.sedotmp.com/oauth/token |
| SEDO_CLIENT_ID         | OAuth客户端ID              | 无                                  |
| SEDO_CLIENT_SECRET     | OAuth客户端密钥             | 无                                  |
| SEDO_AUDIENCE          | OAuth目标受众               | https://api.sedotmp.com            |
| SEDO_GRANT_TYPE        | OAuth授权类型               | client_credentials                  |

### 2.2 初始化服务

有三种方式初始化服务：

1. **使用Bearer令牌认证**:
   ` + "```go" + `
   service := GetSedoContentService()
   service.SetBearerToken("your-api-token")
   ` + "```" + `

2. **使用简化的OAuth认证**:
   ` + "```go" + `
   service := GetSedoContentService()
   service.InitializeWithOAuth("your-client-id", "your-client-secret")
   ` + "```" + `

3. **使用完整的OAuth配置**:
   ` + "```go" + `
   service := GetSedoContentService()
   service.InitializeWithFullOAuth(
       "https://auth.sedotmp.com/oauth/token",
       "your-client-id",
       "your-client-secret",
       "https://api.sedotmp.com",
       "client_credentials"
   )
   ` + "```" + `

## 3. 使用示例

### 3.1 检查API连接

` + "```go" + `
ctx := context.Background()
if err := service.CheckConnection(ctx); err != nil {
    log.Fatalf("连接失败: %v", err)
}
log.Println("连接成功!")
` + "```" + `

### 3.2 获取分类列表

` + "```go" + `
categories, pageInfo, err := service.GetCategories(ctx, 0, 10, "")
if err != nil {
    log.Fatalf("获取分类失败: %v", err)
}
for i, category := range categories {
    log.Printf("%d. ID: %s, 标题: %s", i+1, category.ID, category.Title)
}
` + "```" + `

### 3.3 创建文章

` + "```go" + `
createReq := &model.SedoCreateArticleRequest{
    Title:      "文章标题",
    Excerpt:    "文章摘要",
    Text:       "<p>文章内容</p>",
    CategoryID: "category-id",
    Tags:       []string{"标签1", "标签2"},
    Country:    "CN",
    Locale:     "zh-CN",
}

article, err := service.CreateArticle(ctx, createReq)
if err != nil {
    log.Fatalf("创建文章失败: %v", err)
}
log.Printf("文章已创建: ID=%s", article.ID)
` + "```" + `

### 3.4 更新文章

` + "```go" + `
updateReq := &model.SedoUpdateArticleRequest{
    Title:   "更新后的标题",
    Excerpt: "更新后的摘要",
    Text:    "<p>更新后的内容</p>",
}

updatedArticle, err := service.UpdateArticle(ctx, articleID, updateReq)
if err != nil {
    log.Fatalf("更新文章失败: %v", err)
}
log.Printf("文章已更新: ID=%s", updatedArticle.ID)
` + "```" + `

### 3.5 生成文章

` + "```go" + `
generateReq := &model.SedoGenerateArticleRequest{
    Topics:     []string{"主题1", "主题2"},
    CategoryID: "category-id",
    Country:    "CN",
    Locale:     "zh-CN",
    GenerateImage: &struct {
        Enabled     bool   ` + "`json:\"enabled\"`" + `
        Description string ` + "`json:\"description\"`" + `
    }{
        Enabled:     true,
        Description: "图片描述",
    },
}

// 同步方式生成文章
article, err := service.GenerateArticle(ctx, generateReq, false, "")
if err != nil {
    log.Fatalf("生成文章失败: %v", err)
}
log.Printf("文章已生成: ID=%s", article.ID)

// 异步方式生成文章
article, err := service.GenerateArticle(ctx, generateReq, true, "reference-id")
if err != nil {
    log.Fatalf("请求生成文章失败: %v", err)
}
log.Printf("文章生成请求已提交: ID=%s", article.ID)
` + "```" + `

### 3.6 上传媒体文件

` + "```go" + `
filePath := "./image.jpg"
media, err := service.UploadMediaFile(ctx, filePath)
if err != nil {
    log.Fatalf("上传文件失败: %v", err)
}
log.Printf("文件已上传: ID=%s, URL=%s", media.ID, media.URL)
` + "```" + `

### 3.7 下载媒体文件

` + "```go" + `
mediaID := "media-id"
destPath := "./downloaded-file.jpg"
err := service.DownloadMediaFile(ctx, mediaID, destPath)
if err != nil {
    log.Fatalf("下载文件失败: %v", err)
}
log.Printf("文件已下载到: %s", destPath)
` + "```" + `

## 4. OAuth认证示例

### 4.1 直接使用OAuth客户端

` + "```go" + `
// 获取OAuth客户端
oauthClient := GetSedoOAuthClient()

// 配置OAuth
oauthClient.SetConfig(SedoOAuthConfig{
    TokenEndpoint: "https://auth.sedotmp.com/oauth/token",
    ClientID:      "your-client-id",
    ClientSecret:  "your-client-secret",
    Audience:      "https://api.sedotmp.com",
    GrantType:     "client_credentials",
})

// 获取访问令牌
token, err := oauthClient.GetToken(ctx)
if err != nil {
    log.Fatalf("获取访问令牌失败: %v", err)
}
log.Printf("访问令牌: %s", token[:10]+"...")

// 清除令牌缓存
oauthClient.ClearToken()
` + "```" + `

### 4.2 通过HTTP客户端使用OAuth

` + "```go" + `
// 获取HTTP客户端
httpClient := GetSedoHTTPClient()

// 配置客户端使用OAuth
httpClient.UseOAuth(true)
httpClient.SetOAuthCredentials("your-client-id", "your-client-secret")

// 发送请求
response, err := httpClient.Get(ctx, "/categories", nil, nil)
` + "```" + `

## 5. 完整示例

查看以下文件以获取完整的使用示例：

1. service/sedo_content_service_example.go - 内容服务使用示例
2. service/sedo_oauth_example.go - OAuth认证使用示例

## 6. 数据模型

详细的数据模型定义请查看 model/sedo_content.go 文件。
`

	return guide
}

// PrintSedoContentServiceGuide 打印Sedo内容服务使用指南
func PrintSedoContentServiceGuide() {
	fmt.Println(SedoContentServiceGuide())
}
