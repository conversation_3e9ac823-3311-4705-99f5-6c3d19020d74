import{i as _}from"./index-u3wYLQ4o.js";import{_ as f,u as m,r as u,o as g,D as x,M as y,c as i,b as v,k as b,C as w,e as A,f as r}from"./index-DlVegDiC.js";const B={class:"main"},k={class:"echarts-box"},C=["id"],E={__name:"echarts",setup(I){const l="",d=m(),s=u({title:["0-5秒","5-10秒","10-15秒","15-20秒","20-25秒","25-30秒","30-35秒","35-40秒","40-45秒","45-50秒","50-55秒","55-60秒","60-65秒","65秒以上"],list:{}});g(()=>{h()});const h=()=>{fetch(`${l}/sedo/getChats`,{method:"POST",headers:{token:d.token}}).then(e=>e.json()).then(async e=>{console.log(e);const n={};for(const t in e.data){const o=e.data[t],a=new Array(s.title.length).fill(0);s.title.forEach((c,p)=>{o[c]!==void 0&&(a[p]=o[c])}),n[t]=a}s.list=n,await x(),Object.keys(s.list).forEach(t=>{console.log(`${t}: ${s.list[t]}`);let o=_(document.getElementById(t)),a;a={title:{text:"campaign ID: "+t,left:"center"},tooltip:{trigger:"axis"},xAxis:{type:"category",boundaryGap:!1,data:["0-5秒","5-10秒","10-15秒","15-20秒","20-25秒","25-30秒","30-35秒","35-40秒","40-45秒","45-50秒","50-55秒","55-60秒","60-65秒","65秒以上"]},yAxis:{type:"value"},series:[{data:s.list[t],type:"line",areaStyle:{}}]},a&&o.setOption(a),window.onresize=function(){o.resize()}})}).catch(e=>{y.error({title:"服务器错误",content:`${e}`})})};return(e,n)=>(r(),i("div",B,[v("div",k,[(r(!0),i(b,null,w(A(s).list,(t,o)=>(r(),i("div",{id:o,style:{width:"100%",height:"300px",marginBottom:"30px"}},null,8,C))),256))])]))}},M=f(E,[["__scopeId","data-v-eb8cf166"]]);export{M as default};
