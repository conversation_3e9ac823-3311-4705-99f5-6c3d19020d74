package handler

import (
	"context"
	"database/sql"
	"fmt"
	"log"
	"net/http"
	"os"
	"rsoc-system-go/config"
	"rsoc-system-go/dao"
	"rsoc-system-go/middleware/pkg/redis"
	"rsoc-system-go/middleware/service"
	"rsoc-system-go/model"
	"rsoc-system-go/store"
	"strconv"
	"time"

	"github.com/gin-gonic/gin"
)

type ReplayTaskHandler struct {
	taskDao *dao.ReplayTaskDao
}

func NewReplayTaskHandler(taskDao *dao.ReplayTaskDao) *ReplayTaskHandler {
	return &ReplayTaskHandler{
		taskDao: taskDao,
	}
}

// Create 创建重放任务
func (h *ReplayTaskHandler) Create(c *gin.Context) {
	var task model.ReplayTask
	if err := c.ShouldBindJSON(&task); err != nil {
		c.JSON(http.StatusOK, config.Result{http.StatusBadRequest, err.Error(), nil})
		return
	}
	/*redisManager := store.GetRedisManager()
	err := redisManager.ConnectRedis("task")
	client := redisManager.GetRedisClient("task")*/
	indexdbStr := os.Getenv("redisdb")
	indexdb, _ := strconv.Atoi(indexdbStr)
	client := redis.SelectDB(indexdb)
	//client := redis.SelectDB(1)
	_, err := client.HSet(context.Background(), "task_replay", task.TaskID).Result()
	if err != nil {
		c.JSON(http.StatusOK, config.Result{http.StatusInternalServerError, err.Error(), nil})
		return
	}
	task.CreateTime = sql.NullTime{Time: time.Now(), Valid: true}
	task.UpdateTime = sql.NullTime{Time: time.Now(), Valid: true}
	task.CreateBy = c.GetString("userId") // 假设通过中间件设置了用户名

	if err := h.taskDao.Create(&task); err != nil {
		c.JSON(http.StatusOK, config.Result{http.StatusInternalServerError, err.Error(), nil})
		return
	}

	c.JSON(http.StatusOK, task)
}

// Update 更新重放任务
func (h *ReplayTaskHandler) Update(c *gin.Context) {
	var task model.ReplayTask
	if err := c.ShouldBindJSON(&task); err != nil {
		c.JSON(http.StatusOK, config.Result{http.StatusBadRequest, err.Error(), nil})
		return
	}

	task.UpdateTime = sql.NullTime{Time: time.Now(), Valid: true}
	task.UpdateBy = c.GetString("username")

	if err := h.taskDao.Update(&task); err != nil {
		c.JSON(http.StatusOK, config.Result{http.StatusInternalServerError, err.Error(), nil})
		return
	}

	c.JSON(http.StatusOK, config.Result{http.StatusOK, "success", task})
}

// Delete 删除重放任题 同时删除已生成任务 删除本地redis数据
func (h *ReplayTaskHandler) Delete(c *gin.Context) {
	param := make(map[string]interface{})
	if err := c.ShouldBindJSON(&param); err != nil {
		c.JSON(http.StatusOK, config.Result{http.StatusBadRequest, err.Error(), nil})
		return
	}

	// 检查id参数是否存在
	if param["id"] == nil {
		c.JSON(http.StatusOK, config.Result{http.StatusBadRequest, "id参数不能为空", nil})
		return
	}

	// 将interface{}类型的id转换为float64,再转为int
	taskIDFloat, ok := param["id"].(float64)
	if !ok {
		c.JSON(http.StatusOK, config.Result{http.StatusBadRequest, "id参数类型错误", nil})
		return
	}
	taskID := int(taskIDFloat)
	taskData, err := h.taskDao.GetByID(taskID)

	// 删除Redis中的任务记录
	/*redisManager := store.GetRedisManager()
	err = redisManager.ConnectRedis("task")
	client := redisManager.GetRedisClient("task")*/
	indexdbStr := os.Getenv("redisdb")
	indexdb, _ := strconv.Atoi(indexdbStr)
	client := redis.SelectDB(indexdb)
	//client := redis.SelectDB(1)
	// 获取当前时间
	_, err = client.Del(context.Background(), "task:"+taskData.TaskID).Result()
	if err != nil {
		log.Println("err", err)
	}
	if err != nil {
		c.JSON(http.StatusOK, config.Result{http.StatusInternalServerError, err.Error(), nil})
		return
	}

	// 删除数据库中的任务记录
	if err := h.taskDao.Delete(taskID); err != nil {
		c.JSON(http.StatusOK, config.Result{http.StatusInternalServerError, err.Error(), nil})
		return
	}

	c.JSON(http.StatusOK, config.Result{1, "success", nil})
}

// Get 获取重放任务
func (h *ReplayTaskHandler) Get(c *gin.Context) {
	id := c.Query("id")
	var taskID int
	if _, err := fmt.Sscanf(id, "%d", &taskID); err != nil {
		c.JSON(http.StatusOK, config.Result{http.StatusBadRequest, "invalid id", nil})
		return
	}

	task, err := h.taskDao.GetByID(taskID)
	if err != nil {
		c.JSON(http.StatusOK, config.Result{http.StatusInternalServerError, err.Error(), nil})
		return
	}

	c.JSON(http.StatusOK, config.Result{http.StatusOK, "success", task})
}

type PageReq struct {
	Page int `json:"page"`
	Size int `json:"size"`
}

// List 获取重放任务列表
func (h *ReplayTaskHandler) List(c *gin.Context) {
	var pagereq PageReq
	pagereq.Page = 1
	pagereq.Size = 10
	if err := c.ShouldBindJSON(&pagereq); err != nil {
		c.JSON(http.StatusOK, config.Result{http.StatusBadRequest, err.Error(), nil})
	}
	/*if pageStr := c.Query("page"); pageStr != "" {
		if p, err := strconv.Atoi(pageStr); err == nil && p > 0 {
			page = p
		}
	}

	if sizeStr := c.Query("limit"); sizeStr != "" {
		if s, err := strconv.Atoi(sizeStr); err == nil && s > 0 {
			size = s
		}
	}*/
	var task model.ReplayTask
	/*if err := c.ShouldBindJSON(&task); err != nil {
		c.JSON(http.StatusOK, config.Result{http.StatusBadRequest, err.Error(), nil})
		return
	}*/

	userId, _ := c.Get("userId")
	task.CreateBy = strconv.Itoa(userId.(int))
	param := map[string]interface{}{
		"create_by": map[string]interface{}{
			"eq": task.CreateBy,
		},
	}
	tasks, total, err := h.taskDao.Page(&task, param, pagereq.Page, pagereq.Size)
	if err != nil {
		c.JSON(http.StatusOK, config.Result{http.StatusInternalServerError, err.Error(), nil})
		return
	}
	c.JSON(http.StatusOK, config.Result{1, "success", gin.H{
		"data":  tasks,
		"total": total,
		"page":  pagereq.Page,
		"size":  pagereq.Size,
	}})

}

// UpdateStatus 更新任务状态
func (h *ReplayTaskHandler) UpdateStatus(c *gin.Context) {

	var req struct {
		Status int `json:"status"`
		Id     int `json:"id"`
	}
	if err := c.ShouldBindJSON(&req); err != nil {
		c.JSON(http.StatusBadRequest, gin.H{"error": err.Error()})
		return
	}
	if err := h.taskDao.UpdateStatus(req.Id, req.Status); err != nil {
		c.JSON(http.StatusInternalServerError, gin.H{"error": err.Error()})
		return
	}
	if req.Status == 2 {
		replayTask, err := h.taskDao.GetByID(req.Id)
		// 删除 autoTask 表 taskid数据
		taskDao := dao.NewAutoTaskDao(store.DB)
		err = taskDao.DeleteByTaskID(replayTask.TaskID)
		if err != nil {
			log.Println("err", err)
		}
		// 删除 autoTaskAll 表 taskid数据
		// 连接127.0.0.1:6379 删除 redis 数据
		// 1. 获取Redis管理器
		redisManager := store.GetRedisManager()

		/*	// 2. 注册新的Redis实例
			err = redisManager.RegisterRedis(store.RedisConfig{
				Instance: "cache",
				URL:      "redis://127.0.0.1:6379/0",
				PoolSize: 10,
			})

			if err != nil {
				log.Println("err", err)
			}*/
		// 3. 连接到Redis实例
		err = redisManager.ConnectRedis("local")
		if err != nil {
			log.Println("err", err)
		}

		// 4. 获取Redis客户端
		client := redisManager.GetRedisClient("local")

		key := time.Now().Format(time.DateOnly)
		val, err := client.Del(context.Background(), key).Result()
		log.Println(val)
		if err != nil {
			log.Println("err", err)
		}
	}

	c.JSON(http.StatusOK, config.Result{1, "success", nil})
}

// Replay  生成重放数据
func (h *ReplayTaskHandler) Replay(c *gin.Context) {
	var task model.ReplayTask
	if err := c.ShouldBindJSON(&task); err != nil {
		c.JSON(http.StatusOK, gin.H{"error": err.Error()})
		return
	}
	// 执行测试
	s := &service.RePlayService{}
	rePlayTaskDao := dao.NewReplayTaskDao(store.DB)
	task.TaskCreateDate = time.Now().Format("2006-01-02")
	task.Status = 1
	//task.Flag = 2
	userId, _ := c.Get("userId")
	task.CreateBy = strconv.Itoa(userId.(int))
	err := rePlayTaskDao.Create(&task)
	if err != nil {
		c.JSON(http.StatusOK, gin.H{"error": err.Error()})
		return
	}
	go func() {
		err := s.RePlayDataFormatByCreate(&task)
		if err != nil {
			task.Status = 2
			task.Note = err.Error()
			err := rePlayTaskDao.Update(&task)
			if err != nil {
				log.Println("err", err)
			}
		}
	}()
	/*if err != nil {
		c.JSON(http.StatusInternalServerError, gin.H{"error": err.Error()})
		return
	}*/
	c.JSON(http.StatusOK, config.Result{1, "success", nil})

}

// UpdateNumber 更新任务数量
func (h *ReplayTaskHandler) UpdateNumber(c *gin.Context) {
	var task model.ReplayTask
	if err := c.ShouldBindJSON(&task); err != nil {
		c.JSON(http.StatusOK, gin.H{"error": err.Error()})
		return
	}
	// 执行测试
	s := &service.RePlayService{}
	rePlayTaskDao := dao.NewReplayTaskDao(store.DB)
	taskData, err := rePlayTaskDao.GetByID(task.ID)
	taskData.TaskNumber = task.TaskNumber
	err = rePlayTaskDao.UpdateNumber(task.ID, task.TaskNumber)
	err = rePlayTaskDao.UpdateStatus(task.ID, 1)
	if err != nil {
		c.JSON(http.StatusOK, gin.H{"error": err.Error()})
		return
	}
	// 删除当前重放任务在redis中的重放曲线数据
	//client := redis.SelectDB(1)
	/*redisManager := store.GetRedisManager()
	err = redisManager.ConnectRedis("task")
	client := redisManager.GetRedisClient("task")*/
	indexdbStr := os.Getenv("redisdb")
	indexdb, _ := strconv.Atoi(indexdbStr)
	client := redis.SelectDB(indexdb)
	// 获取当前时间
	_, err = client.Del(context.Background(), "task:"+taskData.TaskID).Result()
	if err != nil {
		log.Println("err", err)
	}

	go func() {
		err := s.RePlayDataFormatByCreate(taskData)
		if err != nil {
			task.Note = err.Error()
			err := rePlayTaskDao.Update(&task)
			if err != nil {
				log.Println("err", err)
			}
		}
	}()
	/*if err != nil {
		c.JSON(http.StatusInternalServerError, gin.H{"error": err.Error()})
		return
	}*/
	c.JSON(http.StatusOK, config.Result{1, "success", nil})

}

// UpdateProxy 更新代理
func (h *ReplayTaskHandler) UpdateProxy(c *gin.Context) {
	var task model.ReplayTask
	if err := c.ShouldBindJSON(&task); err != nil {
		c.JSON(http.StatusOK, gin.H{"error": err.Error()})
		return
	}
	// 执行测试
	rePlayTaskDao := dao.NewReplayTaskDao(store.DB)
	taskData, err := rePlayTaskDao.GetByID(task.ID)
	taskData.ProxySetId = task.ProxySetId
	err = rePlayTaskDao.Update(taskData)
	if err != nil {
		c.JSON(http.StatusOK, gin.H{"error": err.Error()})
		return
	}

	c.JSON(http.StatusOK, config.Result{1, "success", nil})

}

func (h *ReplayTaskHandler) ReplayPng(c *gin.Context) {
	oldDao := dao.NewAutoTaskAllDataOldDAO(store.DB)
	oldDate, err := oldDao.GetAutoTaskAllDataOldDate()
	s := service.RePlayService{}
	for _, itemDate := range oldDate {
		err := s.SyncTaskLine2Redis(itemDate)
		if err != nil {
			log.Println("err", err)
		}
	}
	if err != nil {
		log.Println("allErr", err)
	}

}

// RePlayLine 获取历史重放曲线数据
func (h *ReplayTaskHandler) RePlayLine(c *gin.Context) {
	date := c.Query("date")
	/*redisManager := store.GetRedisManager()
	err := redisManager.ConnectRedis("task")
	client := redisManager.GetRedisClient("task")*/
	indexdbStr := os.Getenv("redisdb")
	indexdb, _ := strconv.Atoi(indexdbStr)
	client := redis.SelectDB(indexdb)
	//client := redis.SelectDB(1)
	result, err := client.HGet(context.Background(), "task_line", date).Result()
	if err != nil {
		c.JSON(http.StatusOK, gin.H{"error": err.Error()})
		return
	}
	c.JSON(http.StatusOK, gin.H{"data": result})

}

// RePlayTaskLine 获取当前重放曲线数据
func (h *ReplayTaskHandler) RePlayTaskLine(c *gin.Context) {
	date := c.Query("date")
	client := store.RedisClient
	result, err := client.HGet(context.Background(), "task", date).Result()
	if err != nil {
		c.JSON(http.StatusOK, gin.H{"error": err.Error()})
		return
	}
	c.JSON(http.StatusOK, gin.H{"data": result})

}

// IntTaskLine 初始化历史曲线
func (h *ReplayTaskHandler) IntTaskLine(c *gin.Context) {
	var task model.ReplayTask
	if err := c.ShouldBindJSON(&task); err != nil {
		c.JSON(http.StatusOK, gin.H{"error": err.Error()})
		return
	}
	if task.TaskDate == "" {
		//oldDao := dao.NewAutoTaskAllDataOldDAO(store.DB)
		oldDao := dao.NewAutoTaskAllDataOld("old")
		oldDate, err := oldDao.GetAutoTaskAllDataOldDate()
		s := service.RePlayService{}
		for _, itemDate := range oldDate {
			err := s.SyncTaskLine2Redis(itemDate)
			if err != nil {
				log.Println("err", err)
			}
		}
		if err != nil {
			log.Println("allErr", err)
		}
	} else {
		s := service.RePlayService{}
		err := s.SyncTaskLine2Redis(task.TaskDate)
		if err != nil {
			log.Println("allErr", err)
		}
	}

}
