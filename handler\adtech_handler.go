package handler

import (
	"net/http"
	"rsoc-system-go/service"

	"github.com/gin-gonic/gin"
)

// AdTechHandler AdTech处理器
type AdTechHandler struct {
	campaignService *service.AdTechCampaignService
}

// NewAdTechHandler 创建新的AdTech处理器
func NewAdTechHandler(campaignService *service.AdTechCampaignService) *AdTechHandler {
	return &AdTechHandler{
		campaignService: campaignService,
	}
}

// GetCampaign godoc
// @Summary 获取可用于发布文章列表
// @Description 获取可用于发布文章列表
// @Tags AdTech
// @Accept json
// @Produce json
// @Success 200 {object} gin.H{data=[]model.AdTechDomain} "成功"
// @Failure 500 {object} gin.H{error=string} "服务器错误"
// @Router /adtech/campaign [get]
func (h *AdTechHandler) GetCampaign(c *gin.Context) {
	value := c.Query("key")
	id := c.Query("id")
	campaign, err := h.campaignService.GetCampaign(c.Request.Context(), value, id)
	if err != nil {
		c.JSON(http.StatusInternalServerError, gin.H{"error": err.Error()})
		return
	}

	c.JSON(http.StatusOK, gin.H{"data": campaign})
}

// GetDomains godoc
// @Summary 获取可用于发布文章的域名列表
// @Description 获取可用于发布文章的域名列表
// @Tags AdTech
// @Accept json
// @Produce json
// @Success 200 {object} gin.H{data=[]model.AdTechDomain} "成功"
// @Failure 500 {object} gin.H{error=string} "服务器错误"
// @Router /adtech/domains [get]
func (h *AdTechHandler) GetDomains(c *gin.Context) {
	value := c.Query("key")
	domains, err := h.campaignService.GetDomains(c.Request.Context(), value)
	if err != nil {
		c.JSON(http.StatusInternalServerError, gin.H{"error": err.Error()})
		return
	}

	c.JSON(http.StatusOK, gin.H{"data": domains})
}
