package tools

import (
	"context"
	"encoding/json"
	"fmt"
	"os"
	"path/filepath"
	"strings"
	"sync"
	"time"

	"github.com/go-redis/redis/v8"
)

// ProcessPosition 记录处理位置
type ProcessPosition struct {
	DirIndex  int
	UaDirKey  int
	FileIndex int
}

// JsonToRedis 结构体用于处理JSON文件到Redis的导入
type JsonToRedis struct {
	redisClient *redis.Client
	ctx         context.Context
	position    ProcessPosition
}

// NewJsonToRedis 创建一个新的JsonToRedis实例
func NewJsonToRedis(redisAddr, redisPassword string, redisDB int) (*JsonToRedis, error) {
	client := redis.NewClient(&redis.Options{
		Addr:         redisAddr,
		Password:     redisPassword,
		DB:           redisDB,
		PoolSize:     10,              // 设置连接池大小
		MinIdleConns: 5,               // 最小空闲连接数
		MaxRetries:   3,               // 最大重试次数
		ReadTimeout:  5 * time.Second, // 读取超时
		WriteTimeout: 5 * time.Second, // 写入超时
	})

	ctx := context.Background()

	// 测试Redis连接
	if err := client.Ping(ctx).Err(); err != nil {
		return nil, fmt.Errorf("无法连接到Redis: %v", err)
	}

	return &JsonToRedis{
		redisClient: client,
		ctx:         ctx,
		position:    ProcessPosition{},
	}, nil
}

// LoadPosition 加载处理位置
func (j *JsonToRedis) LoadPosition(pos ProcessPosition) {
	j.position = pos
}

// GetCurrentPosition 获取当前处理位置
func (j *JsonToRedis) GetCurrentPosition() ProcessPosition {
	return j.position
}

// ImportJsonFiles 从多个目录导入JSON文件到Redis
func (j *JsonToRedis) ImportJsonFiles(directories []string, keyPrefix string) error {
	// 创建错误通道
	errChan := make(chan error, 1)
	// 创建等待组
	var wg sync.WaitGroup

	// 创建工作池限制通道
	workerLimit := make(chan struct{}, 10) // 限制并发goroutine数量

	// 从上次的位置继续处理
	for dirIndex := j.position.DirIndex; dirIndex < len(directories); dirIndex++ {
		dir := directories[dirIndex]
		// 获取ua目录列表
		uaDirs, err := os.ReadDir(dir)
		if err != nil {
			return fmt.Errorf("读取目录 %s 失败: %v", dir, err)
		}

		// 设置起始的ua目录索引
		startUaDirKey := 1
		if dirIndex == j.position.DirIndex && j.position.UaDirKey > 0 {
			startUaDirKey = j.position.UaDirKey
		}

		// 遍历每个ua目录
		for indexDirKey := startUaDirKey; indexDirKey <= len(uaDirs); indexDirKey++ {
			uaDir := uaDirs[indexDirKey-1]
			if !uaDir.IsDir() || !strings.HasPrefix(uaDir.Name(), "ua") {
				continue
			}

			uaPath := filepath.Join(dir, uaDir.Name())
			files, err := os.ReadDir(uaPath)
			if err != nil {
				return fmt.Errorf("读取UA目录 %s 失败: %v", uaPath, err)
			}

			// 设置起始的文件索引
			startFileIndex := 1
			if dirIndex == j.position.DirIndex && indexDirKey == j.position.UaDirKey && j.position.FileIndex > 0 {
				startFileIndex = j.position.FileIndex
			}

			// 遍历目录下的所有JSON文件
			for indexKey := startFileIndex; indexKey <= len(files); indexKey++ {
				file := files[indexKey-1]
				if file.IsDir() || filepath.Ext(file.Name()) != ".json" {
					continue
				}

				filePath := filepath.Join(uaPath, file.Name())

				// 更新当前处理位置
				j.position = ProcessPosition{
					DirIndex:  dirIndex,
					UaDirKey:  indexDirKey,
					FileIndex: indexKey,
				}

				// 为每个文件启动一个goroutine
				wg.Add(1)
				go func(filePath string, indexDirKey, indexKey int) {
					// 获取工作池令牌
					workerLimit <- struct{}{}
					defer func() {
						<-workerLimit // 释放令牌
						wg.Done()
					}()

					// 读取JSON文件
					data, err := os.ReadFile(filePath)
					if err != nil {
						select {
						case errChan <- fmt.Errorf("读取文件 %s 失败: %v", filePath, err):
						default:
						}
						return
					}

					// 解析JSON数据
					var jsonData interface{}
					if err := json.Unmarshal(data, &jsonData); err != nil {
						select {
						case errChan <- fmt.Errorf("解析JSON文件 %s 失败: %v", filePath, err):
						default:
						}
						return
					}

					// 生成Redis键名
					redisKey := fmt.Sprintf("%s%d:%d", "ua", indexDirKey, indexKey)

					// 将数据存储到Redis
					jsonStr, err := json.Marshal(jsonData)
					if err != nil {
						select {
						case errChan <- fmt.Errorf("序列化JSON数据失败 %s: %v", filePath, err):
						default:
						}
						return
					}

					// 添加超时控制
					ctx, cancel := context.WithTimeout(j.ctx, 5*time.Second)
					defer cancel()

					// 尝试写入Redis，如果失败则重试
					for retries := 0; retries < 3; retries++ {
						err = j.redisClient.HSet(ctx, redisKey, indexKey, jsonStr).Err()
						if err == nil {
							break
						}
						time.Sleep(time.Second * time.Duration(retries+1))
					}

					if err != nil {
						select {
						case errChan <- fmt.Errorf("写入Redis失败 %s: %v", redisKey, err):
						default:
						}
						return
					}
				}(filePath, indexDirKey, indexKey)
			}
		}
	}

	// 等待所有goroutine完成
	done := make(chan struct{})
	go func() {
		wg.Wait()
		close(done)
		close(errChan)
	}()

	// 等待完成或出错
	select {
	case err := <-errChan:
		if err != nil {
			// 返回错误和当前位置
			return fmt.Errorf("处理失败于位置 %+v: %v", j.position, err)
		}
	case <-done:
		// 处理完成，重置位置
		j.position = ProcessPosition{}
		return nil
	}

	return nil
}

// Close 关闭Redis连接
func (j *JsonToRedis) Close() error {
	return j.redisClient.Close()
}
