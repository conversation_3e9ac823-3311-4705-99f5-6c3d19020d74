package dao

import (
	"gorm.io/gorm"
	"rsoc-system-go/middleware/pkg/query"
	"rsoc-system-go/model"
	"rsoc-system-go/store"
)

type ReplayTaskDao struct {
	db *gorm.DB
}

func NewReplayTaskDao(db *gorm.DB) *ReplayTaskDao {
	if db == nil {
		db = store.DB
	}
	return &ReplayTaskDao{
		db: db,
	}
}

// Create 创建重放任务
func (d *ReplayTaskDao) Create(task *model.ReplayTask) error {
	d = NewReplayTaskDao(store.DB)
	return d.db.Save(task).Error
}

// Update 更新重放任务
func (d *ReplayTaskDao) Update(task *model.ReplayTask) error {
	d = NewReplayTaskDao(store.DB)

	return d.db.Save(task).Error
}

// Delete 删除重放任务
func (d *ReplayTaskDao) Delete(id int) error {
	d = NewReplayTaskDao(store.DB)

	return d.db.Delete(&model.ReplayTask{}, id).Error
}

// GetByID 根据ID获取重放任务
func (d *ReplayTaskDao) GetByID(id int) (*model.ReplayTask, error) {
	d = NewReplayTaskDao(store.DB)

	var task model.ReplayTask
	err := d.db.First(&task, id).Error
	if err != nil {
		return nil, err
	}
	return &task, nil
}

// List 获取重放任务列表
func (d *ReplayTaskDao) List(task *model.ReplayTask, conditions map[string]interface{}) ([]*model.ReplayTask, error) {
	d = NewReplayTaskDao(store.DB)

	items, err := query.List[model.ReplayTask](d.db, *task, conditions)
	if err != nil {
		return nil, err
	}

	// 转换为指针切片
	tasks := make([]*model.ReplayTask, len(items))
	for i := range items {
		tasks[i] = &items[i]
	}
	return tasks, nil
}

// Page 分页获取重放任务列表
func (d *ReplayTaskDao) Page(task *model.ReplayTask, conditions map[string]interface{}, page, size int) ([]*model.ReplayTask, int64, error) {
	d = NewReplayTaskDao(store.DB)
	items, total, err := query.Page[model.ReplayTask](d.db, *task, conditions, page, size)
	if err != nil {
		return nil, 0, err
	}

	// 转换为指针切片
	tasks := make([]*model.ReplayTask, len(items))
	for i := range items {
		tasks[i] = &items[i]
	}
	return tasks, total, nil
}

// UpdateStatus 更新任务状态
func (d *ReplayTaskDao) UpdateStatus(id int, status int) error {
	d = NewReplayTaskDao(store.DB)

	return d.db.Model(&model.ReplayTask{}).Where("id = ?", id).Update("status", status).Error
}

// UpdateNumber 更新任务数量
func (d *ReplayTaskDao) UpdateNumber(id int, taskNumber int64) error {
	d = NewReplayTaskDao(store.DB)

	return d.db.Model(&model.ReplayTask{}).Where("id = ?", id).Update("task_number", taskNumber).Error
}

// FindOne 根据条件查询单条数据记录
func (d *ReplayTaskDao) FindOne(conditions map[string]interface{}) (*model.ReplayTask, error) {
	var data model.ReplayTask
	err := d.db.Where(conditions).First(&data).Error
	if err != nil {
		return nil, err
	}
	return &data, nil
}
