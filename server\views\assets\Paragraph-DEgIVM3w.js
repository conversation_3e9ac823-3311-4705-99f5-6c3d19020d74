import{d as p,A as oe,aI as We,x as g,aJ as xe,aK as Ue,E as Ve,K as ie,aL as Fe,r as Se,a6 as te,j as ne,o as Ee,a0 as we,p as Ke,P as U,a1 as Q,aM as Xe,aN as qe,L as Oe,N as X,Z as Je,$ as Z,a2 as Qe,aO as pe,aP as Te,aQ as G,aR as Ze,aw as Y,D as fe,aS as ge,y as $e,k as me,aT as Ge,af as Ye}from"./index-DlVegDiC.js";import{u as et,C as tt}from"./index-CSU5nP3m.js";var nt={icon:{tag:"svg",attrs:{viewBox:"64 64 896 896",focusable:"false"},children:[{tag:"path",attrs:{d:"M864 170h-60c-4.4 0-8 3.6-8 8v518H310v-73c0-6.7-7.8-10.5-13-6.3l-141.9 112a8 8 0 000 12.6l141.9 112c5.3 4.2 13 .4 13-6.3v-75h498c35.3 0 64-28.7 64-64V178c0-4.4-3.6-8-8-8z"}}]},name:"enter",theme:"outlined"};function ye(e){for(var t=1;t<arguments.length;t++){var n=arguments[t]!=null?Object(arguments[t]):{},o=Object.keys(n);typeof Object.getOwnPropertySymbols=="function"&&(o=o.concat(Object.getOwnPropertySymbols(n).filter(function(r){return Object.getOwnPropertyDescriptor(n,r).enumerable}))),o.forEach(function(r){ot(e,r,n[r])})}return e}function ot(e,t,n){return t in e?Object.defineProperty(e,t,{value:n,enumerable:!0,configurable:!0,writable:!0}):e[t]=n,e}var le=function(t,n){var o=ye({},t,n.attrs);return p(oe,ye({},o,{icon:nt}),null)};le.displayName="EnterOutlined";le.inheritAttrs=!1;const it=(e,t,n,o)=>{const{sizeMarginHeadingVerticalEnd:r,fontWeightStrong:u}=o;return{marginBottom:r,color:n,fontWeight:u,fontSize:e,lineHeight:t}},lt=e=>{const t=[1,2,3,4,5],n={};return t.forEach(o=>{n[`
      h${o}&,
      div&-h${o},
      div&-h${o} > textarea,
      h${o}
    `]=it(e[`fontSizeHeading${o}`],e[`lineHeightHeading${o}`],e.colorTextHeading,e)}),n},rt=e=>{const{componentCls:t}=e;return{"a&, a":g(g({},xe(e)),{textDecoration:e.linkDecoration,"&:active, &:hover":{textDecoration:e.linkHoverDecoration},[`&[disabled], &${t}-disabled`]:{color:e.colorTextDisabled,cursor:"not-allowed","&:active, &:hover":{color:e.colorTextDisabled},"&:active":{pointerEvents:"none"}}})}},at=()=>({code:{margin:"0 0.2em",paddingInline:"0.4em",paddingBlock:"0.2em 0.1em",fontSize:"85%",background:"rgba(150, 150, 150, 0.1)",border:"1px solid rgba(100, 100, 100, 0.2)",borderRadius:3},kbd:{margin:"0 0.2em",paddingInline:"0.4em",paddingBlock:"0.15em 0.1em",fontSize:"90%",background:"rgba(150, 150, 150, 0.06)",border:"1px solid rgba(100, 100, 100, 0.2)",borderBottomWidth:2,borderRadius:3},mark:{padding:0,backgroundColor:We[2]},"u, ins":{textDecoration:"underline",textDecorationSkipInk:"auto"},"s, del":{textDecoration:"line-through"},strong:{fontWeight:600},"ul, ol":{marginInline:0,marginBlock:"0 1em",padding:0,li:{marginInline:"20px 0",marginBlock:0,paddingInline:"4px 0",paddingBlock:0}},ul:{listStyleType:"circle",ul:{listStyleType:"disc"}},ol:{listStyleType:"decimal"},"pre, blockquote":{margin:"1em 0"},pre:{padding:"0.4em 0.6em",whiteSpace:"pre-wrap",wordWrap:"break-word",background:"rgba(150, 150, 150, 0.1)",border:"1px solid rgba(100, 100, 100, 0.2)",borderRadius:3,code:{display:"inline",margin:0,padding:0,fontSize:"inherit",fontFamily:"inherit",background:"transparent",border:0}},blockquote:{paddingInline:"0.6em 0",paddingBlock:0,borderInlineStart:"4px solid rgba(100, 100, 100, 0.2)",opacity:.85}}),ct=e=>{const{componentCls:t}=e,o=Ue(e).inputPaddingVertical+1;return{"&-edit-content":{position:"relative","div&":{insetInlineStart:-e.paddingSM,marginTop:-o,marginBottom:`calc(1em - ${o}px)`},[`${t}-edit-content-confirm`]:{position:"absolute",insetInlineEnd:e.marginXS+2,insetBlockEnd:e.marginXS,color:e.colorTextDescription,fontWeight:"normal",fontSize:e.fontSize,fontStyle:"normal",pointerEvents:"none"},textarea:{margin:"0!important",MozTransition:"none",height:"1em"}}}},st=e=>({"&-copy-success":{"\n    &,\n    &:hover,\n    &:focus":{color:e.colorSuccess}}}),dt=()=>({"\n  a&-ellipsis,\n  span&-ellipsis\n  ":{display:"inline-block",maxWidth:"100%"},"&-single-line":{whiteSpace:"nowrap"},"&-ellipsis-single-line":{overflow:"hidden",textOverflow:"ellipsis","a&, span&":{verticalAlign:"bottom"}},"&-ellipsis-multiple-line":{display:"-webkit-box",overflow:"hidden",WebkitLineClamp:3,WebkitBoxOrient:"vertical"}}),ut=e=>{const{componentCls:t,sizeMarginHeadingVerticalStart:n}=e;return{[t]:g(g(g(g(g(g(g(g(g({color:e.colorText,wordBreak:"break-word",lineHeight:e.lineHeight,[`&${t}-secondary`]:{color:e.colorTextDescription},[`&${t}-success`]:{color:e.colorSuccess},[`&${t}-warning`]:{color:e.colorWarning},[`&${t}-danger`]:{color:e.colorError,"a&:active, a&:focus":{color:e.colorErrorActive},"a&:hover":{color:e.colorErrorHover}},[`&${t}-disabled`]:{color:e.colorTextDisabled,cursor:"not-allowed",userSelect:"none"},"\n        div&,\n        p\n      ":{marginBottom:"1em"}},lt(e)),{[`
      & + h1${t},
      & + h2${t},
      & + h3${t},
      & + h4${t},
      & + h5${t}
      `]:{marginTop:n},"\n      div,\n      ul,\n      li,\n      p,\n      h1,\n      h2,\n      h3,\n      h4,\n      h5":{"\n        + h1,\n        + h2,\n        + h3,\n        + h4,\n        + h5\n        ":{marginTop:n}}}),at()),rt(e)),{[`
        ${t}-expand,
        ${t}-edit,
        ${t}-copy
      `]:g(g({},xe(e)),{marginInlineStart:e.marginXXS})}),ct(e)),st(e)),dt()),{"&-rtl":{direction:"rtl"}})}},De=Ve("Typography",e=>[ut(e)],{sizeMarginHeadingVerticalStart:"1.2em",sizeMarginHeadingVerticalEnd:"0.5em"}),pt=()=>({prefixCls:String,value:String,maxlength:Number,autoSize:{type:[Boolean,Object]},onSave:Function,onCancel:Function,onEnd:Function,onChange:Function,originContent:String,direction:String,component:String}),ft=ie({compatConfig:{MODE:3},name:"Editable",inheritAttrs:!1,props:pt(),setup(e,t){let{emit:n,slots:o,attrs:r}=t;const{prefixCls:u}=Fe(e),c=Se({current:e.value||"",lastKeyCode:void 0,inComposition:!1,cancelFlag:!1});te(()=>e.value,f=>{c.current=f});const l=ne();Ee(()=>{var f;if(l.value){const s=(f=l.value)===null||f===void 0?void 0:f.resizableTextArea,b=s==null?void 0:s.textArea;b.focus();const{length:y}=b.value;b.setSelectionRange(y,y)}});function h(f){l.value=f}function x(f){let{target:{value:s}}=f;c.current=s.replace(/[\r\n]/g,""),n("change",c.current)}function m(){c.inComposition=!0}function T(){c.inComposition=!1}function N(f){const{keyCode:s}=f;s===Q.ENTER&&f.preventDefault(),!c.inComposition&&(c.lastKeyCode=s)}function O(f){const{keyCode:s,ctrlKey:b,altKey:y,metaKey:E,shiftKey:H}=f;c.lastKeyCode===s&&!c.inComposition&&!b&&!y&&!E&&!H&&(s===Q.ENTER?($(),n("end")):s===Q.ESC&&(c.current=e.originContent,n("cancel")))}function P(){$()}function $(){n("save",c.current.trim())}const[M,B]=De(u);return()=>{const f=we({[`${u.value}`]:!0,[`${u.value}-edit-content`]:!0,[`${u.value}-rtl`]:e.direction==="rtl",[e.component?`${u.value}-${e.component}`:""]:!0},r.class,B.value);return M(p("div",U(U({},r),{},{class:f}),[p(Ke,{ref:h,maxlength:e.maxlength,value:c.current,onChange:x,onKeydown:N,onKeyup:O,onCompositionstart:m,onCompositionend:T,onBlur:P,rows:1,autoSize:e.autoSize===void 0||e.autoSize},null),o.enterIcon?o.enterIcon({className:`${e.prefixCls}-edit-content-confirm`}):p(le,{class:`${e.prefixCls}-edit-content-confirm`},null)]))}}}),gt=3,mt=8;let S;const ee={padding:0,margin:0,display:"inline",lineHeight:"inherit"};function Ie(e,t){e.setAttribute("aria-hidden","true");const n=window.getComputedStyle(t),o=qe(n);e.setAttribute("style",o),e.style.position="fixed",e.style.left="0",e.style.height="auto",e.style.minHeight="auto",e.style.maxHeight="auto",e.style.paddingTop="0",e.style.paddingBottom="0",e.style.borderTopWidth="0",e.style.borderBottomWidth="0",e.style.top="-999999px",e.style.zIndex="-1000",e.style.textOverflow="clip",e.style.whiteSpace="normal",e.style.webkitLineClamp="none"}function yt(e){const t=document.createElement("div");Ie(t,e),t.appendChild(document.createTextNode("text")),document.body.appendChild(t);const n=t.getBoundingClientRect().height;return document.body.removeChild(t),n}const bt=(e,t,n,o,r)=>{S||(S=document.createElement("div"),S.setAttribute("aria-hidden","true"),document.body.appendChild(S));const{rows:u,suffix:c=""}=t,l=yt(e),h=Math.round(l*u*100)/100;Ie(S,e);const x=Xe({render(){return p("div",{style:ee},[p("span",{style:ee},[n,c]),p("span",{style:ee},[o])])}});x.mount(S);function m(){return Math.round(S.getBoundingClientRect().height*100)/100-.1<=h}if(m())return x.unmount(),{content:n,text:S.innerHTML,ellipsis:!1};const T=Array.prototype.slice.apply(S.childNodes[0].childNodes[0].cloneNode(!0).childNodes).filter(s=>{let{nodeType:b,data:y}=s;return b!==mt&&y!==""}),N=Array.prototype.slice.apply(S.childNodes[0].childNodes[1].cloneNode(!0).childNodes);x.unmount();const O=[];S.innerHTML="";const P=document.createElement("span");S.appendChild(P);const $=document.createTextNode(r+c);P.appendChild($),N.forEach(s=>{S.appendChild(s)});function M(s){P.insertBefore(s,$)}function B(s,b){let y=arguments.length>2&&arguments[2]!==void 0?arguments[2]:0,E=arguments.length>3&&arguments[3]!==void 0?arguments[3]:b.length,H=arguments.length>4&&arguments[4]!==void 0?arguments[4]:0;const _=Math.floor((y+E)/2),q=b.slice(0,_);if(s.textContent=q,y>=E-1)for(let A=E;A>=y;A-=1){const L=b.slice(0,A);if(s.textContent=L,m()||!L)return A===b.length?{finished:!1,vNode:b}:{finished:!0,vNode:L}}return m()?B(s,b,_,E,_):B(s,b,y,_,H)}function f(s){if(s.nodeType===gt){const y=s.textContent||"",E=document.createTextNode(y);return M(E),B(E,y)}return{finished:!1,vNode:null}}return T.some(s=>{const{finished:b,vNode:y}=f(s);return y&&O.push(y),b}),{content:O,text:S.innerHTML,ellipsis:!0}};var ht=function(e,t){var n={};for(var o in e)Object.prototype.hasOwnProperty.call(e,o)&&t.indexOf(o)<0&&(n[o]=e[o]);if(e!=null&&typeof Object.getOwnPropertySymbols=="function")for(var r=0,o=Object.getOwnPropertySymbols(e);r<o.length;r++)t.indexOf(o[r])<0&&Object.prototype.propertyIsEnumerable.call(e,o[r])&&(n[o[r]]=e[o[r]]);return n};const vt=()=>({prefixCls:String,direction:String,component:String}),Ct=ie({name:"ATypography",inheritAttrs:!1,props:vt(),setup(e,t){let{slots:n,attrs:o}=t;const{prefixCls:r,direction:u}=Oe("typography",e),[c,l]=De(r);return()=>{var h;const x=g(g({},e),o),{prefixCls:m,direction:T,component:N="article"}=x,O=ht(x,["prefixCls","direction","component"]);return c(p(N,U(U({},O),{},{class:we(r.value,{[`${r.value}-rtl`]:u.value==="rtl"},o.class,l.value)}),{default:()=>[(h=n.default)===null||h===void 0?void 0:h.call(n)]}))}}}),xt=()=>{const e=document.getSelection();if(!e.rangeCount)return function(){};let t=document.activeElement;const n=[];for(let o=0;o<e.rangeCount;o++)n.push(e.getRangeAt(o));switch(t.tagName.toUpperCase()){case"INPUT":case"TEXTAREA":t.blur();break;default:t=null;break}return e.removeAllRanges(),function(){e.type==="Caret"&&e.removeAllRanges(),e.rangeCount||n.forEach(function(o){e.addRange(o)}),t&&t.focus()}},be={"text/plain":"Text","text/html":"Url",default:"Text"},St="Copy to clipboard: #{key}, Enter";function Et(e){const t=(/mac os x/i.test(navigator.userAgent)?"⌘":"Ctrl")+"+C";return e.replace(/#{\s*key\s*}/g,t)}function wt(e,t){let n,o,r,u,c,l=!1;t||(t={});const h=t.debug||!1;try{if(o=xt(),r=document.createRange(),u=document.getSelection(),c=document.createElement("span"),c.textContent=e,c.style.all="unset",c.style.position="fixed",c.style.top=0,c.style.clip="rect(0, 0, 0, 0)",c.style.whiteSpace="pre",c.style.webkitUserSelect="text",c.style.MozUserSelect="text",c.style.msUserSelect="text",c.style.userSelect="text",c.addEventListener("copy",function(m){if(m.stopPropagation(),t.format)if(m.preventDefault(),typeof m.clipboardData>"u"){h&&console.warn("unable to use e.clipboardData"),h&&console.warn("trying IE specific stuff"),window.clipboardData.clearData();const T=be[t.format]||be.default;window.clipboardData.setData(T,e)}else m.clipboardData.clearData(),m.clipboardData.setData(t.format,e);t.onCopy&&(m.preventDefault(),t.onCopy(m.clipboardData))}),document.body.appendChild(c),r.selectNodeContents(c),u.addRange(r),!document.execCommand("copy"))throw new Error("copy command was unsuccessful");l=!0}catch(x){h&&console.error("unable to copy using execCommand: ",x),h&&console.warn("trying IE specific stuff");try{window.clipboardData.setData(t.format||"text",e),t.onCopy&&t.onCopy(window.clipboardData),l=!0}catch(m){h&&console.error("unable to copy using clipboardData: ",m),h&&console.error("falling back to prompt"),n=Et("message"in t?t.message:St),window.prompt(n,e)}}finally{u&&(typeof u.removeRange=="function"?u.removeRange(r):u.removeAllRanges()),c&&document.body.removeChild(c),o()}return l}var Ot={icon:{tag:"svg",attrs:{viewBox:"64 64 896 896",focusable:"false"},children:[{tag:"path",attrs:{d:"M832 64H296c-4.4 0-8 3.6-8 8v56c0 4.4 3.6 8 8 8h496v688c0 4.4 3.6 8 8 8h56c4.4 0 8-3.6 8-8V96c0-17.7-14.3-32-32-32zM704 192H192c-17.7 0-32 14.3-32 32v530.7c0 8.5 3.4 16.6 9.4 22.6l173.3 173.3c2.2 2.2 4.7 4 7.4 5.5v1.9h4.2c3.5 1.3 7.2 2 11 2H704c17.7 0 32-14.3 32-32V224c0-17.7-14.3-32-32-32zM350 856.2L263.9 770H350v86.2zM664 888H414V746c0-22.1-17.9-40-40-40H232V264h432v624z"}}]},name:"copy",theme:"outlined"};function he(e){for(var t=1;t<arguments.length;t++){var n=arguments[t]!=null?Object(arguments[t]):{},o=Object.keys(n);typeof Object.getOwnPropertySymbols=="function"&&(o=o.concat(Object.getOwnPropertySymbols(n).filter(function(r){return Object.getOwnPropertyDescriptor(n,r).enumerable}))),o.forEach(function(r){Tt(e,r,n[r])})}return e}function Tt(e,t,n){return t in e?Object.defineProperty(e,t,{value:n,enumerable:!0,configurable:!0,writable:!0}):e[t]=n,e}var re=function(t,n){var o=he({},t,n.attrs);return p(oe,he({},o,{icon:Ot}),null)};re.displayName="CopyOutlined";re.inheritAttrs=!1;var $t={icon:{tag:"svg",attrs:{viewBox:"64 64 896 896",focusable:"false"},children:[{tag:"path",attrs:{d:"M257.7 752c2 0 4-.2 6-.5L431.9 722c2-.4 3.9-1.3 5.3-2.8l423.9-423.9a9.96 9.96 0 000-14.1L694.9 114.9c-1.9-1.9-4.4-2.9-7.1-2.9s-5.2 1-7.1 2.9L256.8 538.8c-1.5 1.5-2.4 3.3-2.8 5.3l-29.5 168.2a33.5 33.5 0 009.4 29.8c6.6 6.4 14.9 9.9 23.8 9.9zm67.4-174.4L687.8 215l73.3 73.3-362.7 362.6-88.9 15.7 15.6-89zM880 836H144c-17.7 0-32 14.3-32 32v36c0 4.4 3.6 8 8 8h784c4.4 0 8-3.6 8-8v-36c0-17.7-14.3-32-32-32z"}}]},name:"edit",theme:"outlined"};function ve(e){for(var t=1;t<arguments.length;t++){var n=arguments[t]!=null?Object(arguments[t]):{},o=Object.keys(n);typeof Object.getOwnPropertySymbols=="function"&&(o=o.concat(Object.getOwnPropertySymbols(n).filter(function(r){return Object.getOwnPropertyDescriptor(n,r).enumerable}))),o.forEach(function(r){Dt(e,r,n[r])})}return e}function Dt(e,t,n){return t in e?Object.defineProperty(e,t,{value:n,enumerable:!0,configurable:!0,writable:!0}):e[t]=n,e}var ae=function(t,n){var o=ve({},t,n.attrs);return p(oe,ve({},o,{icon:$t}),null)};ae.displayName="EditOutlined";ae.inheritAttrs=!1;var It=function(e,t){var n={};for(var o in e)Object.prototype.hasOwnProperty.call(e,o)&&t.indexOf(o)<0&&(n[o]=e[o]);if(e!=null&&typeof Object.getOwnPropertySymbols=="function")for(var r=0,o=Object.getOwnPropertySymbols(e);r<o.length;r++)t.indexOf(o[r])<0&&Object.prototype.propertyIsEnumerable.call(e,o[r])&&(n[o[r]]=e[o[r]]);return n};const Nt=Te("webkitLineClamp"),Pt=Te("textOverflow"),Ce="...",Ne=()=>({editable:{type:[Boolean,Object],default:void 0},copyable:{type:[Boolean,Object],default:void 0},prefixCls:String,component:String,type:String,disabled:{type:Boolean,default:void 0},ellipsis:{type:[Boolean,Object],default:void 0},code:{type:Boolean,default:void 0},mark:{type:Boolean,default:void 0},underline:{type:Boolean,default:void 0},delete:{type:Boolean,default:void 0},strong:{type:Boolean,default:void 0},keyboard:{type:Boolean,default:void 0},content:String,"onUpdate:content":Function}),Bt=ie({compatConfig:{MODE:3},name:"TypographyBase",inheritAttrs:!1,props:Ne(),setup(e,t){let{slots:n,attrs:o,emit:r}=t;const{prefixCls:u,direction:c}=Oe("typography",e),l=Se({copied:!1,ellipsisText:"",ellipsisContent:null,isEllipsis:!1,expanded:!1,clientRendered:!1,expandStr:"",copyStr:"",copiedStr:"",editStr:"",copyId:void 0,rafId:void 0,prevProps:void 0,originContent:""}),h=ne(),x=ne(),m=X(()=>{const i=e.ellipsis;return i?g({rows:1,expandable:!1},typeof i=="object"?i:null):{}});Ee(()=>{l.clientRendered=!0,_()}),Je(()=>{clearTimeout(l.copyId),Z.cancel(l.rafId)}),te([()=>m.value.rows,()=>e.content],()=>{fe(()=>{E()})},{flush:"post",deep:!0}),Qe(()=>{e.content===void 0&&(pe(!e.editable),pe(!e.ellipsis))});function T(){var i;return e.ellipsis||e.editable?e.content:(i=G(h.value))===null||i===void 0?void 0:i.innerText}function N(i){const{onExpand:a}=m.value;l.expanded=!0,a==null||a(i)}function O(i){i.preventDefault(),l.originContent=e.content,y(!0)}function P(i){$(i),y(!1)}function $(i){const{onChange:a}=f.value;i!==e.content&&(r("update:content",i),a==null||a(i))}function M(){var i,a;(a=(i=f.value).onCancel)===null||a===void 0||a.call(i),y(!1)}function B(i){i.preventDefault(),i.stopPropagation();const{copyable:a}=e,d=g({},typeof a=="object"?a:null);d.text===void 0&&(d.text=T()),wt(d.text||""),l.copied=!0,fe(()=>{d.onCopy&&d.onCopy(i),l.copyId=setTimeout(()=>{l.copied=!1},3e3)})}const f=X(()=>{const i=e.editable;return i?g({},typeof i=="object"?i:null):{editing:!1}}),[s,b]=et(!1,{value:X(()=>f.value.editing)});function y(i){const{onStart:a}=f.value;i&&a&&a(),b(i)}te(s,i=>{var a;i||(a=x.value)===null||a===void 0||a.focus()},{flush:"post"});function E(i){if(i){const{width:a,height:d}=i;if(!a||!d)return}Z.cancel(l.rafId),l.rafId=Z(()=>{_()})}const H=X(()=>{const{rows:i,expandable:a,suffix:d,onEllipsis:v,tooltip:C}=m.value;return d||C||e.editable||e.copyable||a||v?!1:i===1?Pt:Nt}),_=()=>{const{ellipsisText:i,isEllipsis:a}=l,{rows:d,suffix:v,onEllipsis:C}=m.value;if(!d||d<0||!G(h.value)||l.expanded||e.content===void 0||H.value)return;const{content:D,text:z,ellipsis:R}=bt(G(h.value),{rows:d,suffix:v},e.content,se(!0),Ce);(i!==z||l.isEllipsis!==R)&&(l.ellipsisText=z,l.ellipsisContent=D,l.isEllipsis=R,a!==R&&C&&C(R))};function q(i,a){let{mark:d,code:v,underline:C,delete:D,strong:z,keyboard:R}=i,W=a;function I(V,w){if(!V)return;const F=function(){return W}();W=p(w,null,{default:()=>[F]})}return I(z,"strong"),I(C,"u"),I(D,"del"),I(v,"code"),I(d,"mark"),I(R,"kbd"),W}function A(i){const{expandable:a,symbol:d}=m.value;if(!a||!i&&(l.expanded||!l.isEllipsis))return null;const v=(n.ellipsisSymbol?n.ellipsisSymbol():d)||l.expandStr;return p("a",{key:"expand",class:`${u.value}-expand`,onClick:N,"aria-label":l.expandStr},[v])}function L(){if(!e.editable)return;const{tooltip:i,triggerType:a=["icon"]}=e.editable,d=n.editableIcon?n.editableIcon():p(ae,{role:"button"},null),v=n.editableTooltip?n.editableTooltip():l.editStr,C=typeof v=="string"?v:"";return a.indexOf("icon")!==-1?p(Y,{key:"edit",title:i===!1?"":v},{default:()=>[p(ge,{ref:x,class:`${u.value}-edit`,onClick:O,"aria-label":C},{default:()=>[d]})]}):null}function Pe(){if(!e.copyable)return;const{tooltip:i}=e.copyable,a=l.copied?l.copiedStr:l.copyStr,d=n.copyableTooltip?n.copyableTooltip({copied:l.copied}):a,v=typeof d=="string"?d:"",C=l.copied?p(tt,null,null):p(re,null,null),D=n.copyableIcon?n.copyableIcon({copied:!!l.copied}):C;return p(Y,{key:"copy",title:i===!1?"":d},{default:()=>[p(ge,{class:[`${u.value}-copy`,{[`${u.value}-copy-success`]:l.copied}],onClick:B,"aria-label":v},{default:()=>[D]})]})}function Be(){const{class:i,style:a}=o,{maxlength:d,autoSize:v,onEnd:C}=f.value;return p(ft,{class:i,style:a,prefixCls:u.value,value:e.content,originContent:l.originContent,maxlength:d,autoSize:v,onSave:P,onChange:$,onCancel:M,onEnd:C,direction:c.value,component:e.component},{enterIcon:n.editableEnterIcon})}function se(i){return[A(i),L(),Pe()].filter(a=>a)}return()=>{var i;const{triggerType:a=["icon"]}=f.value,d=e.ellipsis||e.editable?e.content!==void 0?e.content:(i=n.default)===null||i===void 0?void 0:i.call(n):n.default?n.default():e.content;return s.value?Be():p(Ze,{componentName:"Text",children:v=>{const C=g(g({},e),o),{type:D,disabled:z,content:R,class:W,style:I}=C,V=It(C,["type","disabled","content","class","style"]),{rows:w,suffix:F,tooltip:J}=m.value,{edit:_e,copy:Re,copied:He,expand:Ae}=v;l.editStr=_e,l.copyStr=Re,l.copiedStr=He,l.expandStr=Ae;const ze=$e(V,["prefixCls","editable","copyable","ellipsis","mark","code","delete","underline","strong","keyboard","onUpdate:content"]),K=H.value,je=w===1&&K,de=w&&w>1&&K;let j=d,ke;if(w&&l.isEllipsis&&!l.expanded&&!K){const{title:ue}=V;let k=ue||"";!ue&&(typeof d=="string"||typeof d=="number")&&(k=String(d)),k=k==null?void 0:k.slice(String(l.ellipsisContent||"").length),j=p(me,null,[Ge(l.ellipsisContent),p("span",{title:k,"aria-hidden":"true"},[Ce]),F])}else j=p(me,null,[d,F]);j=q(e,j);const Me=J&&w&&l.isEllipsis&&!l.expanded&&!K,Le=n.ellipsisTooltip?n.ellipsisTooltip():J;return p(Ye,{onResize:E,disabled:!w},{default:()=>[p(Ct,U({ref:h,class:[{[`${u.value}-${D}`]:D,[`${u.value}-disabled`]:z,[`${u.value}-ellipsis`]:w,[`${u.value}-single-line`]:w===1&&!l.isEllipsis,[`${u.value}-ellipsis-single-line`]:je,[`${u.value}-ellipsis-multiple-line`]:de},W],style:g(g({},I),{WebkitLineClamp:de?w:void 0}),"aria-label":ke,direction:c.value,onClick:a.indexOf("text")!==-1?O:()=>{}},ze),{default:()=>[Me?p(Y,{title:J===!0?d:Le},{default:()=>[p("span",null,[j])]}):j,se()]})]})}},null)}}}),_t=()=>$e(Ne(),["component"]),ce=(e,t)=>{let{slots:n,attrs:o}=t;const r=g(g(g({},e),{component:"div"}),o);return p(Bt,r,n)};ce.displayName="ATypographyParagraph";ce.inheritAttrs=!1;ce.props=_t();export{Bt as B,ce as P,Ct as T,Ne as b};
