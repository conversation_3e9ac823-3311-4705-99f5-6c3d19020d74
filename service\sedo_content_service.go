package service

import (
	"context"
	"fmt"
	"rsoc-system-go/model"
	"sync"
)

// SedoContentService Sedo内容服务
type SedoContentService struct {
	articleService  *SedoArticleService
	categoryService *SedoCategoryService
	domainService   *SedoDomainService
	mediaService    *SedoMediaService
	httpClient      *SedoHTTPClient // HTTP客户端引用
}

var (
	sedoContentService     *SedoContentService
	sedoContentServiceOnce sync.Once
)

// GetSedoContentService 获取Sedo内容服务单例
func GetSedoContentService() *SedoContentService {
	sedoContentServiceOnce.Do(func() {
		httpClient := GetSedoHTTPClient()
		sedoContentService = NewSedoContentService(
			NewSedoArticleService(httpClient),
			NewSedoCategoryService(httpClient),
			NewSedoDomainService(httpClient),
			NewSedoMediaService(httpClient),
			httpClient,
		)
	})
	return sedoContentService
}

// NewSedoContentService 创建新的Sedo内容服务
func NewSedoContentService(
	articleService *SedoArticleService,
	categoryService *SedoCategoryService,
	domainService *SedoDomainService,
	mediaService *SedoMediaService,
	httpClient *SedoHTTPClient,
) *SedoContentService {
	return &SedoContentService{
		articleService:  articleService,
		categoryService: categoryService,
		domainService:   domainService,
		mediaService:    mediaService,
		httpClient:      httpClient,
	}
}

// SetBearerToken 设置Bearer令牌
func (s *SedoContentService) SetBearerToken(token string) {
	s.httpClient.SetBearerToken(token)
}

// UseOAuth 启用OAuth身份验证
func (s *SedoContentService) UseOAuth(enable bool) {
	s.httpClient.UseOAuth(enable)
}

// SetOAuthCredentials 设置OAuth凭据
func (s *SedoContentService) SetOAuthCredentials(clientID, clientSecret string) {
	s.httpClient.SetOAuthCredentials(clientID, clientSecret)
}

// SetOAuthConfig 设置OAuth配置
func (s *SedoContentService) SetOAuthConfig(tokenEndpoint, clientID, clientSecret, audience, grantType string) {
	oauthClient := GetSedoOAuthClient()
	oauthClient.SetConfig(SedoOAuthConfig{
		TokenEndpoint: tokenEndpoint,
		ClientID:      clientID,
		ClientSecret:  clientSecret,
		Audience:      audience,
		GrantType:     grantType,
		Logger:        oauthClient.logger,
	})
	s.httpClient.UseOAuth(true)
}

// -----------------------------
// 文章相关方法
// -----------------------------

// GetArticles 获取文章列表
func (s *SedoContentService) GetArticles(ctx context.Context, page, size int, sort, term string) ([]model.SedoArticle, *model.SedoPageResponse, error) {
	return s.articleService.GetArticles(ctx, page, size, sort, term)
}

// GetArticleByID 根据ID获取文章
func (s *SedoContentService) GetArticleByID(ctx context.Context, id string) (*model.SedoArticle, error) {
	return s.articleService.GetArticleByID(ctx, id)
}

// CreateArticle 创建文章
func (s *SedoContentService) CreateArticle(ctx context.Context, request *model.SedoCreateArticleRequest) (*model.SedoArticle, error) {
	return s.articleService.CreateArticle(ctx, request)
}

// UpdateArticle 更新文章
func (s *SedoContentService) UpdateArticle(ctx context.Context, id string, request *model.SedoUpdateArticleRequest) (*model.SedoArticle, error) {
	return s.articleService.UpdateArticle(ctx, id, request)
}

// PatchArticle 部分更新文章
func (s *SedoContentService) PatchArticle(ctx context.Context, id string, request *model.SedoUpdateArticleRequest) (*model.SedoArticle, error) {
	return s.articleService.PatchArticle(ctx, id, request)
}

// DeleteArticle 删除文章
func (s *SedoContentService) DeleteArticle(ctx context.Context, id string) error {
	return s.articleService.DeleteArticle(ctx, id)
}

// GenerateArticle 生成文章
func (s *SedoContentService) GenerateArticle(ctx context.Context, request *model.SedoGenerateArticleRequest, async bool, referenceID string) (*model.SedoArticle, error) {
	return s.articleService.GenerateArticle(ctx, request, async, referenceID)
}

// GetPublishedArticles 获取已发布文章列表
func (s *SedoContentService) GetPublishedArticles(ctx context.Context, page, size int, sort, term string) ([]model.SedoPublishedArticle, *model.SedoPageResponse, error) {
	return s.articleService.GetPublishedArticles(ctx, page, size, sort, term)
}

// GetPublishedArticleByID 根据ID获取已发布文章
func (s *SedoContentService) GetPublishedArticleByID(ctx context.Context, id string) (*model.SedoPublishedArticle, error) {
	return s.articleService.GetPublishedArticleByID(ctx, id)
}

// PublishArticle 发布文章
func (s *SedoContentService) PublishArticle(ctx context.Context, request *model.SedoPublishArticleRequest, async bool, referenceID string) (*model.SedoPublishedArticle, error) {
	return s.articleService.PublishArticle(ctx, request, async, referenceID)
}

// UnpublishArticle 取消发布文章
func (s *SedoContentService) UnpublishArticle(ctx context.Context, id string) error {
	return s.articleService.UnpublishArticle(ctx, id)
}

// GetDetailedArticles 获取详细文章列表
func (s *SedoContentService) GetDetailedArticles(ctx context.Context, page, size int, sort, term string) ([]model.SedoDetailedArticleResponse, *model.SedoPageResponse, error) {
	return s.articleService.GetDetailedArticles(ctx, page, size, sort, term)
}

// GetDetailedArticleByID 根据ID获取详细文章
func (s *SedoContentService) GetDetailedArticleByID(ctx context.Context, id string) (*model.SedoDetailedArticleResponse, error) {
	return s.articleService.GetDetailedArticleByID(ctx, id)
}

// -----------------------------
// 分类相关方法
// -----------------------------

// GetCategories 获取分类列表
func (s *SedoContentService) GetCategories(ctx context.Context, page, size int, term string) ([]model.SedoCategory, *model.SedoPageResponse, error) {
	return s.categoryService.GetCategories(ctx, page, size, term)
}

// GetCategoryByID 根据ID获取分类
func (s *SedoContentService) GetCategoryByID(ctx context.Context, id string) (*model.SedoCategory, error) {
	return s.categoryService.GetCategoryByID(ctx, id)
}

// CreateCategory 创建分类
func (s *SedoContentService) CreateCategory(ctx context.Context, request *model.SedoCreateCategoryRequest) (*model.SedoCategory, error) {
	return s.categoryService.CreateCategory(ctx, request)
}

// -----------------------------
// 域名相关方法
// -----------------------------

// GetDomains 获取域名列表
func (s *SedoContentService) GetDomains(ctx context.Context, page, size int) ([]model.SedoDomain, *model.SedoPageResponse, error) {
	return s.domainService.GetDomains(ctx, page, size)
}

// GetDomainByID 根据ID获取域名
func (s *SedoContentService) GetDomainByID(ctx context.Context, id string) (*model.SedoDomain, error) {
	return s.domainService.GetDomainByID(ctx, id)
}

// -----------------------------
// 媒体资源相关方法
// -----------------------------

// GetMediaResources 获取媒体资源列表
func (s *SedoContentService) GetMediaResources(ctx context.Context, page, size int) ([]model.SedoMediaResource, *model.SedoPageResponse, error) {
	return s.mediaService.GetMediaResources(ctx, page, size)
}

// GetMediaResourceByID 根据ID获取媒体资源
func (s *SedoContentService) GetMediaResourceByID(ctx context.Context, id string) (*model.SedoMediaResource, error) {
	return s.mediaService.GetMediaResourceByID(ctx, id)
}

// UploadMediaFile 上传媒体文件
func (s *SedoContentService) UploadMediaFile(ctx context.Context, filePath string) (*model.SedoMediaResource, error) {
	return s.mediaService.UploadMediaFile(ctx, filePath)
}

// DownloadMediaFile 下载媒体文件
func (s *SedoContentService) DownloadMediaFile(ctx context.Context, id, destPath string) error {
	return s.mediaService.DownloadMediaFile(ctx, id, destPath)
}

// -----------------------------
// 辅助方法
// -----------------------------

// InitializeWithToken 使用令牌初始化服务
func (s *SedoContentService) InitializeWithToken(token string) {
	s.SetBearerToken(token)
}

// InitializeWithOAuth 使用OAuth凭据初始化服务
func (s *SedoContentService) InitializeWithOAuth(clientID, clientSecret string) {
	s.SetOAuthCredentials(clientID, clientSecret)
}

// InitializeWithFullOAuth 使用完整OAuth配置初始化服务
func (s *SedoContentService) InitializeWithFullOAuth(tokenEndpoint, clientID, clientSecret, audience, grantType string) {
	s.SetOAuthConfig(tokenEndpoint, clientID, clientSecret, audience, grantType)
}

// CheckConnection 检查与API的连接
func (s *SedoContentService) CheckConnection(ctx context.Context) error {
	// 尝试获取分类列表作为连接检查
	_, _, err := s.GetCategories(ctx, 0, 1, "")
	if err != nil {
		return fmt.Errorf("连接Sedo API失败: %v", err)
	}
	return nil
}
