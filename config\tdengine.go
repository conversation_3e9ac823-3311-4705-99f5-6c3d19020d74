package config

import (
	"database/sql"
	_ "github.com/taosdata/driver-go/v3/taosWS"
	"log"
	"os"
	"rsoc-system-go/store"
)

var tdengineName = "TDEngine"

func init() {
	initTDEngine()
}

func initTDEngine() {
	dsn := os.Getenv("TDENGINE_URL")
	if dsn == "" || validateMySQLDSN(dsn) != nil {
		return
	}
	log.Printf("[%s] TDEngine config initial", tdengineName)
	log.Printf("[%s] dsn: %s", tdengineName, dsn)
	taos, err := sql.Open("taosWS", dsn)
	if err != nil {
		log.Printf("[%s] Failed to connect to %s ; ErrMessage: %v ", tdengineName, dsn, err.Error())
		//log.Fatalln("Failed to connect to " + dsn + "; ErrMessage: " + err.Error())
	}
	store.SetTDB(taos)
	log.Printf("[%s] Connected to %s successfully.", tdengineName, dsn)

	defer taos.Close()
}
