package service

import (
	"context"
	"fmt"
	"os"
	"rsoc-system-go/model"
	"time"

	"github.com/robfig/cron/v3"
	"github.com/sirupsen/logrus"
)

// GoogleReportCronService Google报告定时同步服务
type GoogleReportCronService struct {
	cron        *cron.Cron
	syncService *GoogleReportSyncService
	logger      *logrus.Logger
	securityKey string
}

// NewGoogleReportCronService 创建新的定时同步服务
func NewGoogleReportCronService() *GoogleReportCronService {
	logger := logrus.New()
	securityKey := os.Getenv("GOOGLE_REPORT_SECURITY_KEY")

	return &GoogleReportCronService{
		cron:        cron.New(),
		syncService: GetGoogleReportSyncService(),
		logger:      logger,
		securityKey: securityKey,
	}
}

// Start 启动定时任务
func (s *GoogleReportCronService) Start() error {
	if s.securityKey == "" {
		s.logger.Warn("GOOGLE_REPORT_SECURITY_KEY 环境变量未设置，跳过定时同步")
		return nil
	}

	// 每小时同步一次最近7天的数据
	_, err := s.cron.AddFunc("0 * * * *", s.hourlySync)
	if err != nil {
		return err
	}

	// 每天凌晨2点同步昨天的数据
	_, err = s.cron.AddFunc("0 2 * * *", s.dailySync)
	if err != nil {
		return err
	}

	s.cron.Start()
	s.logger.Info("Google报告定时同步服务已启动")
	return nil
}

// Stop 停止定时任务
func (s *GoogleReportCronService) Stop() {
	s.cron.Stop()
	s.logger.Info("Google报告定时同步服务已停止")
}

// hourlySync 每小时同步
func (s *GoogleReportCronService) hourlySync() {
	s.logger.Info("开始执行每小时Google报告同步")

	ctx, cancel := context.WithTimeout(context.Background(), 10*time.Minute)
	defer cancel()

	// 同步最近7天的数据
	endDate := time.Now().Format("2006-01-02")
	startDate := time.Now().AddDate(0, 0, -7).Format("2006-01-02")

	response, err := s.syncService.SyncGoogleReportDataByDateRange(ctx, s.securityKey, startDate, endDate)
	if err != nil {
		s.logger.Errorf("每小时同步失败: %v", err)
		return
	}

	s.logger.Infof("每小时同步完成: %+v", response)
}

// dailySync 每日同步
func (s *GoogleReportCronService) dailySync() {
	s.logger.Info("开始执行每日Google报告同步")

	ctx, cancel := context.WithTimeout(context.Background(), 30*time.Minute)
	defer cancel()

	// 同步昨天的数据
	yesterday := time.Now().AddDate(0, 0, -1).Format("2006-01-02")

	request := &model.GoogleReportSyncRequest{
		SecurityKey: s.securityKey,
		DataDate:    yesterday,
		Limit:       100,
		Page:        0,
	}

	response, err := s.syncService.SyncGoogleReportData(ctx, request)
	if err != nil {
		s.logger.Errorf("每日同步失败: %v", err)
		return
	}

	s.logger.Infof("每日同步完成: %+v", response)
}

// SyncNow 立即执行同步
func (s *GoogleReportCronService) SyncNow() error {
	if s.securityKey == "" {
		return fmt.Errorf("GOOGLE_REPORT_SECURITY_KEY 环境变量未设置")
	}

	ctx, cancel := context.WithTimeout(context.Background(), 15*time.Minute)
	defer cancel()

	// 同步最近7天的数据
	endDate := time.Now().Format("2006-01-02")
	startDate := time.Now().AddDate(0, 0, -7).Format("2006-01-02")

	response, err := s.syncService.SyncGoogleReportDataByDateRange(ctx, s.securityKey, startDate, endDate)
	if err != nil {
		return err
	}

	s.logger.Infof("手动同步完成: %+v", response)
	return nil
}
