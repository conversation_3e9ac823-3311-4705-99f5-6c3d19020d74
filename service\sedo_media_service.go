package service

import (
	"context"
	"fmt"
	"io"
	"os"
	"rsoc-system-go/model"
	"sync"
)

// SedoMediaService Sedo媒体资源服务
type SedoMediaService struct {
	httpClient *SedoHTTPClient
}

var (
	sedoMediaService     *SedoMediaService
	sedoMediaServiceOnce sync.Once
)

// GetSedoMediaService 获取Sedo媒体资源服务单例
func GetSedoMediaService() *SedoMediaService {
	sedoMediaServiceOnce.Do(func() {
		sedoMediaService = NewSedoMediaService(GetSedoHTTPClient())
	})
	return sedoMediaService
}

// NewSedoMediaService 创建新的Sedo媒体资源服务
func NewSedoMediaService(httpClient *SedoHTTPClient) *SedoMediaService {
	return &SedoMediaService{
		httpClient: httpClient,
	}
}

// GetMediaResources 获取媒体资源列表
func (s *SedoMediaService) GetMediaResources(ctx context.Context, page, size int) ([]model.SedoMediaResource, *model.SedoPageResponse, error) {
	query := s.httpClient.BuildQueryParams(page, size, "", "")

	resp, err := s.httpClient.Get(ctx, "/media", query, nil)
	if err != nil {
		return nil, nil, fmt.Errorf("获取媒体资源列表请求失败: %v", err)
	}

	var mediaResources []model.SedoMediaResource
	if err := s.httpClient.ParseResponse(resp, &mediaResources); err != nil {
		return nil, nil, fmt.Errorf("解析媒体资源列表响应失败: %v", err)
	}

	totalCount, totalPages, err := s.httpClient.GetPageHeaders(resp)
	if err != nil {
		return nil, nil, fmt.Errorf("获取分页信息失败: %v", err)
	}

	pageResponse := &model.SedoPageResponse{
		TotalCount: totalCount,
		TotalPages: totalPages,
	}

	return mediaResources, pageResponse, nil
}

// GetMediaResourceByID 根据ID获取媒体资源
func (s *SedoMediaService) GetMediaResourceByID(ctx context.Context, id string) (*model.SedoMediaResource, error) {
	resp, err := s.httpClient.Get(ctx, fmt.Sprintf("/media/%s", id), nil, nil)
	if err != nil {
		return nil, fmt.Errorf("获取媒体资源详情请求失败: %v", err)
	}

	var mediaResource model.SedoMediaResource
	if err := s.httpClient.ParseResponse(resp, &mediaResource); err != nil {
		return nil, fmt.Errorf("解析媒体资源详情响应失败: %v", err)
	}

	return &mediaResource, nil
}

// UploadMediaFile 上传媒体文件
func (s *SedoMediaService) UploadMediaFile(ctx context.Context, filePath string) (*model.SedoMediaResource, error) {
	resp, err := s.httpClient.UploadFile(ctx, "/media", filePath, "file", nil)
	if err != nil {
		return nil, fmt.Errorf("上传媒体文件请求失败: %v", err)
	}

	var mediaResource model.SedoMediaResource
	if err := s.httpClient.ParseResponse(resp, &mediaResource); err != nil {
		return nil, fmt.Errorf("解析上传媒体文件响应失败: %v", err)
	}

	return &mediaResource, nil
}

// DownloadMediaFile 下载媒体文件
func (s *SedoMediaService) DownloadMediaFile(ctx context.Context, id, destPath string) error {
	resp, err := s.httpClient.Get(ctx, fmt.Sprintf("/media/download/%s", id), nil, nil)
	if err != nil {
		return fmt.Errorf("下载媒体文件请求失败: %v", err)
	}
	defer resp.Body.Close()

	// 检查响应状态码
	if resp.StatusCode != 200 {
		body, _ := io.ReadAll(resp.Body)
		return fmt.Errorf("API返回错误 (%d): %s", resp.StatusCode, string(body))
	}

	// 创建目标文件
	out, err := os.Create(destPath)
	if err != nil {
		return fmt.Errorf("创建目标文件失败: %v", err)
	}
	defer out.Close()

	// 复制响应体到文件
	_, err = io.Copy(out, resp.Body)
	if err != nil {
		return fmt.Errorf("写入文件失败: %v", err)
	}

	return nil
}
