package tools

import (
	"math/rand"
	"strings"

	"github.com/google/uuid"
)

var letters = []rune("1234567890abcdefghijklmnopqrstuvwxyzABCDEFGHIJKLMNOPQRSTUVWXYZ")

func RandStr(n int) string {
	b := make([]rune, n)
	for i := range b {
		b[i] = letters[rand.Intn(len(letters))]
	}
	return string(b)
}

// Generate40UUID 生成40位无破折号的UUID
func Generate40UUID() string {
	// 生成两个UUID并移除破折号
	uuid1 := strings.ReplaceAll(uuid.New().String(), "-", "")
	uuid2 := strings.ReplaceAll(uuid.New().String(), "-", "")

	// 合并并截取前40位
	return (uuid1 + uuid2)[:40]
}
