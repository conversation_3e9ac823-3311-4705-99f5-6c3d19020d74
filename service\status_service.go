package service

import (
	"encoding/json"
	"os"
	"strconv"
	"sync"
	"time"
)

// StatusInfo 程序状态信息
type StatusInfo struct {
	StartTime    int64  `json:"start_time"`
	Uptime       int64  `json:"uptime"`
	RestartCount int    `json:"restart_count"`
	LastRestart  int64  `json:"last_restart,omitempty"`
	Status       string `json:"status"`
}

// StatusService 状态监控服务
type StatusService struct {
	startTime int64
	mu        sync.RWMutex
}

var (
	statusService     *StatusService
	statusServiceOnce sync.Once
)

// GetStatusService 获取状态监控服务实例
func GetStatusService() *StatusService {
	statusServiceOnce.Do(func() {
		statusService = &StatusService{
			startTime: time.Now().Unix(),
		}
	})
	return statusService
}

// GetStatus 获取当前程序状态
func (s *StatusService) GetStatus() StatusInfo {
	s.mu.RLock()
	defer s.mu.RUnlock()

	now := time.Now().Unix()
	restartCount := 0
	lastRestart := int64(0)

	// 获取重启次数
	if countStr := os.Getenv("RESTART_COUNT"); countStr != "" {
		if count, err := strconv.Atoi(countStr); err == nil {
			restartCount = count
		}
	}

	// 获取最后重启时间
	if lastRestartStr := os.Getenv("LAST_RESTART"); lastRestartStr != "" {
		if timestamp, err := strconv.ParseInt(lastRestartStr, 10, 64); err == nil {
			lastRestart = timestamp
		}
	}

	return StatusInfo{
		StartTime:    s.startTime,
		Uptime:       now - s.startTime,
		RestartCount: restartCount,
		LastRestart:  lastRestart,
		Status:       "running",
	}
}

// GetStatusJSON 获取JSON格式的状态信息
func (s *StatusService) GetStatusJSON() ([]byte, error) {
	status := s.GetStatus()
	return json.Marshal(status)
}

// UpdateStartTime 更新启动时间
func (s *StatusService) UpdateStartTime() {
	s.mu.Lock()
	defer s.mu.Unlock()
	s.startTime = time.Now().Unix()
}
