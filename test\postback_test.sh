#!/bin/bash

# Postback API 测试脚本

BASE_URL="http://localhost:8080"
POSTBACK_SETUP_URL="$BASE_URL/rsoc/postback/setup"
POSTBACK_URL="$BASE_URL/rsoc/postback"  # 专门用于第三方回调
SEDOTMP_CALLBACK_URL="$BASE_URL/rsoc/sedotmp/callback"  # SedoTMP专用回调

echo "=== Postback API 测试 ==="

# 测试1: 设置 Postback 配置
echo "1. 测试设置 Postback 配置..."
curl -X POST "$POSTBACK_SETUP_URL" \
  -H "Content-Type: application/x-www-form-urlencoded" \
  -d "security_key=test_key_123" \
  -d "domain_name=http://www.example.com" \
  -d "campaign=campaign" \
  -d "click_id=ad_click_cpc" \
  -d "payout=EPC" \
  -d "country=country" \
  -d "zip=zip" \
  -d "os_type=os_type" \
  -d "browser=browser" \
  -d "device_type=device_type" \
  -d "device_brand=device_brand" \
  -d "s1=subid1" \
  -d "s2=subid2" \
  -d "s3=subid3" \
  -w "\n状态码: %{http_code}\n\n"

echo "2. 测试第三方回调数据接收（GET方式 - AdTech格式）..."
curl -X GET "$POSTBACK_URL?campaign=123&click_id=abc123&payout=1.50&country=US&s1=test1" \
  -w "\n状态码: %{http_code}\n\n"

echo "3. 测试第三方回调数据接收（POST方式 - AdTech格式）..."
curl -X POST "$POSTBACK_URL" \
  -H "Content-Type: application/x-www-form-urlencoded" \
  -d "campaign=456&click_id=def456&payout=2.30&country=CA&s1=test2" \
  -w "\n状态码: %{http_code}\n\n"

echo "4. 测试第三方回调数据接收（SedoTMP格式）..."
curl -X GET "$POSTBACK_URL?campaign=789&click_id=xyz789&epayout=3.75&country=GB&country_name=United%20Kingdom&state=London&city=London&zip=SW1A&os_type=WINDOWS&browser=CHROME&device_type=DESKTOP&device_brand=DELL&subid1=AdSetID123&subid2=PictureID456" \
  -w "\n状态码: %{http_code}\n\n"

echo "5. 测试 JSON 格式设置..."
curl -X POST "$POSTBACK_URL" \
  -H "Content-Type: application/json" \
  -d '{
    "security_key": "test_key_456",
    "domain_name": "http://www.test.com",
    "campaign": "my_campaign",
    "click_id": "my_click_id",
    "payout": "my_payout"
  }' \
  -w "\n状态码: %{http_code}\n\n"

echo "6. 测试数据库集成（模拟转化数据）..."
curl -X GET "$POSTBACK_URL?campaign=test_campaign_001&payout=5.25" \
  -w "\n状态码: %{http_code}\n\n"

echo "7. 测试标准格式回调（按文档格式）..."
curl -X GET "$POSTBACK_URL?campaign=campaign&click_id=ad_click_cpc&payout=EPC&country=country&zip=zip&os_type=os_type&browser=browser&device_type=device_type&device_brand=device_brand&s1=subid1&s2=subid2&s3=subid3" \
  -w "\n状态码: %{http_code}\n\n"

echo "8. 测试 SedoTMP 专用回调接口（基础参数）..."
curl -X GET "$SEDOTMP_CALLBACK_URL?campaign=12345&click_id=abc123xyz&epayout=0.123&country=US" \
  -w "\n状态码: %{http_code}\n\n"

echo "9. 测试 SedoTMP 专用回调接口（完整参数）..."
curl -X GET "$SEDOTMP_CALLBACK_URL?campaign=67890&click_id=xyz789&epayout=0.085&country=GB&country_name=United%20Kingdom&state=London&city=London&zip=SW1A&os_type=ANDROID&browser=CHROME&device_type=MOBILE&device_brand=SAMSUNG&subid1=AdSetID123&subid2=PictureID456&subid3=CreativeID789&subid4=AudienceID101&subid5=CampaignTypeA" \
  -w "\n状态码: %{http_code}\n\n"

echo "10. 测试 SedoTMP 专用回调接口（POST方式）..."
curl -X POST "$SEDOTMP_CALLBACK_URL" \
  -H "Content-Type: application/x-www-form-urlencoded" \
  -d "campaign=99999&click_id=post123&epayout=2.50&country=CA&country_name=Canada&state=Ontario&city=Toronto&subid1=PostTest&subid2=MobileAd" \
  -w "\n状态码: %{http_code}\n\n"

echo "=== 基础测试完成 ==="
echo ""
echo "更多测试选项:"
echo "1. 运行详细测试数据: ./run_test_data.sh"
echo "2. 运行Python测试脚本: python3 run_test_data.py"
echo "3. 运行性能测试: python3 performance_test.py [base_url] [concurrent_users] [total_requests]"
echo ""
echo "数据验证SQL:"
echo "SELECT campaign_id, real_price, date, update_time FROM facebook_insights WHERE real_price IS NOT NULL ORDER BY update_time DESC LIMIT 10;"
echo ""
echo "清理测试数据SQL:"
echo "DELETE FROM facebook_insights WHERE campaign_id LIKE 'TEST%' OR campaign_id LIKE 'SEDO%' OR campaign_id LIKE 'ADT%';"
