import{_ as L,u as M,o as R,r as k,a as E,M as p,j as $,c as w,b as r,d as t,w as n,B as z,e as a,k as F,f as T,h as _,n as G,F as H,g as Q,I as W,p as X,s as y}from"./index-DlVegDiC.js";import{P as Y}from"./PlusOutlined-Cg2o2XQN.js";import{_ as Z}from"./index-1uCBjWky.js";import{_ as ee}from"./index-Dj7iX41A.js";import{_ as oe}from"./index-BrFZluVG.js";import"./index-CSU5nP3m.js";const te={class:"main"},ne={class:"filter"},ae={__name:"app",setup(le){const c="",m=M(),C=E();R(()=>{g()});const i=k({data:[],loading:!1}),g=()=>{i.loading=!0,fetch(`${c}/FaceBook/appList`,{method:"POST",headers:{token:m.token}}).then(o=>o.json()).then(o=>{console.log(o),o.code==1?i.data=o.data:o.code==3e3?(m.$patch({token:!1}),C.push("/login"),p.error({title:o.msg})):(i.data=[],p.error({title:o.msg})),i.loading=!1}).catch(o=>{p.error({title:"服务器错误",content:`${o}`})})},v=$(),l=k({open:!1,id:"",key:"",note:"",loading:!1}),K=()=>{l.open=!0},S=()=>{l.loading=!0,fetch(`${c}/FaceBook/addApp`,{method:"POST",body:JSON.stringify({appId:l.id,appKey:l.key,note:l.note}),headers:{token:m.token}}).then(o=>o.json()).then(o=>{console.log(o),o.code==1?(y.success("添加成功"),g(),v.value.resetFields()):p.error({title:o.msg}),l.open=!1,l.loading=!1}).catch(o=>{p.error({title:"服务器错误",content:`${o}`})})},P=$(),s=k({open:!1,app_id:"",app_key:"",token:"",loading:!1}),O=o=>{console.log(o),s.app_id=o.appId,s.app_key=o.appKey,s.open=!0},U=()=>{s.loading=!0,fetch(`${c}/Facebook/updateToken`,{method:"POST",body:JSON.stringify({appId:s.app_id,appKey:s.app_key,access_token:s.token}),headers:{token:m.token}}).then(o=>o.json()).then(o=>{console.log(o),o.code==1?(y.success("更新成功"),g(),P.value.resetFields()):p.error({title:o.msg}),s.open=!1,s.loading=!1}).catch(o=>{p.error({title:"服务器错误",content:`${o}`})})},B=o=>{fetch(`${c}/FaceBook/delApp`,{method:"POST",body:JSON.stringify({id:o.id}),headers:{token:m.token}}).then(e=>e.json()).then(e=>{console.log(e),e.code==1?(y.success(e.msg),g()):p.error({title:e.msg})}).catch(e=>{p.error({title:"服务器错误",content:`${e}`})})},N=[{title:"ID",dataIndex:"id",key:"id",align:"center"},{title:"APP ID",dataIndex:"appId",key:"appId",align:"center"},{title:"APP Key",dataIndex:"appKey",key:"appKey",align:"center"},{title:"备注",dataIndex:"note",key:"note",align:"center"},{title:"操作",key:"action",align:"center"}];return(o,e)=>{const D=Y,f=z,j=ee,q=oe,V=Z,h=W,u=Q,b=X,I=H,A=p;return T(),w(F,null,[r("div",te,[r("div",null,[r("div",ne,[e[7]||(e[7]=r("div",{class:"filter_item"},null,-1)),r("div",null,[t(f,{type:"primary",onClick:K},{icon:n(()=>[t(D)]),default:n(()=>[e[6]||(e[6]=_(" 添加账号 "))]),_:1})])])]),t(V,{columns:N,"data-source":a(i).data,rowKey:"id",pagination:!1,loading:a(i).loading,bordered:""},{bodyCell:n(({column:d,record:x})=>[d.key==="action"?(T(),w(F,{key:0},[t(f,{type:"link",onClick:J=>O(x)},{default:n(()=>e[8]||(e[8]=[_("更新Token")])),_:2},1032,["onClick"]),t(j,{type:"vertical"}),t(q,{title:"确认删除？",onConfirm:J=>B(x)},{default:n(()=>[t(f,{type:"link",danger:""},{default:n(()=>e[9]||(e[9]=[_("删除")])),_:1})]),_:2},1032,["onConfirm"])],64)):G("",!0)]),_:1},8,["data-source","loading"])]),t(A,{open:a(l).open,"onUpdate:open":e[3]||(e[3]=d=>a(l).open=d),title:"添加账号",footer:null,maskClosable:!1},{default:n(()=>[e[11]||(e[11]=r("div",{style:{height:"20px"}},null,-1)),t(I,{ref_key:"add_form",ref:v,model:a(l),onFinish:S,"label-col":{span:4},"wrapper-col":{span:18}},{default:n(()=>[t(u,{label:"APP ID",name:"id",rules:[{required:!0,message:"请输入APP ID"}]},{default:n(()=>[t(h,{value:a(l).id,"onUpdate:value":e[0]||(e[0]=d=>a(l).id=d),placeholder:"APP ID"},null,8,["value"])]),_:1}),t(u,{label:"APP Key",name:"key",rules:[{required:!0,message:"请输入APP Key"}]},{default:n(()=>[t(h,{value:a(l).key,"onUpdate:value":e[1]||(e[1]=d=>a(l).key=d),placeholder:"APP Key"},null,8,["value"])]),_:1}),t(u,{label:"备注",name:"note",rules:[{required:!0,message:"请输入备注"}]},{default:n(()=>[t(b,{value:a(l).note,"onUpdate:value":e[2]||(e[2]=d=>a(l).note=d),placeholder:"备注",rows:2},null,8,["value"])]),_:1}),t(u,{"wrapper-col":{offset:4,span:18}},{default:n(()=>[t(f,{type:"primary","html-type":"submit",loading:a(l).loading},{default:n(()=>e[10]||(e[10]=[_(" 提交 ")])),_:1},8,["loading"])]),_:1})]),_:1},8,["model"])]),_:1},8,["open"]),t(A,{open:a(s).open,"onUpdate:open":e[5]||(e[5]=d=>a(s).open=d),title:"更新Token",footer:null,maskClosable:!1},{default:n(()=>[e[13]||(e[13]=r("div",{style:{height:"20px"}},null,-1)),t(I,{ref_key:"update_form",ref:P,model:a(s),onFinish:U,"label-col":{span:4},"wrapper-col":{span:18}},{default:n(()=>[t(u,{label:"Token",name:"token",rules:[{required:!0,message:"请输入Token"}]},{default:n(()=>[t(b,{value:a(s).token,"onUpdate:value":e[4]||(e[4]=d=>a(s).token=d),placeholder:"Token",rows:5},null,8,["value"])]),_:1}),t(u,{"wrapper-col":{offset:4,span:18}},{default:n(()=>[t(f,{type:"primary","html-type":"submit",loading:a(s).loading},{default:n(()=>e[12]||(e[12]=[_(" 更新 ")])),_:1},8,["loading"])]),_:1})]),_:1},8,["model"])]),_:1},8,["open"])],64)}}},me=L(ae,[["__scopeId","data-v-6e2ded46"]]);export{me as default};
