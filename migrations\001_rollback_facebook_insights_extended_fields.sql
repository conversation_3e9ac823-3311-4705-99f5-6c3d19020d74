-- 回滚脚本：移除 facebook_insights 表的扩展字段
-- 版本：001_rollback
-- 创建时间：2025-01-19
-- 描述：回滚扩展字段的添加，恢复到原始表结构

-- 警告：此操作将删除扩展字段中的所有数据，请确保已备份重要数据
SELECT 'Starting rollback: Remove extended fields from facebook_insights table' as status;

-- 删除索引
DROP INDEX IF EXISTS idx_facebook_insights_campaign_name ON facebook_insights;
DROP INDEX IF EXISTS idx_facebook_insights_platform ON facebook_insights;
DROP INDEX IF EXISTS idx_facebook_insights_country ON facebook_insights;
DROP INDEX IF EXISTS idx_facebook_insights_hour ON facebook_insights;
DROP INDEX IF EXISTS idx_facebook_insights_create_time ON facebook_insights;
DROP INDEX IF EXISTS idx_facebook_insights_platform_country ON facebook_insights;
DROP INDEX IF EXISTS idx_facebook_insights_date_platform ON facebook_insights;

-- 删除扩展字段
ALTER TABLE facebook_insights DROP COLUMN IF EXISTS campaign_name;
ALTER TABLE facebook_insights DROP COLUMN IF EXISTS platform;
ALTER TABLE facebook_insights DROP COLUMN IF EXISTS country;
ALTER TABLE facebook_insights DROP COLUMN IF EXISTS hour;
ALTER TABLE facebook_insights DROP COLUMN IF EXISTS related_links_requests;
ALTER TABLE facebook_insights DROP COLUMN IF EXISTS related_links_impressions;
ALTER TABLE facebook_insights DROP COLUMN IF EXISTS related_links_clicks;
ALTER TABLE facebook_insights DROP COLUMN IF EXISTS related_links_rpm;
ALTER TABLE facebook_insights DROP COLUMN IF EXISTS ad_requests;
ALTER TABLE facebook_insights DROP COLUMN IF EXISTS matched_ad_requests;
ALTER TABLE facebook_insights DROP COLUMN IF EXISTS ad_impressions;
ALTER TABLE facebook_insights DROP COLUMN IF EXISTS ctr;
ALTER TABLE facebook_insights DROP COLUMN IF EXISTS ad_ctr;
ALTER TABLE facebook_insights DROP COLUMN IF EXISTS ad_rpm;
ALTER TABLE facebook_insights DROP COLUMN IF EXISTS cr;
ALTER TABLE facebook_insights DROP COLUMN IF EXISTS revenue;
ALTER TABLE facebook_insights DROP COLUMN IF EXISTS create_time;

SELECT 'Rollback completed: Extended fields removed from facebook_insights table' as status;

-- 验证字段是否已删除
SELECT 
    COLUMN_NAME,
    DATA_TYPE,
    IS_NULLABLE,
    COLUMN_DEFAULT
FROM INFORMATION_SCHEMA.COLUMNS 
WHERE TABLE_SCHEMA = DATABASE() 
  AND TABLE_NAME = 'facebook_insights'
ORDER BY ORDINAL_POSITION;
