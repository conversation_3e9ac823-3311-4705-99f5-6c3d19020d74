package tools

import (
	"crypto/rand"
	"encoding/base64"
)

// StringGenerator 字符串生成器结构体
type StringGenerator struct {
	prefix string
}

// NewStringGenerator 创建新的字符串生成器实例
func NewStringGenerator() *StringGenerator {
	return &StringGenerator{
		prefix: "IwZX",
	}
}

// GenerateString 生成指定格式的字符串
func (g *StringGenerator) GenerateString(length int) (string, error) {
	if length <= len(g.prefix) {
		return g.prefix, nil
	}

	// 计算需要生成的随机字节数
	randomBytesNeeded := ((length-len(g.prefix))*3)/4 + 1

	// 生成随机字节
	randomBytes := make([]byte, randomBytesNeeded)
	_, err := rand.Read(randomBytes)
	if err != nil {
		return "", err
	}

	// Base64编码
	encodedString := base64.RawURLEncoding.EncodeToString(randomBytes)

	// 确保长度正确并添加前缀
	result := g.prefix + encodedString
	if len(result) > length {
		result = result[:length]
	}

	return result, nil
}
