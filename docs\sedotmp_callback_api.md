# SedoTMP 回调接口文档

## 概述

`/rsoc/sedotmp/callback` 是专门用于接收 SedoTMP 平台回传转化数据的回调接口。当用户在 SedoTMP 平台完成转化行为时，SedoTMP 会调用此接口回传转化数据。

## 接口信息

**接口地址**: `/rsoc/sedotmp/callback`  
**请求方式**: `GET` 或 `POST`  
**用途**: 接收 SedoTMP 平台的转化数据回传  
**响应格式**: JSON

## SedoTMP 标准宏参数

根据 SedoTMP 官方文档，支持以下宏参数：

### 基础参数
| 宏参数 | 说明 | 示例 | 适用事件 |
|--------|------|------|----------|
| {campaign} | 广告系列ID | 12345 | all |
| {click_id} | 点击ID（用于转化跟踪） | abc123xyz | all |
| {epayout} | 估算收益金额 | 0.123 | CLICK |

### 地理位置参数
| 宏参数 | 说明 | 示例 | 适用事件 |
|--------|------|------|----------|
| {country} | 2位ISO国家代码 | US | all |
| {country_name} | 完整国家名称 | United States | all |
| {state} | 州代码或名称 | CA / California | all |
| {city} | 城市名称 | Los Angeles | all |
| {zip} | 邮编/邮政编码 | 90001 | all |

### 设备和浏览器参数
| 宏参数 | 说明 | 示例 | 适用事件 |
|--------|------|------|----------|
| {os_type} | 访客操作系统 | WINDOWS | all |
| {browser} | 访客浏览器类型 | CHROME | all |
| {device_type} | 设备类型 | MOBILE/DESKTOP | all |
| {device_brand} | 设备品牌 | APPLE | all |

### 自定义参数
| 宏参数 | 说明 | 示例 | 适用事件 |
|--------|------|------|----------|
| {subid1} | 自定义子ID 1 | AdSetID | all |
| {subid2} | 自定义子ID 2 | PictureID | all |
| {subid3} | 自定义子ID 3 | CreativeID | all |
| {subid4} | 自定义子ID 4 | AudienceID | all |
| {subid5} | 自定义子ID 5 | CampaignType | all |

## SedoTMP 回调 URL 配置

在 SedoTMP 平台中，您需要配置如下格式的回调 URL：

### 基础配置
```
http://your-domain.com/rsoc/sedotmp/callback?click_id={click_id}&epayout={epayout}&campaign={campaign}&country={country}
```

### 完整配置（包含所有参数）
```
http://your-domain.com/rsoc/sedotmp/callback?campaign={campaign}&click_id={click_id}&epayout={epayout}&country={country}&country_name={country_name}&state={state}&city={city}&zip={zip}&os_type={os_type}&browser={browser}&device_type={device_type}&device_brand={device_brand}&subid1={subid1}&subid2={subid2}&subid3={subid3}&subid4={subid4}&subid5={subid5}
```

### 自定义配置示例
```
# 针对移动端流量的配置
http://your-domain.com/rsoc/sedotmp/callback?campaign={campaign}&click_id={click_id}&epayout={epayout}&country={country}&device_type={device_type}&subid1={subid1}&subid2={subid2}

# 针对特定地理位置的配置
http://your-domain.com/rsoc/sedotmp/callback?campaign={campaign}&click_id={click_id}&epayout={epayout}&country={country}&state={state}&city={city}&subid1={subid1}
```

## 回调示例

### 基础回调示例
```bash
GET /rsoc/sedotmp/callback?campaign=12345&click_id=abc123xyz&epayout=0.123&country=US
```

### 完整参数回调示例
```bash
GET /rsoc/sedotmp/callback?campaign=12345&click_id=abc123xyz&epayout=0.123&country=US&country_name=United%20States&state=CA&city=Los%20Angeles&zip=90001&os_type=WINDOWS&browser=CHROME&device_type=DESKTOP&device_brand=DELL&subid1=AdSetID123&subid2=PictureID456&subid3=CreativeID789&subid4=AudienceID101&subid5=CampaignTypeA
```

### 移动端回调示例
```bash
GET /rsoc/sedotmp/callback?campaign=67890&click_id=mobile123&epayout=0.085&country=GB&country_name=United%20Kingdom&state=London&city=London&os_type=ANDROID&browser=CHROME&device_type=MOBILE&device_brand=SAMSUNG&subid1=MobileAd&subid2=BannerCreative
```

## 响应格式

### 成功响应
```json
{
    "code": 1,
    "msg": "ok"
}
```

### 错误响应
```json
{
    "code": 400,
    "msg": "数据格式错误"
}
```

或

```json
{
    "code": 500,
    "msg": "处理数据失败"
}
```

## 数据处理逻辑

1. **参数解析**: 自动解析 SedoTMP 宏参数
2. **收益处理**: 使用 `epayout` 作为估算收益
3. **数据存储**: 转化数据存储到 `facebook_insights` 表
4. **重复处理**: 同一天同一个 campaign 的数据会更新而不是重复创建

### 数据映射规则

| SedoTMP 参数 | 数据库字段 | 说明 |
|--------------|------------|------|
| campaign | campaign_id, account_id, ad_id, adset_id | 作为各种ID |
| epayout | real_price | 真实收益 |
| 当前日期 | date | 数据日期 |
| 固定值1 | clicks | 转化次数 |
| 当前时间 | update_time | 更新时间 |

## 在 SedoTMP 平台配置

### 步骤1：登录 SedoTMP 管理后台
1. 访问 SedoTMP 管理界面
2. 找到 Postback 或 Callback 设置页面

### 步骤2：配置回调 URL
1. 在 Postback URL 字段中输入：
   ```
   http://your-domain.com/rsoc/sedotmp/callback?campaign={campaign}&click_id={click_id}&epayout={epayout}&country={country}&subid1={subid1}&subid2={subid2}
   ```

2. 根据需要添加更多参数：
   ```
   http://your-domain.com/rsoc/sedotmp/callback?campaign={campaign}&click_id={click_id}&epayout={epayout}&country={country}&country_name={country_name}&state={state}&city={city}&zip={zip}&os_type={os_type}&browser={browser}&device_type={device_type}&device_brand={device_brand}&subid1={subid1}&subid2={subid2}&subid3={subid3}&subid4={subid4}&subid5={subid5}
   ```

### 步骤3：测试配置
1. 保存配置后，进行测试转化
2. 检查回调是否正常接收
3. 验证数据是否正确存储

## 监控和调试

### 查看回调日志
```bash
# 查看 SedoTMP 回调相关日志
tail -f /var/log/your-app.log | grep "sedotmp"

# 查看转化数据处理日志
tail -f /var/log/your-app.log | grep "处理postback数据"
```

### 验证数据存储
```sql
-- 查看最新的 SedoTMP 转化数据
SELECT campaign_id, date, real_price, update_time 
FROM facebook_insights 
WHERE real_price IS NOT NULL 
ORDER BY update_time DESC 
LIMIT 10;

-- 查看特定广告系列的转化数据
SELECT * FROM facebook_insights 
WHERE campaign_id = 'your_campaign_id' 
ORDER BY date DESC;
```

### 测试回调接口
```bash
# 手动测试 SedoTMP 回调接口
curl "http://your-domain.com/rsoc/sedotmp/callback?campaign=test123&epayout=1.50&country=US"

# 测试完整参数
curl "http://your-domain.com/rsoc/sedotmp/callback?campaign=test123&click_id=abc123&epayout=1.50&country=US&country_name=United%20States&state=CA&city=Los%20Angeles&subid1=test1&subid2=test2"
```

## 与通用回调接口的区别

| 特性 | `/rsoc/postback` | `/rsoc/sedotmp/callback` |
|------|------------------|--------------------------|
| 用途 | 通用第三方回调 | SedoTMP 专用回调 |
| 参数格式 | AdTech + SedoTMP 混合 | 纯 SedoTMP 格式 |
| 收益参数 | payout/epayout | epayout |
| 自定义参数 | s1-s3 + subid1-subid5 | subid1-subid5 |
| 地理参数 | 基础支持 | 完整支持 |

## 最佳实践

1. **参数选择**: 根据您的跟踪需求选择必要的宏参数
2. **URL 编码**: 确保特殊字符正确编码
3. **测试验证**: 配置后进行充分测试
4. **监控告警**: 设置回调失败的告警机制
5. **数据验证**: 定期检查转化数据的准确性
