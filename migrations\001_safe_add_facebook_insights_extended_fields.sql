-- 安全迁移脚本：为 facebook_insights 表添加扩展字段
-- 版本：001_safe
-- 创建时间：2025-01-19
-- 描述：使用存储过程安全添加字段，避免重复添加错误

DELIMITER $$

-- 删除存储过程（如果存在）
DROP PROCEDURE IF EXISTS AddColumnIfNotExists$$

-- 创建安全添加字段的存储过程
CREATE PROCEDURE AddColumnIfNotExists(
    IN tableName VARCHAR(128),
    IN columnName VARCHAR(128),
    IN columnDefinition TEXT
)
BEGIN
    DECLARE columnExists INT DEFAULT 0;

    -- 检查字段是否存在
    SELECT COUNT(*) INTO columnExists
    FROM INFORMATION_SCHEMA.COLUMNS
    WHERE TABLE_SCHEMA = DATABASE()
      AND TABLE_NAME = tableName
      AND COLUMN_NAME = columnName;

    -- 如果字段不存在，则添加
    IF columnExists = 0 THEN
        SET @sql = CONCAT('ALTER TABLE ', tableName, ' ADD COLUMN ', columnName, ' ', columnDefinition);
        PREPARE stmt FROM @sql;
        EXECUTE stmt;
        DEALLOCATE PREPARE stmt;
        SELECT CONCAT('Added column: ', columnName) as result;
    ELSE
        SELECT CONCAT('Column already exists: ', columnName) as result;
    END IF;
END$$

DELIMITER ;

-- 开始迁移
SELECT 'Starting safe migration: Add extended fields to facebook_insights table' as status;

-- 添加所有新字段
CALL AddColumnIfNotExists('facebook_insights', 'campaign_name', 'VARCHAR(255) DEFAULT NULL COMMENT \'广告系列名称\'');
CALL AddColumnIfNotExists('facebook_insights', 'platform', 'VARCHAR(100) DEFAULT NULL COMMENT \'平台名称\'');
CALL AddColumnIfNotExists('facebook_insights', 'country', 'VARCHAR(10) DEFAULT NULL COMMENT \'国家代码\'');
CALL AddColumnIfNotExists('facebook_insights', 'hour', 'INT DEFAULT NULL COMMENT \'小时(0-23)\'');
CALL AddColumnIfNotExists('facebook_insights', 'related_links_requests', 'INT DEFAULT NULL COMMENT \'相关链接请求数\'');
CALL AddColumnIfNotExists('facebook_insights', 'related_links_impressions', 'INT DEFAULT NULL COMMENT \'相关链接展示数\'');
CALL AddColumnIfNotExists('facebook_insights', 'related_links_clicks', 'INT DEFAULT NULL COMMENT \'相关链接点击数\'');
CALL AddColumnIfNotExists('facebook_insights', 'related_links_rpm', 'DECIMAL(20,6) DEFAULT NULL COMMENT \'相关链接RPM\'');
CALL AddColumnIfNotExists('facebook_insights', 'ad_requests', 'INT DEFAULT NULL COMMENT \'广告请求数\'');
CALL AddColumnIfNotExists('facebook_insights', 'matched_ad_requests', 'INT DEFAULT NULL COMMENT \'匹配的广告请求数\'');
CALL AddColumnIfNotExists('facebook_insights', 'ad_impressions', 'INT DEFAULT NULL COMMENT \'广告展示数\'');
CALL AddColumnIfNotExists('facebook_insights', 'ctr', 'DECIMAL(20,6) DEFAULT NULL COMMENT \'点击率\'');
CALL AddColumnIfNotExists('facebook_insights', 'ad_ctr', 'DECIMAL(20,6) DEFAULT NULL COMMENT \'广告点击率\'');
CALL AddColumnIfNotExists('facebook_insights', 'ad_rpm', 'DECIMAL(20,6) DEFAULT NULL COMMENT \'广告RPM\'');
CALL AddColumnIfNotExists('facebook_insights', 'cr', 'DECIMAL(20,6) DEFAULT NULL COMMENT \'转化率\'');
CALL AddColumnIfNotExists('facebook_insights', 'revenue', 'DECIMAL(20,10) DEFAULT NULL COMMENT \'收益\'');
CALL AddColumnIfNotExists('facebook_insights', 'create_time', 'DATETIME DEFAULT NULL COMMENT \'创建时间\'');

-- 创建索引（安全方式）
DELIMITER $$

-- 删除存储过程（如果存在）
DROP PROCEDURE IF EXISTS CreateIndexIfNotExists$$

-- 创建安全添加索引的存储过程
CREATE PROCEDURE CreateIndexIfNotExists(
    IN tableName VARCHAR(128),
    IN indexName VARCHAR(128),
    IN indexDefinition TEXT
)
BEGIN
    DECLARE indexExists INT DEFAULT 0;

    -- 检查索引是否存在
    SELECT COUNT(*) INTO indexExists
    FROM INFORMATION_SCHEMA.STATISTICS
    WHERE TABLE_SCHEMA = DATABASE()
      AND TABLE_NAME = tableName
      AND INDEX_NAME = indexName;

    -- 如果索引不存在，则创建
    IF indexExists = 0 THEN
        SET @sql = CONCAT('CREATE INDEX ', indexName, ' ON ', tableName, ' ', indexDefinition);
        PREPARE stmt FROM @sql;
        EXECUTE stmt;
        DEALLOCATE PREPARE stmt;
        SELECT CONCAT('Created index: ', indexName) as result;
    ELSE
        SELECT CONCAT('Index already exists: ', indexName) as result;
    END IF;
END$$

DELIMITER ;

-- 创建索引
CALL CreateIndexIfNotExists('facebook_insights', 'idx_facebook_insights_campaign_name', '(campaign_name)');
CALL CreateIndexIfNotExists('facebook_insights', 'idx_facebook_insights_platform', '(platform)');
CALL CreateIndexIfNotExists('facebook_insights', 'idx_facebook_insights_country', '(country)');
CALL CreateIndexIfNotExists('facebook_insights', 'idx_facebook_insights_hour', '(hour)');
CALL CreateIndexIfNotExists('facebook_insights', 'idx_facebook_insights_create_time', '(create_time)');
CALL CreateIndexIfNotExists('facebook_insights', 'idx_facebook_insights_platform_country', '(platform, country)');
CALL CreateIndexIfNotExists('facebook_insights', 'idx_facebook_insights_date_platform', '(date, platform)');

-- 清理存储过程
DROP PROCEDURE IF EXISTS AddColumnIfNotExists;
DROP PROCEDURE IF EXISTS CreateIndexIfNotExists;

SELECT 'Safe migration completed: Extended fields added to facebook_insights table' as status;

-- 验证新字段是否添加成功
SELECT 
    COLUMN_NAME,
    DATA_TYPE,
    IS_NULLABLE,
    COLUMN_DEFAULT,
    COLUMN_COMMENT
FROM INFORMATION_SCHEMA.COLUMNS 
WHERE TABLE_SCHEMA = DATABASE() 
  AND TABLE_NAME = 'facebook_insights'
  AND COLUMN_NAME IN (
    'campaign_name', 'platform', 'country', 'hour',
    'related_links_requests', 'related_links_impressions', 'related_links_clicks', 'related_links_rpm',
    'ad_requests', 'matched_ad_requests', 'ad_impressions',
    'ctr', 'ad_ctr', 'ad_rpm', 'cr', 'revenue', 'create_time'
  )
ORDER BY ORDINAL_POSITION;
