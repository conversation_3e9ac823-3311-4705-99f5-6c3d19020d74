#!/bin/bash

# Postback 回调接口测试数据执行脚本
# 使用方法: ./run_test_data.sh [base_url]

BASE_URL="${1:-http://localhost:8080}"
POSTBACK_URL="$BASE_URL/rsoc/postback"
SEDOTMP_URL="$BASE_URL/rsoc/sedotmp/callback"
SETUP_URL="$BASE_URL/rsoc/postback/setup"

echo "=== Postback 回调接口测试数据执行 ==="
echo "基础URL: $BASE_URL"
echo "开始时间: $(date)"
echo ""

# 函数：执行测试并显示结果
run_test() {
    local test_name="$1"
    local curl_command="$2"
    
    echo "执行测试: $test_name"
    echo "命令: $curl_command"
    
    response=$(eval $curl_command 2>/dev/null)
    status_code=$(eval "$curl_command -w '%{http_code}' -o /dev/null -s" 2>/dev/null)
    
    echo "响应: $response"
    echo "状态码: $status_code"
    echo "---"
    echo ""
}

# 1. 通用回调接口测试
echo "### 1. 通用回调接口测试 ($POSTBACK_URL)"
echo ""

run_test "AdTech格式 - 美国桌面端用户" \
    "curl -s '$POSTBACK_URL?campaign=ADT001&click_id=us_desktop_001&payout=2.50&country=US&zip=10001&os_type=WINDOWS&browser=CHROME&device_type=DESKTOP&device_brand=DELL&s1=AdSetUS001&s2=CreativeDesktop&s3=AudienceRetargeting'"

run_test "AdTech格式 - 英国移动端用户" \
    "curl -s '$POSTBACK_URL?campaign=ADT002&click_id=uk_mobile_001&payout=1.75&country=GB&zip=SW1A&os_type=ANDROID&browser=CHROME&device_type=MOBILE&device_brand=SAMSUNG&s1=AdSetUK001&s2=CreativeMobile&s3=AudienceLookalike'"

run_test "混合格式 - 加拿大用户" \
    "curl -s '$POSTBACK_URL?campaign=MIX001&click_id=ca_user_001&payout=3.20&country=CA&zip=M5V&os_type=MACOS&browser=SAFARI&device_type=DESKTOP&device_brand=APPLE&s1=AdSetCA001&subid1=TestSubID1&subid2=TestSubID2'"

run_test "POST方式 - 澳大利亚用户" \
    "curl -s -X POST '$POSTBACK_URL' -H 'Content-Type: application/x-www-form-urlencoded' -d 'campaign=POST001&click_id=post_test_001&payout=1.95&country=AU&zip=2000&os_type=WINDOWS&browser=EDGE&device_type=DESKTOP&device_brand=HP&s1=AdSetAU001&s2=CreativeVideo&s3=AudienceInterest'"

run_test "最小参数集" \
    "curl -s '$POSTBACK_URL?campaign=MIN001&payout=0.85'"

# 2. SedoTMP 专用回调接口测试
echo "### 2. SedoTMP 专用回调接口测试 ($SEDOTMP_URL)"
echo ""

run_test "美国用户 - 完整参数" \
    "curl -s '$SEDOTMP_URL?campaign=SEDO001&click_id=us_full_001&epayout=1.25&country=US&country_name=United%20States&state=CA&city=Los%20Angeles&zip=90001&os_type=WINDOWS&browser=CHROME&device_type=DESKTOP&device_brand=DELL&subid1=AdSetUS001&subid2=CreativeDesktop&subid3=AudienceRetargeting&subid4=CampaignTypeConversion&subid5=SeasonSummer'"

run_test "英国移动端用户" \
    "curl -s '$SEDOTMP_URL?campaign=SEDO002&click_id=uk_mobile_002&epayout=0.85&country=GB&country_name=United%20Kingdom&state=London&city=London&zip=SW1A&os_type=ANDROID&browser=CHROME&device_type=MOBILE&device_brand=SAMSUNG&subid1=AdSetUK001&subid2=CreativeMobile&subid3=AudienceLookalike'"

run_test "德国用户 - iOS设备" \
    "curl -s '$SEDOTMP_URL?campaign=SEDO003&click_id=de_ios_001&epayout=2.10&country=DE&country_name=Germany&state=Bavaria&city=Munich&zip=80331&os_type=IOS&browser=SAFARI&device_type=MOBILE&device_brand=APPLE&subid1=AdSetDE001&subid2=CreativeVideo&subid3=AudienceInterest&subid4=CampaignTypeBranding&subid5=SeasonWinter'"

run_test "加拿大用户 - POST方式" \
    "curl -s -X POST '$SEDOTMP_URL' -H 'Content-Type: application/x-www-form-urlencoded' -d 'campaign=SEDO004&click_id=ca_post_001&epayout=1.60&country=CA&country_name=Canada&state=Ontario&city=Toronto&zip=M5V&os_type=MACOS&browser=SAFARI&device_type=DESKTOP&device_brand=APPLE&subid1=AdSetCA001&subid2=CreativeImage&subid3=AudienceCustom'"

run_test "澳大利亚用户 - 基础参数" \
    "curl -s '$SEDOTMP_URL?campaign=SEDO005&click_id=au_basic_001&epayout=0.95&country=AU&subid1=AdSetAU001&subid2=CreativeBasic'"

run_test "法国用户 - 平板设备" \
    "curl -s '$SEDOTMP_URL?campaign=SEDO006&click_id=fr_tablet_001&epayout=1.40&country=FR&country_name=France&state=Ile-de-France&city=Paris&zip=75001&os_type=ANDROID&browser=CHROME&device_type=TABLET&device_brand=SAMSUNG&subid1=AdSetFR001&subid2=CreativeTablet&subid3=AudienceRetargeting&subid4=CampaignTypeApp&subid5=SeasonSpring'"

# 3. 配置设置接口测试
echo "### 3. 配置设置接口测试 ($SETUP_URL)"
echo ""

run_test "AdTech平台配置" \
    "curl -s -X POST '$SETUP_URL' -H 'Content-Type: application/x-www-form-urlencoded' -d 'security_key=adtech_test_key_123&domain_name=http://callback.adtech-platform.com/postback&campaign=campaign_id&click_id=click_tracking_id&payout=revenue_amount&country=geo_country&zip=geo_zip&os_type=user_os&browser=user_browser&device_type=user_device&device_brand=device_manufacturer&s1=custom_param_1&s2=custom_param_2&s3=custom_param_3'"

# 4. 批量测试
echo "### 4. 批量测试"
echo ""

echo "执行批量测试 - 通用回调接口..."
campaigns=("BATCH001" "BATCH002" "BATCH003" "BATCH004" "BATCH005")
payouts=("1.25" "2.50" "0.85" "3.75" "1.95")
countries=("US" "GB" "DE" "CA" "AU")

for i in {0..4}; do
    echo "测试 ${campaigns[$i]}..."
    response=$(curl -s "$POSTBACK_URL?campaign=${campaigns[$i]}&click_id=batch_test_$i&payout=${payouts[$i]}&country=${countries[$i]}")
    echo "响应: $response"
done

echo ""
echo "执行批量测试 - SedoTMP专用接口..."
sedo_campaigns=("SBATCH001" "SBATCH002" "SBATCH003" "SBATCH004" "SBATCH005")
epayouts=("0.123" "0.456" "0.789" "1.234" "0.567")

for i in {0..4}; do
    echo "测试 ${sedo_campaigns[$i]}..."
    response=$(curl -s "$SEDOTMP_URL?campaign=${sedo_campaigns[$i]}&click_id=sedo_batch_$i&epayout=${epayouts[$i]}&country=${countries[$i]}")
    echo "响应: $response"
done

# 5. 错误测试用例
echo ""
echo "### 5. 错误测试用例"
echo ""

run_test "缺少参数测试" \
    "curl -s '$POSTBACK_URL'"

run_test "无效收益格式" \
    "curl -s '$POSTBACK_URL?campaign=ERROR001&payout=invalid_number'"

run_test "特殊字符测试" \
    "curl -s '$POSTBACK_URL?campaign=ERROR003&click_id=test%20with%20spaces&payout=1.00&country=US'"

echo ""
echo "=== 测试完成 ==="
echo "结束时间: $(date)"
echo ""
echo "数据验证SQL查询:"
echo "SELECT campaign_id, real_price, date, update_time FROM facebook_insights WHERE campaign_id LIKE 'ADT%' OR campaign_id LIKE 'SEDO%' OR campaign_id LIKE 'BATCH%' OR campaign_id LIKE 'SBATCH%' ORDER BY update_time DESC LIMIT 20;"
echo ""
echo "清理测试数据SQL:"
echo "DELETE FROM facebook_insights WHERE campaign_id LIKE 'ADT%' OR campaign_id LIKE 'SEDO%' OR campaign_id LIKE 'BATCH%' OR campaign_id LIKE 'SBATCH%' OR campaign_id LIKE 'ERROR%' OR campaign_id LIKE 'MIN%' OR campaign_id LIKE 'MIX%' OR campaign_id LIKE 'POST%';"
