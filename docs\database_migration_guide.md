# Facebook Insights 数据库迁移指南

## 概述

本指南说明如何为 `facebook_insights` 表添加扩展字段，以支持新的JSON数据格式。迁移采用向后兼容的方式，不会影响现有功能。

## 新增字段列表

| 字段名 | 类型 | 说明 | 对应JSON字段 |
|--------|------|------|--------------|
| `campaign_name` | VARCHAR(255) | 广告系列名称 | campaign_name |
| `platform` | VARCHAR(100) | 平台名称 | platform |
| `country` | VARCHAR(10) | 国家代码 | country |
| `hour` | INT | 小时(0-23) | hour |
| `related_links_requests` | INT | 相关链接请求数 | relatedLinksRequests |
| `related_links_impressions` | INT | 相关链接展示数 | relatedLinksImpressions |
| `related_links_clicks` | INT | 相关链接点击数 | relatedLinksClicks |
| `related_links_rpm` | DECIMAL(20,6) | 相关链接RPM | relatedLinksRpm |
| `ad_requests` | INT | 广告请求数 | adRequests |
| `matched_ad_requests` | INT | 匹配的广告请求数 | matchedAdRequests |
| `ad_impressions` | INT | 广告展示数 | adImpressions |
| `ctr` | DECIMAL(20,6) | 点击率 | ctr |
| `ad_ctr` | DECIMAL(20,6) | 广告点击率 | adCtr |
| `ad_rpm` | DECIMAL(20,6) | 广告RPM | adRpm |
| `cr` | DECIMAL(20,6) | 转化率 | cr |
| `revenue` | DECIMAL(20,10) | 收益 | revenue |
| `create_time` | DATETIME | 创建时间 | create_time |

## 迁移方式

### 方式1：自动迁移（推荐）

应用程序启动时，GORM会自动检测模型变化并添加新字段：

```bash
# 重启应用程序
go run main.go
```

GORM会自动执行以下操作：
- 检测 `FacebookInsights` 模型的新字段
- 生成并执行 `ALTER TABLE` 语句
- 添加必要的索引

### 方式2：手动执行SQL脚本

#### Linux/macOS 环境

```bash
# 设置数据库连接
export MYSQL_URL='user:password@tcp(host:port)/database?charset=utf8mb4&parseTime=True&loc=Local'

# 执行迁移
./scripts/migrate_facebook_insights.sh migrate

# 验证迁移结果
./scripts/migrate_facebook_insights.sh verify

# 如需回滚
./scripts/migrate_facebook_insights.sh rollback
```

#### Windows 环境

```powershell
# 设置数据库连接
$env:MYSQL_URL='user:password@tcp(host:port)/database?charset=utf8mb4&parseTime=True&loc=Local'

# 执行迁移
.\scripts\migrate_facebook_insights.ps1 migrate

# 验证迁移结果
.\scripts\migrate_facebook_insights.ps1 verify

# 如需回滚
.\scripts\migrate_facebook_insights.ps1 rollback
```

#### 直接执行SQL文件

**简单迁移脚本（推荐用于新环境）：**
```bash
# 执行迁移
mysql -h host -u user -p database < migrations/001_simple_add_facebook_insights_extended_fields.sql

# 回滚（如需要）
mysql -h host -u user -p database < migrations/001_rollback_facebook_insights_extended_fields.sql
```

**标准迁移脚本：**
```bash
# 执行迁移
mysql -h host -u user -p database < migrations/001_add_facebook_insights_extended_fields.sql
```

**安全迁移脚本（推荐用于生产环境）：**
```bash
# 安全执行迁移（自动检查字段是否存在）
mysql -h host -u user -p database < migrations/001_safe_add_facebook_insights_extended_fields.sql
```

**脚本区别说明：**
- `001_simple_add_facebook_insights_extended_fields.sql`：最简单的迁移，兼容所有MySQL版本
- `001_add_facebook_insights_extended_fields.sql`：标准迁移，包含详细注释
- `001_safe_add_facebook_insights_extended_fields.sql`：安全迁移，使用存储过程检查字段是否存在

**推荐使用顺序：**
1. 首选：简单迁移脚本（最兼容）
2. 备选：安全迁移脚本（生产环境）
3. 最后：标准迁移脚本（开发环境）

## 验证迁移结果

### 检查表结构

```sql
-- 查看新增字段
SELECT 
    COLUMN_NAME,
    DATA_TYPE,
    IS_NULLABLE,
    COLUMN_DEFAULT,
    COLUMN_COMMENT
FROM INFORMATION_SCHEMA.COLUMNS 
WHERE TABLE_SCHEMA = 'your_database' 
  AND TABLE_NAME = 'facebook_insights'
  AND COLUMN_NAME IN (
    'campaign_name', 'platform', 'country', 'hour',
    'related_links_requests', 'related_links_impressions', 'related_links_clicks', 'related_links_rpm',
    'ad_requests', 'matched_ad_requests', 'ad_impressions',
    'ctr', 'ad_ctr', 'ad_rpm', 'cr', 'revenue', 'create_time'
  )
ORDER BY ORDINAL_POSITION;
```

### 检查索引

```sql
-- 查看新增索引
SHOW INDEX FROM facebook_insights 
WHERE Key_name IN (
    'idx_facebook_insights_campaign_name',
    'idx_facebook_insights_platform',
    'idx_facebook_insights_country',
    'idx_facebook_insights_hour',
    'idx_facebook_insights_create_time',
    'idx_facebook_insights_platform_country',
    'idx_facebook_insights_date_platform'
);
```

## 测试新功能

### 测试数据插入

```bash
# 测试包含新字段的postback数据
curl -X POST http://localhost:8080/rsoc/postback \
  -H "Content-Type: application/json" \
  -d '{
    "campaign": "test123",
    "campaign_name": "Apply for credit cards",
    "platform": "HighEndMobile",
    "country": "US",
    "hour": "0",
    "relatedLinksRequests": "1",
    "adRequests": "1",
    "revenue": "0.6016",
    "create_time": "2025-01-01 16:00:02"
  }'
```

### 验证数据存储

```sql
-- 查看最新插入的数据
SELECT 
    campaign_id,
    campaign_name,
    platform,
    country,
    revenue,
    create_time,
    update_time
FROM facebook_insights 
WHERE campaign_name IS NOT NULL 
ORDER BY update_time DESC 
LIMIT 5;
```

## 注意事项

### 数据兼容性

1. **现有数据不受影响**：新字段默认为 NULL，不影响现有记录
2. **现有查询继续有效**：所有现有的SQL查询和应用逻辑保持不变
3. **渐进式迁移**：可以逐步开始使用新字段

### 性能考虑

1. **索引优化**：已为常用查询字段添加索引
2. **存储空间**：新字段为可选，只在有数据时占用空间
3. **查询性能**：复合索引优化了多字段查询

### 回滚策略

1. **备份重要**：执行迁移前自动备份表结构
2. **数据丢失警告**：回滚会删除新字段中的所有数据
3. **测试环境先行**：建议先在测试环境验证

## 故障排除

### 常见问题

1. **权限不足**
   ```
   ERROR 1142: ALTER command denied to user
   ```
   解决：确保数据库用户有 ALTER 权限

2. **字段已存在**
   ```
   ERROR 1060: Duplicate column name
   ```
   解决：字段已存在，迁移可能已执行过

3. **连接失败**
   ```
   ERROR 2003: Can't connect to MySQL server
   ```
   解决：检查 MYSQL_URL 环境变量和网络连接

### 获取帮助

- 查看应用日志：检查 GORM 迁移日志
- 数据库日志：查看 MySQL 错误日志
- 联系开发团队：提供详细的错误信息

## 相关文件

- 迁移脚本：`migrations/001_add_facebook_insights_extended_fields.sql`
- 回滚脚本：`migrations/001_rollback_facebook_insights_extended_fields.sql`
- 执行脚本：`scripts/migrate_facebook_insights.sh` (Linux/macOS)
- 执行脚本：`scripts/migrate_facebook_insights.ps1` (Windows)
- 模型文件：`model/facebook_insights.go`
- 配置文件：`config/mysql.go`
