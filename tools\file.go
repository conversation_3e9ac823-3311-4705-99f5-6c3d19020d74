package tools

import (
	"fmt"
	"os"
	"path/filepath"
	"strings"
)

func GetFilesPath(root string, replace string) []string {
	var files []string

	//root := "./server/admin/static"
	err := filepath.Walk(root, func(path string, info os.FileInfo, err error) error {
		if info.IsDir() {
			return nil
		}
		path = strings.ReplaceAll(path, replace, "")
		files = append(files, path)
		return nil
	})
	if err != nil {
		panic(err)
	}
	for _, file := range files {
		fmt.Println(file)
	}
	return files
}
