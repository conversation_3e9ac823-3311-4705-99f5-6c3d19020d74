package main

import (
	"log"
	"path/filepath"
	"rsoc-system-go/config"
	"rsoc-system-go/tools"
	"testing"
)

func TestQiniuUploadByLocalFile(t *testing.T) {

	filesPath := tools.GetFilesPath("./server/admin/static", "server\\admin\\")
	for _, item := range filesPath {
		qiniu := config.Qiniu{}
		file, err := qiniu.UploadByLocalFile(filepath.ToSlash(item), ".\\server\\admin\\"+item)
		if err != nil {
			log.Println(err)
		}
		log.Println(file)
	}
	filesPathSf := tools.GetFilesPath("./server/admin/sa-frame", "server\\admin\\")
	for _, item := range filesPathSf {
		qiniu := config.Qiniu{}
		file, err := qiniu.UploadByLocalFile(filepath.ToSlash(item), ".\\server\\admin\\"+item)
		if err != nil {
			log.Println(err)
		}
		log.Println(file)
	}

}

func TestFilePath(t *testing.T) {
	filesPath := tools.GetFilesPath("./server/admin/static", "server\\admin\\")
	for _, item := range filesPath {
		log.Println(".\\server\\admin\\"+item, filepath.ToSlash(item))
	}
	filesPathSf := tools.GetFilesPath("./server/admin/sa-frame", "server\\admin\\")
	for _, item := range filesPathSf {
		log.Println(".\\server\\admin\\"+item, filepath.ToSlash(item))
	}
}
