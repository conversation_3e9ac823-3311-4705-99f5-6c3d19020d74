package config

import (
	"crypto/rand"
	"crypto/sha256"
	"crypto/subtle"
	"encoding/base64"
)

const saltSize = 16 // 盐的长度,可以根据需要调整

// HashPassword 使用随机盐对密码进行哈希并返回哈希值和盐
func HashPassword(password string) (string, string, error) {
	salt, err := generateSalt(saltSize)
	if err != nil {
		return "", "", err
	}

	hash := sha256.Sum256([]byte(salt + password))
	hashStr := base64.URLEncoding.EncodeToString(hash[:])

	return hashStr, salt, nil
}

// ComparePasswords 比较输入的密码与哈希值和盐是否匹配
func ComparePasswords(password, hash, salt string) bool {
	hashBytes, err := base64.URLEncoding.DecodeString(hash)
	if err != nil {
		return false
	}

	hashInput := sha256.Sum256([]byte(salt + password))

	return subtle.ConstantTimeCompare(hashBytes, hashInput[:]) == 1
}

// generateSalt 生成指定长度的随机盐
func generateSalt(size int) (string, error) {
	buf := make([]byte, size)
	_, err := rand.Read(buf)
	if err != nil {
		return "", err
	}

	return base64.URLEncoding.EncodeToString(buf), nil
}
