package store

import (
	"context"
	"fmt"
	"log"
	"sync"
	"time"

	"github.com/go-redis/redis/v8"
)

// RedisInstance Redis实例标识
type RedisInstance string

// RedisConfig Redis配置
type RedisConfig struct {
	Instance RedisInstance
	URL      string
	PoolSize int
	Client   *redis.Client
}

// redisManager Redis管理器
type redisManager struct {
	mu          sync.RWMutex
	configs     map[RedisInstance]RedisConfig
	clients     map[RedisInstance]*redis.Client
	defaultInst RedisInstance
}

var (
	rManager *redisManager
	rOnce    sync.Once
)

// GetRedisManager 获取Redis管理器单例
func GetRedisManager() *redisManager {
	rOnce.Do(func() {
		rManager = &redisManager{
			configs:     make(map[RedisInstance]RedisConfig),
			clients:     make(map[RedisInstance]*redis.Client),
			defaultInst: RedisInstance("main"),
		}
	})
	return rManager
}

// RegisterRedis 注册Redis配置
func (m *redisManager) RegisterRedis(config RedisConfig) error {
	m.mu.Lock()
	defer m.mu.Unlock()

	if config.Instance == "" {
		return fmt.Errorf("redis instance name cannot be empty")
	}

	if config.URL == "" {
		return fmt.Errorf("redis URL cannot be empty")
	}

	if config.PoolSize <= 0 {
		config.PoolSize = 10 // 默认连接池大小
	}

	m.configs[config.Instance] = config
	return nil
}

// ConnectRedis 连接到指定的Redis实例
func (m *redisManager) ConnectRedis(instance RedisInstance) error {
	m.mu.Lock()
	defer m.mu.Unlock()

	config, exists := m.configs[instance]
	if !exists {
		return fmt.Errorf("redis instance %s not registered", instance)
	}

	opts, err := redis.ParseURL(config.URL)
	if err != nil {
		return fmt.Errorf("failed to parse Redis URL for %s: %v", instance, err)
	}

	// 配置连接池
	opts.PoolSize = config.PoolSize
	opts.MinIdleConns = config.PoolSize / 2
	opts.MaxRetries = 3
	opts.ReadTimeout = 3 * time.Second
	opts.WriteTimeout = 3 * time.Second
	opts.PoolTimeout = 4 * time.Second
	opts.IdleTimeout = 5 * time.Minute
	opts.IdleCheckFrequency = 1 * time.Minute

	client := redis.NewClient(opts)

	// 测试连接
	ctx, cancel := context.WithTimeout(context.Background(), 5*time.Second)
	defer cancel()

	if _, err := client.Ping(ctx).Result(); err != nil {
		return fmt.Errorf("failed to connect to Redis instance %s: %v", instance, err)
	}

	m.clients[instance] = client
	config.Client = client
	m.configs[instance] = config

	// 如果是默认实例，设置全局客户端
	if instance == m.defaultInst {
		RedisClient = client
	}

	return nil
}

// GetRedisClient 获取指定Redis实例的客户端
func (m *redisManager) GetRedisClient(instance RedisInstance) *redis.Client {
	m.mu.RLock()
	defer m.mu.RUnlock()

	if client, exists := m.clients[instance]; exists {
		return client
	}
	return nil
}

// GetDefaultRedisClient 获取默认Redis客户端
func (m *redisManager) GetDefaultRedisClient() *redis.Client {
	return m.GetRedisClient(m.defaultInst)
}

// CloseRedis 关闭指定的Redis实例连接
func (m *redisManager) CloseRedis(instance RedisInstance) error {
	m.mu.Lock()
	defer m.mu.Unlock()

	if client, exists := m.clients[instance]; exists {
		if err := client.Close(); err != nil {
			return err
		}
		delete(m.clients, instance)
		delete(m.configs, instance)
		return nil
	}
	return fmt.Errorf("redis instance %s not found", instance)
}

// CloseAllRedis 关闭所有Redis连接
func (m *redisManager) CloseAllRedis() {
	m.mu.Lock()
	defer m.mu.Unlock()

	for instance, client := range m.clients {
		if err := client.Close(); err != nil {
			log.Printf("Failed to close Redis instance %s: %v", instance, err)
		}
	}
	m.clients = make(map[RedisInstance]*redis.Client)
	m.configs = make(map[RedisInstance]RedisConfig)
}

// SetDefaultRedis 设置默认Redis实例
func (m *redisManager) SetDefaultRedis(instance RedisInstance) {
	m.mu.Lock()
	defer m.mu.Unlock()
	m.defaultInst = instance
}

// GetAllRedisInstances 获取所有已注册的Redis实例
func (m *redisManager) GetAllRedisInstances() []RedisInstance {
	m.mu.RLock()
	defer m.mu.RUnlock()

	instances := make([]RedisInstance, 0, len(m.configs))
	for instance := range m.configs {
		instances = append(instances, instance)
	}
	return instances
}
