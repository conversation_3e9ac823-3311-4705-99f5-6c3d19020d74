import{_ as $,u as B,r as L,o as S,M as r,c as v,b as s,d,w as p,B as Y,e as l,t as u,f as _,a_ as C,a$ as A,k as M,C as O,m as E,h as P,i as y,D as x}from"./index-DlVegDiC.js";import{i as g}from"./index-u3wYLQ4o.js";import{D as N}from"./dayjs-BF6WblrD.js";import{S as j,a as z}from"./index-CSU5nP3m.js";const G={class:"main"},R={class:"echarts-box"},U={class:"filter"},V={class:"filter_box"},F={class:"filter_item"},J={style:{display:"flex","align-items":"center"}},q={class:"filter"},H={class:"filter_box"},K={class:"filter_item"},Q={style:{display:"flex","align-items":"center"}},W={__name:"echarts",setup(X){const D="",f="",m=B(),e=L({replayDate:"",total:0,replayDateTask:"",totalTask:0,taskList:[],selectedTaskId:void 0});S(()=>{T()});const T=()=>{fetch(`${D}/Task/list`,{method:"POST",body:JSON.stringify({page:1,limit:1e3,status:1}),headers:{token:m.token}}).then(t=>t.json()).then(t=>{t.code==1?e.taskList=t.data.data:r.error({title:t.msg})}).catch(t=>{r.error({title:"服务器错误",content:`${t}`})})},h=()=>{console.log(y(e.replayDate).format("YYYY-MM-DD")),fetch(`${f}/replay/line?date=${y(e.replayDate).format("YYYY-MM-DD")}`,{method:"GET",headers:{token:m.token}}).then(t=>t.json()).then(async t=>{t.data==null&&r.error({title:"服务器错误",content:`${error}`}),await x();let a=g(document.getElementById("replay")),n;e.total=t.data.reduce((o,c)=>o+parseInt(c.number),0),n={title:{text:"重放曲线",left:"center"},tooltip:{trigger:"axis"},xAxis:{type:"category",boundaryGap:!1,data:t.data.map(o=>o.addtime)},yAxis:{type:"value"},series:[{data:t.data.map(o=>o.number),type:"line",areaStyle:{}}]},n&&a.setOption(n),window.onresize=function(){a.resize()}}).catch(t=>{r.error({title:"服务器错误",content:`${t}`})})},k=()=>{fetch(`${f}/replay/task-line?task_id=${e.selectedTaskId}`,{method:"GET",headers:{token:m.token}}).then(t=>t.json()).then(async t=>{t.data==null&&r.error({title:"服务器错误",content:`${error}`}),await x();let a=g(document.getElementById("replayTask")),n;e.totalTask=t.data.reduce((o,c)=>o+parseInt(c.number),0),n={title:{text:"重放任务曲线",left:"center"},tooltip:{trigger:"axis"},xAxis:{type:"category",boundaryGap:!1,data:t.data.map(o=>o.addtime)},yAxis:{type:"value"},series:[{data:t.data.map(o=>o.number),type:"line",areaStyle:{}}]},n&&a.setOption(n),window.onresize=function(){a.resize()}}).catch(t=>{r.error({title:"服务器错误",content:`${t}`})})},b=()=>{e.replayDate&&(e.replayDate=y(e.replayDate).subtract(1,"day"),k())},w=()=>{e.replayDate&&(e.replayDate=y(e.replayDate).add(1,"day"),h())};return(t,a)=>{const n=Y,o=N,c=z,I=j;return _(),v("div",G,[s("div",R,[s("div",U,[s("div",V,[s("div",F,[a[2]||(a[2]=s("p",null,"选择日期：",-1)),s("div",J,[d(n,{type:"link",onClick:b},{default:p(()=>[d(l(C))]),_:1}),d(o,{onChange:h,value:l(e).replayDate,"onUpdate:value":a[0]||(a[0]=i=>l(e).replayDate=i)},null,8,["value"]),d(n,{type:"link",onClick:w},{default:p(()=>[d(l(A))]),_:1})])])])]),a[4]||(a[4]=s("div",{id:"replay",style:{width:"100%",height:"300px",marginBottom:"30px"}},null,-1)),s("div",null,[s("p",null,"数量："+u(l(e).total),1)]),s("div",q,[s("div",H,[s("div",K,[a[3]||(a[3]=s("p",null,"选择任务：",-1)),s("div",Q,[d(I,{value:l(e).selectedTaskId,"onUpdate:value":a[1]||(a[1]=i=>l(e).selectedTaskId=i),style:{width:"200px"},placeholder:"请选择任务",onChange:k},{default:p(()=>[(_(!0),v(M,null,O(l(e).taskList,i=>(_(),E(c,{key:i.id,value:i.task_id},{default:p(()=>[P(u(i.id),1)]),_:2},1032,["value"]))),128))]),_:1},8,["value"])])])])]),a[5]||(a[5]=s("div",{id:"replayTask",style:{width:"100%",height:"300px",marginBottom:"30px"}},null,-1)),s("div",null,[s("p",null,"数量："+u(l(e).totalTask),1)])])])}}},st=$(W,[["__scopeId","data-v-91720355"]]);export{st as default};
