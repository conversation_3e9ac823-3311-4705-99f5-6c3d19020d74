package jobs

import (
	"context"
	"database/sql"
	"encoding/json"
	"log"
	"math/rand"
	"os"
	"rsoc-system-go/dao"
	"rsoc-system-go/middleware/pkg/redis"
	"rsoc-system-go/model"
	"rsoc-system-go/store"
	"rsoc-system-go/tools"
	"rsoc-system-go/tools/constants"
	"strconv"
	"sync"
	"time"
)

const GenDataSendJobClass = "gen_data_send_job"

// 全局DAO实例
var (
	taskDao       *dao.TaskDao
	autoTaskDao   *dao.AutoTaskDao
	replayTaskDao *dao.ReplayTaskDao
	daoOnce       sync.Once
)

func initDAOs() {
	daoOnce.Do(func() {
		taskDao = dao.NewTaskDao(store.DB)
		autoTaskDao = dao.NewAutoTaskDao(store.DB)
		replayTaskDao = dao.NewReplayTaskDao(store.DB)
	})
}

// 任务数据结构
type taskData struct {
	task     *model.ReplayTask
	taskInfo *model.Task
	count    int
}

func GenDataSendJobFunc(ctx context.Context) error {
	// 初始化DAO实例
	initDAOs()

	// 查询任务列表
	var taskParam model.ReplayTask
	taskParam.Status = 1
	taskParam.TaskCreateDate = time.Now().Format(time.DateOnly)
	replayTaskList, err := replayTaskDao.List(&taskParam, map[string]interface{}{
		"status": map[string]interface{}{
			"eq": 1,
		},
	})
	if err != nil {
		log.Printf("获取任务列表失败: %v", err)
		return err
	}

	if len(replayTaskList) == 0 {
		return nil
	}

	// 获取Redis客户端
	indexdbStr := os.Getenv("redisdb")
	indexdb, _ := strconv.Atoi(indexdbStr)
	client := redis.SelectDB(indexdb)

	// 创建任务通道
	taskChan := make(chan taskData)
	doneChan := make(chan struct{})

	// 启动5个工作协程
	for i := 0; i < 5; i++ {
		go worker(ctx, taskChan, doneChan)
	}

	// 收集并发送任务
	go func() {
		for _, task := range replayTaskList {
			taskInfo, err := taskDao.GetByID(task.TaskDataId)
			if err != nil {
				log.Printf("获取任务数据失败: %v", err)
				continue
			}

			format := time.Now().Format(time.TimeOnly)
			number, err := client.HGet(ctx, "task:"+task.TaskID, format).Result()
			if err != nil {
				log.Printf("获取任务数量失败: %v", err)
				continue
			}

			count, err := strconv.Atoi(number)
			if err != nil {
				log.Printf("解析任务数量失败: %v", err)
				continue
			}

			// 限制每个任务最多处理50条数据
			if count > 50 {
				count = 50
			}

			select {
			case taskChan <- taskData{task: task, taskInfo: taskInfo, count: count}:
			case <-ctx.Done():
				close(taskChan)
				return
			}
		}
		close(taskChan)
	}()

	// 等待所有工作协程完成
	for i := 0; i < 5; i++ {
		<-doneChan
	}

	return nil
}

func worker(ctx context.Context, taskChan chan taskData, doneChan chan struct{}) {
	for td := range taskChan {
		for i := 0; i < td.count; i++ {
			select {
			case <-ctx.Done():
				doneChan <- struct{}{}
				return
			default:
				processTask(td.task, td.taskInfo)
			}
		}
	}
	doneChan <- struct{}{}
}

func processTask(task *model.ReplayTask, taskData *model.Task) {
	// 生成随机时间
	formattedDate := time.Now().Format(time.DateOnly)
	randomTime := time.Now().Add(time.Duration(rand.Intn(1000)) * time.Millisecond)

	// 生成traffic data
	trafficData := map[string]interface{}{
		"campaignid": taskData.CampaginID.String,
		"adsetid":    taskData.CampaginID.String,
		"adid":       taskData.CampaginID.String,
		"creativeid": 0,
		"cid":        generateCID(),
		"placement":  getRandomPlacement(),
	}
	trafficDataBytes, _ := json.Marshal(trafficData)

	// 获取UA
	ua := getUA()

	// 创建自动任务
	autoTask := &model.AutoTask{
		TaskID:      sql.NullString{String: task.TaskID, Valid: true},
		Status:      sql.NullInt32{Int32: 1, Valid: true},
		AddTime:     sql.NullString{String: randomTime.Format(time.DateTime), Valid: true},
		Date:        sql.NullString{String: formattedDate, Valid: true},
		ClickID:     sql.NullString{String: tools.Generate40UUID(), Valid: true},
		TrafficData: sql.NullString{String: string(trafficDataBytes), Valid: true},
		Country:     sql.NullString{String: "US", Valid: true},
		Ref:         sql.NullString{String: "https://m.facebook.com", Valid: true},
		Traffic:     sql.NullString{String: "facebook", Valid: true},
		Viewport:    sql.NullString{String: ua["viewport"], Valid: true},
		UserAgent:   sql.NullString{String: ua["useragent"], Valid: true},
		Lang:        sql.NullString{String: getRandomLanguage(), Valid: true},
		CampaignID:  taskData.CampaginID,
		TrafficURL:  taskData.TrafficURL,
	}

	if err := autoTaskDao.Create(autoTask); err != nil {
		log.Printf("创建自动任务失败: %v", err)
	}
}

func getRandomPlacement() string {
	placements := []struct {
		name string
		prob float64
	}{
		{"Facebook_Instream_Video", 0.04},
		{"Facebook_Marketplace", 19.02},
		{"Facebook_Mobile_Feed", 26.80},
		{"Facebook_Mobile_Reels", 42.03},
		{"Facebook_Stories", 1.42},
		{"Others", 10.68},
	}

	r := rand.Float64() * 100
	var sum float64
	for _, p := range placements {
		sum += p.prob
		if r <= sum {
			return p.name
		}
	}
	return "Facebook_Mobile_Reels"
}

func generateCID() string {
	cid, err := tools.NewStringGenerator().GenerateString(116)
	if err != nil {
		log.Printf("生成CID失败: %v", err)
		return ""
	}
	return cid
}

func getUA() map[string]string {
	uaIndex := rand.Intn(82) + 1
	key := "ua" + strconv.Itoa(uaIndex)
	count := constants.UAFileCountMap[key]
	val := rand.Intn(count) + 1

	scan, err := tools.GetRandomFromRedis(key, strconv.Itoa(val))
	if err != nil {
		return map[string]string{"viewport": "", "useragent": ""}
	}

	var ua map[string]string
	if err := json.Unmarshal([]byte(scan), &ua); err != nil {
		return map[string]string{"viewport": "", "useragent": ""}
	}
	return ua
}

func getRandomLanguage() string {
	languages := []struct {
		lang string
		prob float64
	}{
		{"en-US,en;q=0.9", 0.85},
		{"es-US,es;q=0.9,en-US;q=0.8,en;q=0.7", 0.10},
		{"es-US,es-419;q=0.9,es;q=0.8", 0.05},
	}

	r := rand.Float64()
	var sum float64
	for _, l := range languages {
		sum += l.prob
		if r <= sum {
			return l.lang
		}
	}
	return "en-US,en;q=0.9"
}
