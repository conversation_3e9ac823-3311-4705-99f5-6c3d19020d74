# Google 报告同步 API 使用文档

## 概述

Google 报告同步 API 用于从 AdTech API 获取 Google 收益报告数据，并自动同步到本地的 `facebook_insights` 表中。支持单日同步、日期范围同步和自动同步功能。

## API 端点

### 1. 同步指定日期的报告数据

**请求方式**: `GET`  
**请求路径**: `/google/syncReportData`

#### 请求参数

| 参数名 | 类型 | 必填 | 说明 |
|--------|------|------|------|
| security_key | string | 是 | 授权密钥 |
| data_date | string | 是 | 数据日期，格式：YYYY-MM-DD |
| limit | integer | 否 | 分页限制，默认30 |
| page | integer | 否 | 页码，默认1 |

#### 示例请求

```bash
curl -X GET "http://localhost:8080/google/syncReportData?security_key=your_key&data_date=2025-01-01&limit=50&page=1"
```

#### 响应格式

```json
{
    "code": 200,
    "msg": "同步完成",
    "time": **********,
    "synced_count": 25,
    "total_count": 25,
    "error_count": 0
}
```

### 2. 按日期范围同步报告数据

**请求方式**: `GET`  
**请求路径**: `/google/syncReportDataRange`

#### 请求参数

| 参数名 | 类型 | 必填 | 说明 |
|--------|------|------|------|
| security_key | string | 是 | 授权密钥 |
| start_date | string | 是 | 开始日期，格式：YYYY-MM-DD |
| end_date | string | 是 | 结束日期，格式：YYYY-MM-DD |

#### 示例请求

```bash
curl -X GET "http://localhost:8080/google/syncReportDataRange?security_key=your_key&start_date=2025-01-01&end_date=2025-01-07"
```

#### 响应格式

```json
{
    "code": 200,
    "msg": "批量同步完成",
    "time": **********,
    "synced_count": 150,
    "total_count": 175,
    "error_count": 25
}
```

### 3. 自动同步最近7天的报告数据

**请求方式**: `GET`  
**请求路径**: `/google/autoSyncReportData`

#### 请求参数

| 参数名 | 类型 | 必填 | 说明 |
|--------|------|------|------|
| security_key | string | 是 | 授权密钥 |

#### 示例请求

```bash
curl -X GET "http://localhost:8080/google/autoSyncReportData?security_key=your_key"
```

#### 响应格式

```json
{
    "code": 200,
    "msg": "批量同步完成",
    "time": **********,
    "synced_count": 200,
    "total_count": 210,
    "error_count": 10
}
```

## 数据映射说明

Google 报告数据会按以下规则映射到 `facebook_insights` 表：

| Google 字段 | FacebookInsights 字段 | 说明 |
|-------------|----------------------|------|
| campaign_name | campaign_id, account_id, ad_id, adset_id | 广告系列名称作为各种ID |
| impressions | impressions | 展示次数 |
| clicks | clicks | 点击次数 |
| revenue | spend, oracle_price | 收益存储为花费和预估收益 |
| - | cpm | 根据收益和展示次数计算 |
| data_date | date | 数据日期 |
| - | user_id | 默认为1 |
| - | update_time | 当前时间 |

## 数据处理逻辑

1. **重复数据处理**：如果同一天同一个 campaign_name 已有记录，则更新现有记录
2. **新数据创建**：如果没有现有记录，则创建新的 FacebookInsights 记录
3. **收益计算**：Google 的 revenue 字段会同时存储为 spend 和 oracle_price
4. **CPM 计算**：如果有展示次数，会自动计算 CPM = (revenue / impressions) * 1000

## 定时同步功能

### 环境变量配置

设置环境变量 `GOOGLE_REPORT_SECURITY_KEY` 可启用自动定时同步：

```bash
export GOOGLE_REPORT_SECURITY_KEY=your_security_key
```

### 定时规则

- **每小时同步**：同步最近7天的数据
- **每日同步**：每天凌晨2点同步昨天的数据

### 启用定时同步

```go
// 在应用启动时添加
cronService := service.NewGoogleReportCronService()
if err := cronService.Start(); err != nil {
    log.Printf("启动定时同步失败: %v", err)
}
```

## 错误码说明

| 错误码 | 说明 |
|--------|------|
| 200 | 成功 |
| 400 | 请求参数错误 |
| 500 | 服务器内部错误 |

## 使用建议

1. **首次同步**：建议使用日期范围同步功能，同步历史数据
2. **日常维护**：使用自动同步功能，每小时更新最近7天数据
3. **数据验证**：定期检查同步结果，确保数据完整性
4. **性能考虑**：大量数据同步时，建议分批进行，避免超时

## 监控和日志

查看同步日志：

```bash
# 查看应用日志
tail -f /var/log/your-app.log | grep "Google报告"
```

日志示例：
```
2025-01-01 10:00:00 INFO 开始同步Google报告数据: date=2025-01-01
2025-01-01 10:00:05 INFO 获取第1页数据，共25条
2025-01-01 10:00:10 INFO Google报告数据同步完成: 总数=25, 成功=25, 失败=0
```

## 注意事项

1. **数据更新频率**：Google 数据每小时更新一次，最近7天数据会持续更新
2. **点击数限制**：当24小时内点击次数少于10次时，Google 只报告收入，不报告点击
3. **API 限制**：注意 AdTech API 的调用频率限制
4. **数据一致性**：同步过程中如有错误，会在日志中记录详细信息
