openapi: 3.0.3
info:
  title: content-api
  description: |
    # Introduction and Process Overview

    This API offers easy-to-use endpoints for managing articles on content sites using the Sedo Traffic Monetization Platform.

    # Authentication
    The API uses a modern OAuth authentication process to ensure security without sacrificing simplicity.
    To access the API, you need an access token. For more details on authentication, please refer to the [Introduction](/cms/docs-api/introduction) section.

    <!-- ReDoc-Inject: <security-definitions> -->
  version: 1.2.0
x-tagGroups:
- name: Articles
  tags:
  - Articles
  - Published Articles
  - Categories
  - Media Resources
- name: Content Sites
  tags:
  - Domains
- name: Appendix
  tags:
  - Glossary
tags:
- name: Published Articles
  description: |
    In order articles get online we need to publish them to content site. The following endpoints will guide you through the process, making it easy to publish and host your articles.
- name: Categories
  description: |
    Categories help organize content into different sections or topics. They allow users to filter and search for content based on specific interests. Categories group similar articles, making it easier for readers to find related content. They are essential for organizing articles on content sites, enhancing user experience and content discoverability.
- name: Articles
  description: |
    In order to monetize traffic to a content sites, you need to fill content site with articles. They can be imported or generated by our AI system. Once article has been added to the system, it needs to be published to content site for public access

    **Categories**:

    Categories help organize content into different sections or topics. They allow users to filter and search for content based on specific interests. Categories group similar articles, making it easier for readers to find related content. They are essential for organizing articles on content sites, enhancing user experience and content discoverability.

    **Publishing an article**

    When article gets generated or imported it will be stored in our system. To make the article available on the content site, it needs to be uploaded/published. You can find available content site (domains) for publishing using the `/domains` endpoint. To streamline the process, you can specify the domain at generation time, and the article will be published automatically in the background. The generated article will be listed in the article list, and its publishing status can be checked using the `/published-articles` endpoint.

    **Filtering**

    - The list of resource will be filtered by the authenticated partner, ensuring that only relevant to the partner resources are retrieved.

    **Usage**
    - Published articles are the final versions of articles that have been reviewed and approved for public viewing.
    - These articles are accessible to the end-users on the content sites and represent the current published state of the content.

    **Additional Information**

    - To see details about unpublished or draft articles, you need to call the `/articles` endpoint.
- name: Domains
  description: |
    To get articles online you'll need to set up a content site (domain) and configure it using our API. The following endpoints will guide you through the process, making it easy to publish and host your articles.
- name: Glossary
  description: |
    This section provides definitions for terms used throughout the API documentation.

    Term | Definition
    ----------------|-------------
    Article | Content page (article) which contains title, excerpt (introduction short text), main text body, an image, tags, category.
    AutoPublish | A feature that allows articles to be automatically published to a specified domain.
    Category | A classification used to group similar articles together.
    Domain | A content site where articles and other content are published.
    Locale | The language and regional settings for the audience of the content.
    Media Resource | A file, such as an image, that can be attached to articles to enhance their content.
    Partner | An entity that collaborates with the system to manage and publish content.
    Published Article | An article which is uploaded/published to a content site.
    ReferenceIdHeader | A header containing a reference ID to link resources.
    RequestFlowHeader | A header indicating whether the request should be processed synchronously or asynchronously.
servers:
- url: https://api.sedotmp.com/content/v1
security:
- bearerAuth: []
paths:
  /categories:
    get:
      tags:
      - category
      - Categories
      summary: Retrieve a list of content categories
      parameters:
      - $ref: '#/components/parameters/pageQuery'
      - $ref: '#/components/parameters/termQuery'
      responses:
        200:
          $ref: '#/components/responses/CategoriesList'
        400:
          $ref: '#/components/responses/BadRequest'
        500:
          $ref: '#/components/responses/InternalServerError'
    post:
      tags:
      - category
      - Categories
      summary: Create a new content category
      requestBody:
        required: true
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/CreateCategory'
      responses:
        200:
          $ref: '#/components/responses/Category'
        400:
          $ref: '#/components/responses/BadRequest'
        500:
          $ref: '#/components/responses/InternalServerError'
  /categories/{id}:
    get:
      tags:
      - category
      - Categories
      summary: Retrieve a content category by its ID
      parameters:
      - $ref: '#/components/parameters/id'
      responses:
        200:
          $ref: '#/components/responses/Category'
        400:
          $ref: '#/components/responses/BadRequest'
        500:
          $ref: '#/components/responses/InternalServerError'
  /generated-articles:
    post:
      tags:
      - generated-article
      - Articles
      summary: Generate a new article for a specified topic
      description: |
        This endpoint allows you to generate a new article for a specific topic. Article generation can take some time, so you can choose to process the request either synchronously or asynchronously using the `X-Sedo-Request-Flow` header.

        Once the article is generated, it will be stored in our system. To make the article available on the content site, it needs to be uploaded/published. You can find available domains for publishing using the `/domains` endpoint. To streamline the process, you can specify the domain at generation time, and the article will be published automatically in the background. The generated article will be listed in the article list, and its publishing status can be checked using the `/published-articles` endpoint.

        To ensure fair usage and protect our system from abuse, we apply certain limits on article generation:
          - **Content Generation:** Limited to a certain number of requests per hour.
          - **Resource Usage:** Limited to a certain amount of resources per day.

        If these limits are exceeded, a `TooManyRequests` error response will be returned.

        **Compliance Check:**
        Articles published on our content sites must adhere to several regulations and guidelines. Before generating an article, it is checked for compliance. If the article does not meet the guidelines, the generation may fail, or modifications may be applied to the requested topic, title, and other fields to ensure compliance and enhance user experience.
      parameters:
      - $ref: '#/components/parameters/xSedoRequestFlowHeader'
      - $ref: '#/components/parameters/xSedoReferenceIdHeader'
      requestBody:
        required: true
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/GenerateArticle'
      responses:
        202:
          description: Request has been accepted and will be processed asynchronously
        200:
          $ref: '#/components/responses/Article'
        400:
          $ref: '#/components/responses/BadRequest'
        429:
          $ref: '#/components/responses/TooManyRequests'
        500:
          $ref: '#/components/responses/InternalServerError'
  /articles:
    get:
      tags:
      - article
      - Articles
      summary: Retrieve a list of articles
      description: |
        This endpoint retrieves a list of articles available in the system. Only articles that belong to the authenticated partner will be listed, or all if the authenticated user has sufficient privileges. No details about published articles will be included in the response. To see details about published articles, you need to call the `/published-articles` endpoint.
      parameters:
      - $ref: '#/components/parameters/pageQuery'
      - $ref: '#/components/parameters/termQuery'
      responses:
        200:
          $ref: '#/components/responses/ArticleList'
        400:
          $ref: '#/components/responses/BadRequest'
        500:
          $ref: '#/components/responses/InternalServerError'
    post:
      tags:
      - article
      - Articles
      summary: Create a new article
      description: |
        With this endpoint you can import and existing article into the system. To make the article available on the content site, it needs to be uploaded/published. You can find available domains for publishing using the `/domains` endpoint. To streamline the process, you can specify the domain at generation time, and the article will be published automatically in the background. The generated article will be listed in the article list, and its publishing status can be checked using the `/published-articles` endpoint.
      requestBody:
        required: true
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/CreateArticle'
      responses:
        200:
          $ref: '#/components/responses/Article'
        400:
          $ref: '#/components/responses/BadRequest'
        500:
          $ref: '#/components/responses/InternalServerError'
  /articles/{id}:
    get:
      tags:
      - article
      - Articles
      summary: Retrieve an article by its ID
      parameters:
      - $ref: '#/components/parameters/id'
      responses:
        200:
          $ref: '#/components/responses/Article'
        400:
          $ref: '#/components/responses/BadRequest'
        500:
          $ref: '#/components/responses/InternalServerError'
    patch:
      tags:
      - article
      - Articles
      summary: Partially update an article by its ID
      description: |
        **Publishing Note:**
        - If the article is already published, changes made through this endpoint will not be visible until the article is published again by calling the `/published-articles` POST endpoint.
      parameters:
      - $ref: '#/components/parameters/id'
      requestBody:
        required: true
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/UpdateArticle'
      responses:
        200:
          $ref: '#/components/responses/Article'
        400:
          $ref: '#/components/responses/BadRequest'
        500:
          $ref: '#/components/responses/InternalServerError'
    put:
      tags:
      - article
      - Articles
      summary: Fully update an article by its ID
      description: |
        **Publishing Note:**
        - If the article is already published, changes made through this endpoint will not be visible until the article is published again by calling the `/published-articles` POST endpoint.
      parameters:
      - $ref: '#/components/parameters/id'
      requestBody:
        required: true
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/UpdateArticle'
      responses:
        200:
          $ref: '#/components/responses/Article'
        400:
          $ref: '#/components/responses/BadRequest'
        500:
          $ref: '#/components/responses/InternalServerError'
    delete:
      tags:
      - article
      - Articles
      summary: Delete an article by its ID
      description: |
        This endpoint deletes an article by its ID. Note that deleting an article does not affect the resources used within that article. For example, if a category or media resource was used for the article creation, they will remain untouched and can be reused for the creation of other articles.
      parameters:
      - $ref: '#/components/parameters/id'
      responses:
        204:
          description: The resource was deleted successfully.
        400:
          $ref: '#/components/responses/BadRequest'
        500:
          $ref: '#/components/responses/InternalServerError'
  /published-articles:
    get:
      tags:
      - published-article
      - Published Articles
      summary: Retrieve a list of published articles
      description: |
        This endpoint retrieves a list of published articles available in the system. Published articles are those that have been made publicly accessible on the content sites.
      parameters:
      - $ref: '#/components/parameters/pageQuery'
      - $ref: '#/components/parameters/termQuery'
      responses:
        200:
          $ref: '#/components/responses/PublishedArticleList'
        400:
          $ref: '#/components/responses/BadRequest'
        500:
          $ref: '#/components/responses/InternalServerError'
    post:
      tags:
      - published-article
      - Published Articles
      summary: Publish an article on a specified domain
      description: |
        This endpoint allows you to publish an article on a specified domain.
        - Publishing means that the current snapshot of the article gets uploaded to specified domain.
        - Any changes made to the article after this point will not be automatically uploaded, they would require a new publish operation.
      parameters:
      - $ref: '#/components/parameters/xSedoRequestFlowHeader'
      - $ref: '#/components/parameters/xSedoReferenceIdHeader'
      requestBody:
        required: true
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/PublishArticle'
      responses:
        200:
          $ref: '#/components/responses/PublishedArticle'
        400:
          $ref: '#/components/responses/BadRequest'
        500:
          $ref: '#/components/responses/InternalServerError'
  /published-articles/{id}:
    get:
      tags:
      - published-article
      - Published Articles
      summary: Retrieve a published article by its ID
      parameters:
      - $ref: '#/components/parameters/id'
      responses:
        200:
          $ref: '#/components/responses/PublishedArticle'
        400:
          $ref: '#/components/responses/BadRequest'
        500:
          $ref: '#/components/responses/InternalServerError'
    delete:
      tags:
      - published-article
      - Published Articles
      summary: Unpublish an article by its ID
      description: |
        This endpoint allows you to unpublish an article by its ID. The article will be removed from the content site, but the article itself will still be available in the system. This means it can be published again in the future if needed.

        **Restrictions:**
        - Only published articles that belong to the authenticated partner can be deleted.
        - The authenticated partner must have the necessary permissions to unpublish the article.

        **Usage:**
        - Unpublishing an article removes it from public view on the content site.
        - The article remains in the system and can be republished at a later time if required.

        **Additional Information:**
        - Unpublishing does not delete the article from the system; it only removes the public access to it.
        - Any associated media resources or metadata will remain intact and can be reused.
      parameters:
      - $ref: '#/components/parameters/id'
      responses:
        204:
          description: The article was successfully unpublished
        400:
          $ref: '#/components/responses/BadRequest'
        500:
          $ref: '#/components/responses/InternalServerError'
  /detailed-articles:
    get:
      tags:
      - detailed-article
      summary: List detailed-articles, compared to article this includes additionally published-article information
      parameters:
      - $ref: '#/components/parameters/pageQuery'
      - $ref: '#/components/parameters/termQuery'
      responses:
        200:
          $ref: '#/components/responses/DetailedArticleList'
        400:
          $ref: '#/components/responses/BadRequest'
        500:
          $ref: '#/components/responses/InternalServerError'
  /detailed-articles/{id}:
    get:
      tags:
      - detailed-article
      summary: Retrieve an detailed-articles by its articleId, compared to article this includes additionally published-article information
      parameters:
      - $ref: '#/components/parameters/id'
      responses:
        200:
          $ref: '#/components/responses/DetailedArticle'
        400:
          $ref: '#/components/responses/BadRequest'
        500:
          $ref: '#/components/responses/InternalServerError'
  /domains:
    get:
      tags:
      - domain
      - Domains
      summary: Retrieve a list of domains
      description: |
        This endpoint retrieves a list of domains available in the system. Domains, sometimes referred to as content sites, are configured to be used for publishing articles, categories, and tags.

        **Permissions:**
        - Only users with sufficient permissions can perform this operation.

        **Domains:**
        - Domains represent the content sites where articles and other content are published.
        - Each domain can have specific configurations and settings.
      parameters:
      - $ref: '#/components/parameters/pageQuery'
      responses:
        200:
          $ref: '#/components/responses/DomainList'
        400:
          $ref: '#/components/responses/BadRequest'
        500:
          $ref: '#/components/responses/InternalServerError'
  /domains/{id}:
    get:
      tags:
      - domain
      - Domains
      summary: Retrieve a domain by its ID
      parameters:
      - $ref: '#/components/parameters/id'
      responses:
        200:
          $ref: '#/components/responses/Domain'
        400:
          $ref: '#/components/responses/BadRequest'
        500:
          $ref: '#/components/responses/InternalServerError'
  /media:
    get:
      tags:
      - media-resource
      - Media Resources
      summary: Retrieve a list of media resources
      description: |
        This endpoint retrieves a list of all media resources stored in the system, filtered by partner. Media resources for now, include only images that can be used in articles.

        **Permissions:**
        - Only users with sufficient permissions can perform this operation.

        **Media Resources:**
        - Media resources are files that can be attached to articles to enhance their content.
        - These resources are stored in the system and can be reused across multiple articles.

        **Usage:**
        - Media resources can be used when creating new articles.
        - When an article that includes a media resource is published, the media resource will also be published to the content site.
      parameters:
      - $ref: '#/components/parameters/pageQuery'
      responses:
        200:
          $ref: '#/components/responses/MediaResourceList'
        400:
          $ref: '#/components/responses/BadRequest'
        500:
          $ref: '#/components/responses/InternalServerError'
    post:
      tags:
      - media-resource
      - Media Resources
      summary: Create a new media resource
      description: |
        This endpoint allows you to add a new media resource to the system. When a media resource is added, it is stored in the system but not published to the content site.

        **Permissions:**
        - Only users with sufficient permissions can perform this operation.

        **Media Resources:**
        - Media resources are files that can be attached to articles to enhance their content.
        - These resources are stored in the system and can be reused across multiple articles.

        **Usage:**
        - Media resources can be used when creating new articles.
        - When an article that includes a media resource is published, the media resource will also be published to the content site.
      requestBody:
        required: true
        content:
          multipart/form-data:
            schema:
              type: object
              properties:
                file:
                  type: string
                  format: binary
            encoding:
              file:
                contentType: image/jpeg, image/png, image/webp
      responses:
        200:
          $ref: '#/components/responses/MediaResource'
        400:
          $ref: '#/components/responses/BadRequest'
        500:
          $ref: '#/components/responses/InternalServerError'
  /media/{id}:
    get:
      tags:
      - media-resource
      - Media Resources
      summary: Retrieve a media resource by its ID
      parameters:
      - $ref: '#/components/parameters/id'
      responses:
        200:
          $ref: '#/components/responses/MediaResource'
        400:
          $ref: '#/components/responses/BadRequest'
        500:
          $ref: '#/components/responses/InternalServerError'
  /media/download/{id}:
    get:
      tags:
      - media-resource
      - Media Resources
      summary: Download a media resource by its ID
      parameters:
      - $ref: '#/components/parameters/id'
      responses:
        200:
          description: Media resource content
          content:
            image/jpg:
              schema:
                type: string
                format: binary
            image/jpeg:
              schema:
                type: string
                format: binary
            image/webp:
              schema:
                type: string
                format: binary
            image/png:
              schema:
                type: string
                format: binary
components:
  securitySchemes:
    bearerAuth:
      type: http
      scheme: bearer
      bearerFormat: JWT
  parameters:
    id:
      name: id
      in: path
      description: Resource id
      required: true
      schema:
        $ref: '#/components/schemas/Id'
    pageQuery:
      name: page
      in: query
      description: Pageable object (every key is a separate query parameter)
      schema:
        $ref: '#/components/schemas/Pageable'
    termQuery:
      name: term
      in: query
      description: Search term for matching against any text field e.g. ID, title, excerpt, text..
      schema:
        type: string
        example: summer%20vacation
    xSedoRequestFlowHeader:
      name: X-Sedo-Request-Flow
      in: header
      schema:
        $ref: '#/components/schemas/RequestFlowHeader'
    xSedoReferenceIdHeader:
      name: X-Sedo-Reference-Id
      in: header
      schema:
        $ref: '#/components/schemas/ReferenceIdHeader'
  schemas:
    Id:
      type: string
      format: uuid4
      example: cf1a429f-e596-4648-83a2-5a3045b2276a
    DateTime:
      description: ISO-8601 formatted timestamp
      type: string
      format: date-time
      example: '2024-01-01T18:00:00.000Z'
    User:
      description: Username of the user who operated on the resource
      type: string
      example: some-user-1
    ArticleTitle:
      description: Article title
      type: string
      example: 5 most beautiful locations for summer vacation
    ArticleExcerpt:
      description: Short excerpt of the text
      type: string
      example: Lorem ipsum dolor sit amet...
    ArticleText:
      description: Full text content
      type: string
      example: Lorem ipsum dolor sit amet, consectetur adipiscing elit. Nullam nec purus nec nunc...
    ArticleTags:
      description: |
        List of tags that describe the article. These tags help categorize articles and group similar articles together.

        Note:
          * Tags do not affect the ads displayed. They should not be confused with ad keywords.
          * Multiple tags should be sent as an array, not as a single long string.
      type: array
      items:
        type: string
      example:
      - Summer
      - Travel
    CategoryResponse:
      type: object
      properties:
        id:
          $ref: '#/components/schemas/Id'
        title:
          description: Category title
          type: string
          example: Travel
    CreateCategory:
      type: object
      required:
      - title
      properties:
        title:
          description: The title of the category
          type: string
          example: Travel
    ArticleResponse:
      type: object
      properties:
        id:
          $ref: '#/components/schemas/Id'
        title:
          $ref: '#/components/schemas/ArticleTitle'
        excerpt:
          $ref: '#/components/schemas/ArticleExcerpt'
        text:
          $ref: '#/components/schemas/ArticleText'
        categoryId:
          $ref: '#/components/schemas/Id'
        tags:
          $ref: '#/components/schemas/ArticleTags'
        country:
          $ref: '#/components/schemas/Country'
        locale:
          $ref: '#/components/schemas/Locale'
        createdDate:
          $ref: '#/components/schemas/DateTime'
        lastModifiedDate:
          $ref: '#/components/schemas/DateTime'
        partner:
          $ref: '#/components/schemas/Partner'
        createdBy:
          $ref: '#/components/schemas/User'
        lastModifiedBy:
          $ref: '#/components/schemas/User'
        images:
          $ref: '#/components/schemas/ArticleImagesList'
    DetailedArticleResponse:
      type: object
      properties:
        id:
          $ref: '#/components/schemas/Id'
        title:
          $ref: '#/components/schemas/ArticleTitle'
        excerpt:
          $ref: '#/components/schemas/ArticleExcerpt'
        text:
          $ref: '#/components/schemas/ArticleText'
        categoryId:
          $ref: '#/components/schemas/Id'
        tags:
          $ref: '#/components/schemas/ArticleTags'
        country:
          $ref: '#/components/schemas/Country'
        locale:
          $ref: '#/components/schemas/Locale'
        images:
          $ref: '#/components/schemas/ArticleImagesList'
        createdDate:
          $ref: '#/components/schemas/DateTime'
        lastModifiedDate:
          $ref: '#/components/schemas/DateTime'
        partner:
          $ref: '#/components/schemas/Partner'
        createdBy:
          $ref: '#/components/schemas/User'
        lastModifiedBy:
          $ref: '#/components/schemas/User'
        publishedArticles:
          type: array
          items:
            $ref: '#/components/schemas/MinimalPublishedArticle'
    SimpleArticle:
      type: object
      required:
      - text
      - title
      properties:
        title:
          $ref: '#/components/schemas/ArticleTitle'
        excerpt:
          $ref: '#/components/schemas/ArticleExcerpt'
        text:
          $ref: '#/components/schemas/ArticleText'
        categoryId:
          $ref: '#/components/schemas/Id'
        tags:
          $ref: '#/components/schemas/ArticleTags'
        country:
          $ref: '#/components/schemas/Country'
        locale:
          $ref: '#/components/schemas/Locale'
        images:
          $ref: '#/components/schemas/ArticleImagesList'
    CreateArticle:
      allOf:
      - $ref: '#/components/schemas/SimpleArticle'
      - type: object
        required:
        - text
        - title
        properties:
          autoPublish:
            $ref: '#/components/schemas/AutoPublish'
    UpdateArticle:
      allOf:
      - $ref: '#/components/schemas/SimpleArticle'
      - type: object
        required:
        - text
        - title
        properties:
          partner:
            $ref: '#/components/schemas/Partner'
    GenerateArticle:
      type: object
      required:
      - topics
      properties:
        topics:
          description: The topic to generate article about
          type: array
          items:
            type: string
          example:
          - Summer vacation
          - Senior living
        title:
          $ref: '#/components/schemas/ArticleTitle'
        excerpt:
          $ref: '#/components/schemas/ArticleExcerpt'
        country:
          $ref: '#/components/schemas/Country'
        locale:
          $ref: '#/components/schemas/Locale'
        categoryId:
          description: |
            **Category Assignment:**
            If `categoryId` is not included in the request field in the request body, we will try to find a suitable category for the generated article based on the text and title from the existing categories in the system.
          allOf:
          - $ref: '#/components/schemas/Id'
        tags:
          $ref: '#/components/schemas/ArticleTags'
        autoPublish:
          $ref: '#/components/schemas/AutoPublish'
        generateImage:
          type: object
          description: |
            Generate a image matching the content. Note: image generation is not supported by all providers
          properties:
            enabled:
              type: boolean
              default: false
              description: Generate an image and attach to the article
              example: 'true'
            description:
              type: string
              description: Details to refine the generated image
    Partner:
      description: Partner to assigned to the resource. Requires corresponding privileges
      type: string
      example: partner2
    Platform:
      description: Content site API platform
      type: string
      default: WORDPRESS
      example: WORDPRESS
      enum:
      - WORDPRESS
    DomainResponse:
      type: object
      properties:
        id:
          $ref: '#/components/schemas/Id'
        domain:
          description: Domain name
          type: string
          example: my-domain.com
        url:
          description: The content site url
          type: string
        platform:
          $ref: '#/components/schemas/Platform'
        createdDate:
          $ref: '#/components/schemas/DateTime'
        lastModifiedDate:
          $ref: '#/components/schemas/DateTime'
        partners:
          type: array
          uniqueItems: true
          items:
            $ref: '#/components/schemas/Partner'
        createdBy:
          $ref: '#/components/schemas/User'
        lastModifiedBy:
          $ref: '#/components/schemas/User'
    PublishArticle:
      type: object
      required:
      - articleId
      - domain
      properties:
        articleId:
          $ref: '#/components/schemas/Id'
        domain:
          description: |
            * Domain name on which the article should be published.
            * Domain name should already be configured in the system.
          type: string
          minLength: 1
          example: my-domain.com
        publishDate:
          $ref: '#/components/schemas/DateTime'
    PublishedArticleResponse:
      type: object
      required:
      - text
      - title
      properties:
        id:
          $ref: '#/components/schemas/Id'
        domain:
          type: string
          example: my-domain.com
        url:
          description: The published article url
          type: string
          example: http://my-domain.com/my-article
        slug:
          description: Slug of the published article
          type: string
          example: /my-article-3
        publishedId:
          $ref: '#/components/schemas/Id'
        articleId:
          $ref: '#/components/schemas/Id'
        publishedDate:
          $ref: '#/components/schemas/DateTime'
        publishedStatus:
          type: string
          enum:
          - DRAFT
          - PENDING
          - PUBLISHED
          - ERROR
          example: PUBLISHED
        publishedBy:
          $ref: '#/components/schemas/User'
        title:
          $ref: '#/components/schemas/ArticleTitle'
        excerpt:
          $ref: '#/components/schemas/ArticleExcerpt'
        text:
          $ref: '#/components/schemas/ArticleText'
        categoryId:
          $ref: '#/components/schemas/Id'
        tags:
          $ref: '#/components/schemas/ArticleTags'
        locale:
          $ref: '#/components/schemas/Locale'
        createdDate:
          $ref: '#/components/schemas/DateTime'
        lastModifiedDate:
          $ref: '#/components/schemas/DateTime'
        partner:
          $ref: '#/components/schemas/Partner'
        createdBy:
          $ref: '#/components/schemas/User'
        lastModifiedBy:
          $ref: '#/components/schemas/User'
        images:
          $ref: '#/components/schemas/ArticleImagesList'
    MinimalPublishedArticle:
      type: object
      required:
      - id
      properties:
        id:
          $ref: '#/components/schemas/Id'
        articleId:
          $ref: '#/components/schemas/Id'
        domain:
          type: string
          example: my-domain.com
        url:
          description: The published article url
          type: string
          example: http://my-domain.com/my-article
        slug:
          description: Slug of the published article
          type: string
          example: /my-article-3
        publishedId:
          $ref: '#/components/schemas/Id'
        publishedBy:
          $ref: '#/components/schemas/User'
        publishedDate:
          $ref: '#/components/schemas/DateTime'
        publishedStatus:
          type: string
          enum:
          - DRAFT
          - PENDING
          - PUBLISHED
          - ERROR
          example: PUBLISHED
        partner:
          $ref: '#/components/schemas/Partner'
    Pageable:
      type: object
      properties:
        page:
          type: integer
          format: int32
          default: 0
          minimum: 0
          description: 0-indexed page number
        size:
          type: integer
          format: int32
          default: 10
          minimum: 1
          maximum: 10
        sort:
          description: |
            Since this will be used in query string, it is possible to send multiple `sort=property,direction` parameters
          type: string
          example: id,asc
    Country:
      description: Audience country (ISO code)
      type: string
      maxLength: 2
      format: iso-3166-alpha-2
      example: US
    Locale:
      description: Audience language (Language tag formatted)
      type: string
      format: bcp47
      example: en-US
    ArticleImagesList:
      type: array
      items:
        $ref: '#/components/schemas/ArticleImage'
    ArticleImage:
      type: object
      properties:
        id:
          $ref: '#/components/schemas/Id'
        uid:
          $ref: '#/components/schemas/Id'
        name:
          type: string
          example: Some title
        filename:
          type: string
          example: sunny-beach-123.jpg
        url:
          type: string
          example: /api/media/2ab769df-1b0b-44bf-a544-f978b1b4c723.jpg
        type:
          type: string
          example: image/jpeg
        size:
          type: number
          example: 475677
    MediaResourceResponse:
      type: object
      properties:
        id:
          $ref: '#/components/schemas/Id'
        name:
          description: Name of media resource
          type: string
          example: Some title
        filename:
          description: Filename of the media resource
          type: string
          example: sunny-beach-123.jpg
        originalFilename:
          description: Original filename of the media resource
          type: string
          example: sunny-beach-123.jpg
        url:
          description: Url where media resource can be accessed
          type: string
          example: /api/media/2ab769df-1b0b-44bf-a544-f978b1b4c723.jpg
        type:
          description: Media type of the resource
          type: string
          example: image/jpeg
        createdDate:
          $ref: '#/components/schemas/DateTime'
        lastModifiedDate:
          $ref: '#/components/schemas/DateTime'
        partner:
          $ref: '#/components/schemas/Partner'
        createdBy:
          $ref: '#/components/schemas/User'
        lastModifiedBy:
          $ref: '#/components/schemas/User'
    AutoPublish:
      type: object
      properties:
        domainName:
          type: string
          minLength: 4
          description: Domain name for which article will be auto-published
          example: domain.com
        publishDate:
          $ref: '#/components/schemas/DateTime'
    RequestFlowHeader:
      description: |
        Process request in synchronous or synchronous manner
        - `SYNC`: The request will processed synchronously, which might take some time and client needs to wait for the response
        - `ASYNC`: The request will be processed by the system in background and clients needs to poll for result
      type: string
      default: SYNC
      enum:
      - SYNC
      - ASYNC
      example: SYNC
    ReferenceIdHeader:
      description: Reference id which caller can send in order to be able to link resources
      type: string
      example: some-reference-id-123
  headers:
    X-Total-Count:
      description: Total count of results
      required: true
      schema:
        type: integer
        format: int64
      example: 100
    X-Total-Pages:
      description: Total count of pages
      required: true
      schema:
        type: integer
        format: int32
      example: 10
  responses:
    BadRequest:
      description: |
        Bad request: most likely the provided json body is invalid or some required fields are missing.
      content:
        application/json:
          schema:
            $ref: https://raw.githubusercontent.com/zalando/restful-api-guidelines/master/models/problem-1.0.1.yaml#/Problem
    InternalServerError:
      description: |
        Internal Server Error: some internal processing failed. May be related to external api calls
      content:
        application/json:
          schema:
            $ref: https://raw.githubusercontent.com/zalando/restful-api-guidelines/master/models/problem-1.0.1.yaml#/Problem
    TooManyRequests:
      description: |
        Too Many Requests: occurs when we reach a sort kind of request limits.

        For example the limit of generation requests
      content:
        application/json:
          schema:
            $ref: https://raw.githubusercontent.com/zalando/restful-api-guidelines/master/models/problem-1.0.1.yaml#/Problem
    Category:
      description: Single category details
      content:
        application/json:
          schema:
            $ref: '#/components/schemas/CategoryResponse'
    CategoriesList:
      description: List of categories available for the content
      content:
        application/json:
          schema:
            type: array
            items:
              $ref: '#/components/schemas/CategoryResponse'
      headers:
        X-Total-Count:
          $ref: '#/components/headers/X-Total-Count'
        X-Total-Pages:
          $ref: '#/components/headers/X-Total-Pages'
    Article:
      description: Successfully created or generated article
      content:
        application/json:
          schema:
            $ref: '#/components/schemas/ArticleResponse'
    DetailedArticle:
      description: Successfully created or generated detailed-article
      content:
        application/json:
          schema:
            $ref: '#/components/schemas/DetailedArticleResponse'
    ArticleList:
      description: List of existing articles
      content:
        application/json:
          schema:
            type: array
            items:
              $ref: '#/components/schemas/ArticleResponse'
      headers:
        X-Total-Count:
          $ref: '#/components/headers/X-Total-Count'
        X-Total-Pages:
          $ref: '#/components/headers/X-Total-Pages'
    DetailedArticleList:
      description: List of existing articles including published article information
      content:
        application/json:
          schema:
            type: array
            items:
              $ref: '#/components/schemas/DetailedArticleResponse'
      headers:
        X-Total-Count:
          $ref: '#/components/headers/X-Total-Count'
        X-Total-Pages:
          $ref: '#/components/headers/X-Total-Pages'
    Domain:
      description: Successfully created or generated domain
      content:
        application/json:
          schema:
            $ref: '#/components/schemas/DomainResponse'
    DomainList:
      description: List of existing domains
      content:
        application/json:
          schema:
            type: array
            items:
              $ref: '#/components/schemas/DomainResponse'
      headers:
        X-Total-Count:
          $ref: '#/components/headers/X-Total-Count'
        X-Total-Pages:
          $ref: '#/components/headers/X-Total-Pages'
    PublishedArticle:
      description: Successfully created or generated article
      content:
        application/json:
          schema:
            $ref: '#/components/schemas/PublishedArticleResponse'
    PublishedArticleList:
      description: List of published articles
      content:
        application/json:
          schema:
            type: array
            items:
              $ref: '#/components/schemas/PublishedArticleResponse'
      headers:
        X-Total-Count:
          $ref: '#/components/headers/X-Total-Count'
        X-Total-Pages:
          $ref: '#/components/headers/X-Total-Pages'
    MediaResource:
      description: Successfully stored media resource
      content:
        application/json:
          schema:
            $ref: '#/components/schemas/MediaResourceResponse'
    MediaResourceList:
      description: List of existing articles
      content:
        application/json:
          schema:
            type: array
            items:
              $ref: '#/components/schemas/MediaResourceResponse'
      headers:
        X-Total-Count:
          $ref: '#/components/headers/X-Total-Count'
        X-Total-Pages:
          $ref: '#/components/headers/X-Total-Pages'
