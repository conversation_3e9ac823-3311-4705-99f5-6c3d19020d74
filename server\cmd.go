package server

import (
	"embed"
	"net/http"
	"net/http/httputil"

	"github.com/gin-gonic/gin"
)

/*//go:embed admin/*
var EmbededFiles embed.FS*/

//go:embed views/*
var ViewsEmbededFiles embed.FS

func LoadTmpl(param *gin.Engine) {
	// 使用嵌入的文件系统提供静态资源
	// assetsFS, err := fs.Sub(ViewsEmbededFiles, "views/assets")
	// if err != nil {
	// 	log.Fatal("Failed to get assets subdirectory:", err)
	// }
	param.StaticFS("/assets", http.Dir("server/views/assets"))

	//param.StaticFS("/admin/sa-frame", http.Dir("server/admin/sa-frame"))
	//param.StaticFS("/sa-frame", http.Dir("server/admin/sa-frame"))

	// 根路径重定向到主页
	param.GET("/", func(c *gin.Context) {
		c.Redirect(http.StatusFound, "/admin_view_index.html")
	})

	// 处理所有未匹配的路由，重定向到index.html，使Vue路由生效
	param.NoRoute(func(c *gin.Context) {
		// 定义需要代理的路径前缀
		proxyPaths := []string{"/user", "/SeDo", "/MainDirectory", "/FaceBook", "/sedo", "/TwoDirectory", "/Task", "/proxy", "/server", "/Yahoo1", "/KeyWords", "/rsoc"}

		// 检查当前请求路径是否需要代理
		needProxy := false
		for _, path := range proxyPaths {
			if len(c.Request.URL.Path) >= len(path) && c.Request.URL.Path[:len(path)] == path {
				needProxy = true
				break
			}
		}

		if needProxy {
			// 使用反向代理转发请求
			proxy := &httputil.ReverseProxy{
				Director: func(req *http.Request) {
					req.URL.Scheme = "http"
					req.URL.Host = "127.0.0.1"
					req.Host = "127.0.0.1"
				},
			}
			proxy.ServeHTTP(c.Writer, c.Request)
			return
		}

		// 其他路径返回index.html，交给前端路由处理
		c.File("server/views/index.html")
	})

	// 匹配vue中的/v/*链接，跳转至vue入口文件，vue会自动进行路由
	/*	param.GET("/v/*name", func(c *gin.Context) {
		c.Request.URL.Path = "/index.html"
		param.HandleContext(c)
	})*/
	// 匹配/链接，重定向到主页
	/*param.GET("/", func(c *gin.Context) {
		c.Redirect(http.StatusFound, "/v/main")
	})*/
	//param.Delims("{{{", "}}}")
	// //tpl := template.Must(template.New("").Delims("{{{", "}}}").ParseFS(EmbededFiles, "admin/sa-view/**/*.html", "admin/sa-view/**/*.vue"))
	// //param.SetHTMLTemplate(tpl)

	//param.LoadHTMLGlob(filepath.Join("server/admin/sa-view/**/*"))
}
