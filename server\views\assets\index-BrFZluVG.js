import{E as q,J,K as X,am as G,j as Q,aO as Y,aU as Z,L as ee,N as j,b3 as oe,b4 as ne,y as te,a0 as ae,d as i,P as O,b0 as le,b5 as se,x as r,b6 as ie,b7 as re,ag as I,b8 as f,as as ce,b9 as ue,aF as pe,ba as z,B as de,bb as fe,a1 as me}from"./index-DlVegDiC.js";import{u as ve}from"./index-CSU5nP3m.js";const ge=o=>{const{componentCls:l,iconCls:t,zIndexPopup:a,colorText:s,colorWarning:C,marginXS:p,fontSize:c,fontWeightStrong:b,lineHeight:d}=o;return{[l]:{zIndex:a,[`${l}-inner-content`]:{color:s},[`${l}-message`]:{position:"relative",marginBottom:p,color:s,fontSize:c,display:"flex",flexWrap:"nowrap",alignItems:"start",[`> ${l}-message-icon ${t}`]:{color:C,fontSize:c,flex:"none",lineHeight:1,paddingTop:(Math.round(c*d)-c)/2},"&-title":{flex:"auto",marginInlineStart:p},"&-title-only":{fontWeight:b}},[`${l}-description`]:{position:"relative",marginInlineStart:c+p,marginBottom:p,color:s,fontSize:c},[`${l}-buttons`]:{textAlign:"end",button:{marginInlineStart:p}}}}},ye=q("Popconfirm",o=>ge(o),o=>{const{zIndexPopupBase:l}=o;return{zIndexPopup:l+60}});var Ce=function(o,l){var t={};for(var a in o)Object.prototype.hasOwnProperty.call(o,a)&&l.indexOf(a)<0&&(t[a]=o[a]);if(o!=null&&typeof Object.getOwnPropertySymbols=="function")for(var s=0,a=Object.getOwnPropertySymbols(o);s<a.length;s++)l.indexOf(a[s])<0&&Object.prototype.propertyIsEnumerable.call(o,a[s])&&(t[a[s]]=o[a[s]]);return t};const be=()=>r(r({},re()),{prefixCls:String,content:f(),title:f(),description:f(),okType:ce("primary"),disabled:{type:Boolean,default:!1},okText:f(),cancelText:f(),icon:f(),okButtonProps:I(),cancelButtonProps:I(),showCancel:{type:Boolean,default:!0},onConfirm:Function,onCancel:Function}),xe=X({compatConfig:{MODE:3},name:"APopconfirm",inheritAttrs:!1,props:G(be(),r(r({},ie()),{trigger:"click",placement:"top",mouseEnterDelay:.1,mouseLeaveDelay:.1,arrowPointAtCenter:!1,autoAdjustOverflow:!0,okType:"primary",disabled:!1})),slots:Object,setup(o,l){let{slots:t,emit:a,expose:s,attrs:C}=l;const p=Q();Y(o.visible===void 0),s({getPopupDomNode:()=>{var e,n;return(n=(e=p.value)===null||e===void 0?void 0:e.getPopupDomNode)===null||n===void 0?void 0:n.call(e)}});const[c,b]=ve(!1,{value:Z(o,"open")}),d=(e,n)=>{o.open===void 0&&b(e),a("update:open",e),a("openChange",e,n)},D=e=>{d(!1,e)},T=e=>{var n;return(n=o.onConfirm)===null||n===void 0?void 0:n.call(o,e)},E=e=>{var n;d(!1,e),(n=o.onCancel)===null||n===void 0||n.call(o,e)},A=e=>{e.keyCode===me.ESC&&c&&d(!1,e)},F=e=>{const{disabled:n}=o;n||d(e)},{prefixCls:u,getPrefixCls:k}=ee("popconfirm",o),L=j(()=>k()),R=j(()=>k("btn")),[K]=ye(u),[S]=oe("Popconfirm",ne.Popconfirm),V=()=>{var e,n,m,v,g;const{okButtonProps:y,cancelButtonProps:x,title:P=(e=t.title)===null||e===void 0?void 0:e.call(t),description:_=(n=t.description)===null||n===void 0?void 0:n.call(t),cancelText:W=(m=t.cancel)===null||m===void 0?void 0:m.call(t),okText:H=(v=t.okText)===null||v===void 0?void 0:v.call(t),okType:h,icon:B=((g=t.icon)===null||g===void 0?void 0:g.call(t))||i(pe,null,null),showCancel:M=!0}=o,{cancelButton:N,okButton:w}=t,$=r({onClick:E,size:"small"},x),U=r(r(r({onClick:T},z(h)),{size:"small"}),y);return i("div",{class:`${u.value}-inner-content`},[i("div",{class:`${u.value}-message`},[B&&i("span",{class:`${u.value}-message-icon`},[B]),i("div",{class:[`${u.value}-message-title`,{[`${u.value}-message-title-only`]:!!_}]},[P])]),_&&i("div",{class:`${u.value}-description`},[_]),i("div",{class:`${u.value}-buttons`},[M?N?N($):i(de,$,{default:()=>[W||S.value.cancelText]}):null,w?w(U):i(fe,{buttonProps:r(r({size:"small"},z(h)),y),actionFn:T,close:D,prefixCls:R.value,quitOnNullishReturnValue:!0,emitEvent:!0},{default:()=>[H||S.value.okText]})])])};return()=>{var e;const{placement:n,overlayClassName:m,trigger:v="click"}=o,g=Ce(o,["placement","overlayClassName","trigger"]),y=te(g,["title","content","cancelText","okText","onUpdate:open","onConfirm","onCancel","prefixCls"]),x=ae(u.value,m);return K(i(se,O(O(O({},y),C),{},{trigger:v,placement:n,onOpenChange:F,open:c.value,overlayClassName:x,transitionName:le(L.value,"zoom-big",o.transitionName),ref:p,"data-popover-inject":!0}),{default:()=>[ue(((e=t.default)===null||e===void 0?void 0:e.call(t))||[],{onKeydown:P=>{A(P)}},!1)],content:V}))}}}),Oe=J(xe);export{Oe as _};
