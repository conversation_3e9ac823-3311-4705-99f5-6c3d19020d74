# Replay-go

# redis配置
```config
appendonly yes
maxmemory 0
hash-max-ziplist-entries 512
hash-max-ziplist-value 64
maxmemory-policy allkeys-lru
```

## 配置
```bash
# 环境变量配置
# 微信小程序appid
AppID=微信小程序appid
# 微信小程序appsecret
AppSecret=微信小程序appsecret
# 服务器token
Token=服务器token
EncodingAESKey=EncodingAESKey

# 数据库配置
# MySQL默认数据库连接地址
MYSQL_URL=root:root@tcp(localhost:3306)/dbname?charset=utf8mb4

# MySQL其他实例配置（可配置多个）
MYSQL_URL_BUSINESS=root:root@tcp(localhost:3306)/business?charset=utf8mb4
MYSQL_URL_LOG=root:root@tcp(localhost:3306)/log?charset=utf8mb4
MYSQL_URL_STATS=root:root@tcp(localhost:3306)/stats?charset=utf8mb4

# PostgreSQL数据库连接地址（可选）
POSTGRES_URL=postgres://user:password@localhost:5432/dbname?sslmode=disable

# Redis配置
REDIS_URL=redis://localhost:6379

# 其他Redis实例
REDIS_URL_CACHE=redis://cache:6379
REDIS_URL_SESSION=redis://session:6379
REDIS_URL_QUEUE=redis://queue:6379
```

## 数据库支持
系统支持MySQL和PostgreSQL数据库，并且支持多个MySQL实例的动态切换：

### 1. MySQL多实例支持
系统支持配置多个MySQL数据库实例，通过环境变量进行配置：
- `MYSQL_URL`: 默认数据库实例
- `MYSQL_URL_<INSTANCE_NAME>`: 其他数据库实例，如 `MYSQL_URL_BUSINESS`、`MYSQL_URL_LOG` 等

### 2. 数据库实例切换
可以在代码中动态切换数据库实例：

```go
// 获取所有MySQL实例
instances := store.GetMySQLInstances()
for _, instance := range instances {
    log.Printf("Found MySQL instance: %s", instance)
}

// 切换到指定的MySQL实例
err := store.SwitchDBInstance("business") // 切换到业务库
if err != nil {
    log.Printf("Failed to switch to business database: %v", err)
}

err = store.SwitchDBInstance("log") // 切换到日志库
if err != nil {
    log.Printf("Failed to switch to log database: %v", err)
}

// 获取当前数据库配置
config := store.GetCurrentDBConfig()
log.Printf("Current database: Type=%s, Instance=%s", config.Type, config.Instance)
```

### 3. PostgreSQL支持
系统同样支持PostgreSQL数据库：
```go
// 切换到PostgreSQL
err := store.SwitchDBInstance("postgres_default")
if err != nil {
    log.Printf("Failed to switch to PostgreSQL: %v", err)
}
```

## 注意事项
1. 确保配置的数据库连接信息正确
2. 数据库切换是线程安全的
3. 切换数据库时会自动维护连接池
4. 支持运行时动态切换数据库实例
5. 默认实例（MYSQL_URL）会自动进行表结构迁移
6. 其他实例需要手动管理表结构
7. 建议根据业务场景合理规划数据库实例
8. 注意监控数据库连接池的使用情况


### Redis
```go
// 1. 获取Redis管理器
redisManager := store.GetRedisManager()

// 2. 注册新的Redis实例
err := redisManager.RegisterRedis(store.RedisConfig{
    Instance: "cache",
    URL:      "redis://cache-server:6379",
    PoolSize: 10,
})

// 3. 连接到Redis实例
err = redisManager.ConnectRedis("cache")

// 4. 获取Redis客户端
client := redisManager.GetRedisClient("cache")

// 5. 获取默认Redis客户端
defaultClient := redisManager.GetDefaultRedisClient()

// 6. 关闭Redis实例
err = redisManager.CloseRedis("cache")

// 7. 关闭所有Redis实例
redisManager.CloseAllRedis()
```