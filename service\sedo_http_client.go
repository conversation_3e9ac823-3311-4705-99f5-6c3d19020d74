package service

import (
	"context"
	"fmt"
	"net/url"
	"os"
	"strconv"
	"sync"
	"time"

	"github.com/imroc/req/v3"
	"github.com/sirupsen/logrus"
)

// SedoHTTPClient Sedo API HTTP客户端
type SedoHTTPClient struct {
	client      *req.Client // 使用req/v3的Client替代http.Client
	baseURL     string
	bearerToken string
	oauthClient *SedoOAuthClient
	useOAuth    bool
	logger      *logrus.Logger
}

// SedoHTTPClientConfig HTTP客户端配置
type SedoHTTPClientConfig struct {
	BaseURL     string         // API基础URL
	BearerToken string         // Bearer令牌
	Timeout     time.Duration  // 超时时间
	UseOAuth    bool           // 是否使用OAuth
	Logger      *logrus.Logger // 日志记录器
}

var (
	sedoHTTPClient     *SedoHTTPClient
	sedoHTTPClientOnce sync.Once
)

// GetSedoHTTPClient 获取Sedo HTTP客户端单例
func GetSedoHTTPClient() *SedoHTTPClient {
	sedoHTTPClientOnce.Do(func() {
		// 从环境变量中获取配置
		baseURL := getEnvOrDefaultHTTP("SEDO_API_BASE_URL", "https://api.sedotmp.com/content/v1")
		bearerToken := getEnvOrDefaultHTTP("SEDO_API_TOKEN", "")
		timeout, _ := strconv.Atoi(getEnvOrDefaultHTTP("SEDO_API_TIMEOUT", "30"))
		useOAuth := getEnvOrDefaultHTTP("SEDO_USE_OAUTH", "false") == "true"

		// 创建日志记录器
		logger := logrus.New()
		level, err := logrus.ParseLevel(getEnvOrDefaultHTTP("LOG_LEVEL", "info"))
		if err == nil {
			logger.SetLevel(level)
		}

		sedoHTTPClient = NewSedoHTTPClient(SedoHTTPClientConfig{
			BaseURL:     baseURL,
			BearerToken: bearerToken,
			Timeout:     time.Duration(timeout) * time.Second,
			UseOAuth:    useOAuth,
			Logger:      logger,
		})
	})
	return sedoHTTPClient
}

// NewSedoHTTPClient 创建新的Sedo HTTP客户端
func NewSedoHTTPClient(config SedoHTTPClientConfig) *SedoHTTPClient {
	// 初始化req/v3客户端
	client := req.C().
		//SetTimeout(config.Timeout).
		// 创建的客户端不直接设置BaseURL，而是在请求时动态拼接
		// 添加请求前的日志记录
		OnBeforeRequest(func(c *req.Client, r *req.Request) error {
			method := r.Method
			url := r.RawURL
			config.Logger.Debugf("发送请求: %s %s", method, url)
			if r.Body != nil {
				config.Logger.Debugf("请求体: %s", r.Body)
			}
			return nil
		}).
		// 添加响应后的日志记录
		OnAfterResponse(func(c *req.Client, resp *req.Response) error {
			config.Logger.Debugf("收到响应: %d %s", resp.StatusCode, resp.Status)
			return nil
		})

	return &SedoHTTPClient{
		client:      client,
		baseURL:     config.BaseURL,
		bearerToken: config.BearerToken,
		useOAuth:    config.UseOAuth,
		oauthClient: GetSedoOAuthClient(),
		logger:      config.Logger,
	}
}

// SetBaseURL 设置API基础URL
func (c *SedoHTTPClient) SetBaseURL(baseURL string) {
	c.baseURL = baseURL
}

// SetBearerToken 设置Bearer令牌
func (c *SedoHTTPClient) SetBearerToken(token string) {
	c.bearerToken = token
}

// UseOAuth 启用OAuth身份验证
func (c *SedoHTTPClient) UseOAuth(enable bool) {
	c.useOAuth = enable
}

// SetOAuthCredentials 设置OAuth凭据
func (c *SedoHTTPClient) SetOAuthCredentials(clientID, clientSecret string) {
	c.oauthClient.SetCredentials(clientID, clientSecret)
	c.useOAuth = true
}

// GetBearerToken 获取Bearer令牌（优先使用OAuth获取）
func (c *SedoHTTPClient) GetBearerToken(ctx context.Context) (string, error) {
	if c.useOAuth && c.oauthClient.IsConfigured() {
		return c.oauthClient.GetToken(ctx)
	}

	if c.bearerToken == "" {
		return "", fmt.Errorf("未设置Bearer令牌，也未配置OAuth")
	}

	return c.bearerToken, nil
}

// setAuth 为请求设置认证信息
func (c *SedoHTTPClient) setAuth(ctx context.Context, r *req.Request) error {
	token, err := c.GetBearerToken(ctx)
	if err != nil {
		return fmt.Errorf("获取认证令牌失败: %v", err)
	}

	if token != "" {
		r.SetBearerAuthToken(token)
	}
	return nil
}

// Get 发送GET请求
func (c *SedoHTTPClient) Get(ctx context.Context, path string, query url.Values, headers map[string]string) (*req.Response, error) {
	return c.Request(ctx, "GET", path, query, nil, headers)
}

// Post 发送POST请求
func (c *SedoHTTPClient) Post(ctx context.Context, path string, query url.Values, body interface{}, headers map[string]string) (*req.Response, error) {
	return c.Request(ctx, "POST", path, query, body, headers)
}

// Put 发送PUT请求
func (c *SedoHTTPClient) Put(ctx context.Context, path string, query url.Values, body interface{}, headers map[string]string) (*req.Response, error) {
	return c.Request(ctx, "PUT", path, query, body, headers)
}

// Patch 发送PATCH请求
func (c *SedoHTTPClient) Patch(ctx context.Context, path string, query url.Values, body interface{}, headers map[string]string) (*req.Response, error) {
	return c.Request(ctx, "PATCH", path, query, body, headers)
}

// Delete 发送DELETE请求
func (c *SedoHTTPClient) Delete(ctx context.Context, path string, query url.Values, headers map[string]string) (*req.Response, error) {
	return c.Request(ctx, "DELETE", path, query, nil, headers)
}

// Request 发送HTTP请求
func (c *SedoHTTPClient) Request(ctx context.Context, method, path string, query url.Values, body interface{}, headers map[string]string) (*req.Response, error) {
	// 构建完整URL
	fullURL := c.baseURL + path

	// 创建新的请求
	r := c.client.R().
		SetContext(ctx)

	// 设置查询参数
	if query != nil {
		for k, values := range query {
			for _, v := range values {
				r.SetQueryParam(k, v)
			}
		}
	}

	// 设置请求体
	if body != nil {
		r.SetBody(body)
	}

	// 设置请求头
	r.SetHeaders(map[string]string{
		"Accept": "application/json",
	})

	// 如果有请求体，设置Content-Type
	if body != nil {
		r.SetHeader("Content-Type", "application/json")
	}

	// 设置自定义请求头
	if headers != nil {
		r.SetHeaders(headers)
	}

	// 设置认证信息
	if err := c.setAuth(ctx, r); err != nil {
		return nil, err
	}

	// 发送请求
	resp, err := r.Send(method, fullURL)
	if err != nil {
		return nil, fmt.Errorf("发送请求失败: %v", err)
	}

	// 处理401未授权错误（可能需要刷新令牌）
	if resp.StatusCode == 401 && c.useOAuth {
		c.logger.Debug("收到未授权响应，尝试刷新令牌")
		c.oauthClient.ClearToken()

		// 获取新令牌
		_, err := c.oauthClient.GetToken(ctx)
		if err != nil {
			return nil, fmt.Errorf("刷新令牌失败: %v", err)
		}

		// 重新设置认证
		if err := c.setAuth(ctx, r); err != nil {
			return nil, err
		}

		// 重试请求
		c.logger.Debug("使用新令牌重试请求")
		resp, err = r.Send(method, fullURL)
		if err != nil {
			return nil, fmt.Errorf("重试请求失败: %v", err)
		}
	}

	return resp, nil
}

// UploadFile 上传文件
func (c *SedoHTTPClient) UploadFile(ctx context.Context, path string, filePath string, fieldName string, headers map[string]string) (*req.Response, error) {
	// 检查文件是否存在
	if _, err := os.Stat(filePath); err != nil {
		return nil, fmt.Errorf("文件不存在或无法访问: %v", err)
	}

	// 构建完整URL
	fullURL := c.baseURL + path

	// 创建新的请求
	r := c.client.R().
		SetContext(ctx).
		SetFile(fieldName, filePath)

	// 设置自定义请求头
	if headers != nil {
		r.SetHeaders(headers)
	}

	// 设置认证信息
	if err := c.setAuth(ctx, r); err != nil {
		return nil, err
	}

	// 发送请求
	resp, err := r.Post(fullURL)
	if err != nil {
		return nil, fmt.Errorf("上传文件失败: %v", err)
	}

	// 处理401未授权错误（可能需要刷新令牌）
	if resp.StatusCode == 401 && c.useOAuth {
		c.logger.Debug("上传文件时收到未授权响应，尝试刷新令牌")
		c.oauthClient.ClearToken()

		// 获取新令牌
		_, err := c.oauthClient.GetToken(ctx)
		if err != nil {
			return nil, fmt.Errorf("刷新令牌失败: %v", err)
		}

		// 重新创建请求（因为文件已经被消费）
		r = c.client.R().
			SetContext(ctx).
			SetFile(fieldName, filePath)

		// 重新设置请求头
		if headers != nil {
			r.SetHeaders(headers)
		}

		// 重新设置认证
		if err := c.setAuth(ctx, r); err != nil {
			return nil, err
		}

		// 重试请求
		c.logger.Debug("使用新令牌重试上传请求")
		resp, err = r.Post(fullURL)
		if err != nil {
			return nil, fmt.Errorf("重试上传文件失败: %v", err)
		}
	}

	return resp, nil
}

// ParseResponse 解析响应
func (c *SedoHTTPClient) ParseResponse(resp *req.Response, result interface{}) error {
	// 检查响应状态码
	if resp.IsError() {
		var errResp struct {
			Type       string `json:"type"`
			Title      string `json:"title"`
			Status     int    `json:"status"`
			Detail     string `json:"detail"`
			Instance   string `json:"instance"`
			Violations []struct {
				Field   string `json:"field"`
				Message string `json:"message"`
			}
		}

		if err := resp.UnmarshalJson(&errResp); err == nil && errResp.Title != "" {
			return fmt.Errorf("API错误 (%d): %s - %s", resp.StatusCode, errResp.Title, errResp.Violations)
		}

		// 如果无法解析为标准错误响应，则返回原始错误信息
		return fmt.Errorf("API错误 (%d): %s", resp.StatusCode, resp.String())
	}

	// 解析响应体到结果对象
	if result != nil {
		if err := resp.UnmarshalJson(result); err != nil {
			return fmt.Errorf("解析响应体失败: %v, 原始数据: %s", err, resp.String())
		}
	}

	return nil
}

// DownloadFile 下载文件
func (c *SedoHTTPClient) DownloadFile(ctx context.Context, url string, destPath string) error {
	// 创建新的请求
	r := c.client.R().
		SetContext(ctx).
		SetOutputFile(destPath)

	// 设置认证信息
	if err := c.setAuth(ctx, r); err != nil {
		return err
	}

	// 发送请求
	resp, err := r.Get(url)
	if err != nil {
		return fmt.Errorf("下载文件失败: %v", err)
	}

	// 检查响应状态码
	if resp.IsError() {
		return fmt.Errorf("下载文件失败，HTTP状态码: %d", resp.StatusCode)
	}

	return nil
}

// GetPageHeaders 获取分页响应头
func (c *SedoHTTPClient) GetPageHeaders(resp *req.Response) (totalCount, totalPages int, err error) {
	totalCountStr := resp.Header.Get("X-Total-Count")
	totalPagesStr := resp.Header.Get("X-Total-Pages")

	if totalCountStr != "" {
		totalCount, err = strconv.Atoi(totalCountStr)
		if err != nil {
			return 0, 0, fmt.Errorf("解析X-Total-Count头失败: %v", err)
		}
	}

	if totalPagesStr != "" {
		totalPages, err = strconv.Atoi(totalPagesStr)
		if err != nil {
			return 0, 0, fmt.Errorf("解析X-Total-Pages头失败: %v", err)
		}
	}

	return totalCount, totalPages, nil
}

// BuildQueryParams 构建查询参数
func (c *SedoHTTPClient) BuildQueryParams(page, size int, sort, term string) url.Values {
	query := url.Values{}

	if page >= 0 {
		query.Set("page", strconv.Itoa(page))
	}

	if size > 0 {
		query.Set("size", strconv.Itoa(size))
	}

	if sort != "" {
		query.Set("sort", sort)
	}

	if term != "" {
		query.Set("term", term)
	}

	return query
}

// getEnvOrDefaultHTTP 获取环境变量值，如果不存在则返回默认值
func getEnvOrDefaultHTTP(key, defaultValue string) string {
	value := os.Getenv(key)
	if value == "" {
		return defaultValue
	}
	return value
}
