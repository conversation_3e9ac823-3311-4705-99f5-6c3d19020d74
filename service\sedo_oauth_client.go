package service

import (
	"context"
	"fmt"
	"os"
	"sync"
	"time"

	"github.com/imroc/req/v3"
	"github.com/sirupsen/logrus"
)

// SedoOAuthConfig OAuth客户端配置
type SedoOAuthConfig struct {
	TokenEndpoint string         // 令牌端点URL
	ClientID      string         // 客户端ID
	ClientSecret  string         // 客户端密钥
	Audience      string         // 目标受众
	GrantType     string         // 授权类型
	Logger        *logrus.Logger // 日志记录器
}

// SedoTokenResponse OAuth令牌响应
type SedoTokenResponse struct {
	AccessToken string `json:"access_token"`    // 访问令牌
	TokenType   string `json:"token_type"`      // 令牌类型
	ExpiresIn   int    `json:"expires_in"`      // 过期时间（秒）
	Scope       string `json:"scope,omitempty"` // 作用域
}

// SedoOAuthClient OAuth客户端
type SedoOAuthClient struct {
	config         SedoOAuthConfig // 配置
	client         *req.Client     // 使用req/v3替代标准库http.Client
	currentToken   string          // 当前令牌
	tokenExpiresAt time.Time       // 令牌过期时间
	mutex          sync.Mutex      // 互斥锁
	logger         *logrus.Logger  // 日志记录器
}

var (
	sedoOAuthClient     *SedoOAuthClient
	sedoOAuthClientOnce sync.Once
)

// GetSedoOAuthClient 获取OAuth客户端单例
func GetSedoOAuthClient() *SedoOAuthClient {
	sedoOAuthClientOnce.Do(func() {
		// 从环境变量获取配置
		tokenEndpoint := getEnvOrDefaultOAuth("SEDO_TOKEN_ENDPOINT", "https://auth.sedotmp.com/oauth/token")
		clientID := getEnvOrDefaultOAuth("SEDO_CLIENT_ID", "")
		clientSecret := getEnvOrDefaultOAuth("SEDO_CLIENT_SECRET", "")
		audience := getEnvOrDefaultOAuth("SEDO_AUDIENCE", "https://api.sedotmp.com/")
		grantType := getEnvOrDefaultOAuth("SEDO_GRANT_TYPE", "client_credentials")

		// 创建日志记录器
		logger := logrus.New()
		level, err := logrus.ParseLevel(getEnvOrDefaultOAuth("LOG_LEVEL", "info"))
		if err == nil {
			logger.SetLevel(level)
		}

		sedoOAuthClient = NewSedoOAuthClient(SedoOAuthConfig{
			TokenEndpoint: tokenEndpoint,
			ClientID:      clientID,
			ClientSecret:  clientSecret,
			Audience:      audience,
			GrantType:     grantType,
			Logger:        logger,
		})
	})
	return sedoOAuthClient
}

// NewSedoOAuthClient 创建新的OAuth客户端
func NewSedoOAuthClient(config SedoOAuthConfig) *SedoOAuthClient {
	// 创建req/v3客户端
	client := req.C().
		SetTimeout(30 * time.Second).
		EnableDumpAll().
		OnBeforeRequest(func(c *req.Client, r *req.Request) error {
			if config.Logger != nil {
				config.Logger.Debugf("OAuth请求: %s %s", r.Method, r.RawURL)
			}
			return nil
		}).
		OnAfterResponse(func(c *req.Client, resp *req.Response) error {
			if config.Logger != nil {
				config.Logger.Debugf("OAuth响应: %d %s", resp.StatusCode, resp.Status)
			}
			return nil
		})

	return &SedoOAuthClient{
		config: config,
		client: client,
		logger: config.Logger,
	}
}

// SetConfig 设置OAuth配置
func (c *SedoOAuthClient) SetConfig(config SedoOAuthConfig) {
	c.mutex.Lock()
	defer c.mutex.Unlock()

	c.config = config
	// 重置令牌，以便下次请求时获取新令牌
	c.currentToken = ""
	c.tokenExpiresAt = time.Time{}
}

// SetCredentials 设置OAuth凭据
func (c *SedoOAuthClient) SetCredentials(clientID, clientSecret string) {
	c.mutex.Lock()
	defer c.mutex.Unlock()

	c.config.ClientID = clientID
	c.config.ClientSecret = clientSecret
	// 重置令牌，以便下次请求时获取新令牌
	c.currentToken = ""
	c.tokenExpiresAt = time.Time{}
}

// GetToken 获取访问令牌（如果需要，则从服务器获取）
func (c *SedoOAuthClient) GetToken(ctx context.Context) (string, error) {
	c.mutex.Lock()
	defer c.mutex.Unlock()

	// 检查令牌是否存在且有效
	if c.currentToken != "" && time.Now().Before(c.tokenExpiresAt) {
		return c.currentToken, nil
	}

	// 如果没有凭据，则无法获取令牌
	if c.config.ClientID == "" || c.config.ClientSecret == "" {
		return "", fmt.Errorf("未设置OAuth凭据")
	}

	// 获取新令牌
	token, expiresIn, err := c.fetchToken(ctx)
	if err != nil {
		return "", err
	}

	// 设置令牌和过期时间（比实际过期时间提前60秒，以确保安全边界）
	c.currentToken = token
	c.tokenExpiresAt = time.Now().Add(time.Duration(expiresIn-60) * time.Second)

	return c.currentToken, nil
}

// fetchToken 从服务器获取新令牌
func (c *SedoOAuthClient) fetchToken(ctx context.Context) (string, int, error) {
	c.logger.Debugf("正在从 %s 获取OAuth令牌", c.config.TokenEndpoint)

	// 准备请求体
	requestBody := map[string]string{
		"client_id":     c.config.ClientID,
		"client_secret": c.config.ClientSecret,
		"audience":      c.config.Audience,
		"grant_type":    c.config.GrantType,
	}

	// 使用req/v3发送请求
	var tokenResp SedoTokenResponse
	resp, err := c.client.R().
		SetContext(ctx).
		SetBody(requestBody).
		SetHeader("Content-Type", "application/json").
		SetHeader("Accept", "application/json").
		SetResult(&tokenResp). // 自动解析响应到结构体
		Post(c.config.TokenEndpoint)

	if err != nil {
		return "", 0, fmt.Errorf("发送OAuth请求失败: %v", err)
	}

	// 检查响应状态
	if resp.IsError() {
		return "", 0, fmt.Errorf("OAuth服务器返回错误状态码 %d: %s", resp.StatusCode, resp.String())
	}

	// 确保获取到了令牌
	if tokenResp.AccessToken == "" {
		return "", 0, fmt.Errorf("OAuth响应中未包含访问令牌")
	}

	c.logger.Debugf("成功获取OAuth令牌，有效期为 %d 秒", tokenResp.ExpiresIn)

	return tokenResp.AccessToken, tokenResp.ExpiresIn, nil
}

// ClearToken 清除当前令牌
func (c *SedoOAuthClient) ClearToken() {
	c.mutex.Lock()
	defer c.mutex.Unlock()

	c.currentToken = ""
	c.tokenExpiresAt = time.Time{}
}

// IsConfigured 检查是否已配置OAuth客户端
func (c *SedoOAuthClient) IsConfigured() bool {
	return c.config.ClientID != "" && c.config.ClientSecret != ""
}

// TokenEndpointOrDefault 获取令牌端点，如果未设置则使用默认值
func (c *SedoOAuthClient) TokenEndpointOrDefault() string {
	if c.config.TokenEndpoint == "" {
		return "https://auth.sedotmp.com/oauth/token"
	}
	return c.config.TokenEndpoint
}

// AudienceOrDefault 获取受众，如果未设置则使用默认值
func (c *SedoOAuthClient) AudienceOrDefault() string {
	if c.config.Audience == "" {
		return "https://api.sedotmp.com/"
	}
	return c.config.Audience
}

// GrantTypeOrDefault 获取授权类型，如果未设置则使用默认值
func (c *SedoOAuthClient) GrantTypeOrDefault() string {
	if c.config.GrantType == "" {
		return "client_credentials"
	}
	return c.config.GrantType
}

// getEnvOrDefaultOAuth 获取环境变量，如果不存在则返回默认值
func getEnvOrDefaultOAuth(key, defaultValue string) string {
	value := os.Getenv(key)
	if value == "" {
		return defaultValue
	}
	return value
}
