package dao

import (
	"rsoc-system-go/model"

	"gorm.io/gorm"
)

type TaskDao struct {
	db *gorm.DB
}

func NewTaskDao(db *gorm.DB) *TaskDao {
	return &TaskDao{db: db}
}

// Create 创建任务
func (d *TaskDao) Create(task *model.Task) error {
	return d.db.Create(task).Error
}

// GetByID 根据ID获取任务
func (d *TaskDao) GetByID(id int) (*model.Task, error) {
	var task model.Task
	err := d.db.First(&task, id).Error
	if err != nil {
		return nil, err
	}
	return &task, nil
}

// Update 更新任务
func (d *TaskDao) Update(task *model.Task) error {
	return d.db.Save(task).Error
}

// Delete 删除任务
func (d *TaskDao) Delete(id int) error {
	return d.db.Delete(&model.Task{}, id).Error
}

// List 获取任务列表
func (d *TaskDao) List(page *int, conditions map[string]interface{}) ([]*model.Task, error) {
	var tasks []*model.Task
	query := d.db.Model(&model.Task{})

	// 添加条件
	for key, value := range conditions {
		query = query.Where(key+" = ?", value)
	}

	// 添加分页
	if page != nil {
		limit := 10
		offset := (*page - 1) * limit
		query = query.Limit(limit).Offset(offset)
	}

	err := query.Find(&tasks).Error
	if err != nil {
		return nil, err
	}

	return tasks, nil
}
