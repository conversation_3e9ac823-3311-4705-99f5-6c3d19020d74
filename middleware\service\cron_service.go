package service

import (
	"rsoc-system-go/middleware/jobs"
	"rsoc-system-go/middleware/pkg/scheduler"
	"time"

	sysdao "rsoc-system-go/dao/system"
	sysmodel "rsoc-system-go/model/system"
)

type CronService struct {
	sysdao.CronDao
}

// InitCronTasks 初始化所有启用的定时任务
func (s *CronService) InitCronTasks() error {
	// TODO 暂时使用自定义
	// 获取所有启用的定时任务
	/*	tasks, err := s.GetCronList(&sysmodel.CronEntity{Status: 1})
		if err != nil {
			return err
		}*/

	tasks := []*sysmodel.CronEntity{
		{
			Id:        3,
			TaskName:  "同步国内rsoc收益",
			TaskGroup: "test",
			CronExpr:  "0 0/5 * * * ?",
			JobClass:  jobs.GoogleReportSyncJobClass,
			Status:    1,
		},
		{
			Id:        2,
			TaskName:  "同步token到redis",
			TaskGroup: "test",
			CronExpr:  "* * * * * ?",
			JobClass:  "user_token_job",
			Status:    1,
		},
		//{
		//	Id:        4,
		//	TaskName:  "生成任务数据",
		//	TaskGroup: "test",
		//	CronExpr:  "* * * * * ?",
		//	JobClass:  "gen_data_send_job",
		//	Status:    1,
		//},
		//{
		//	Id:        5,
		//	TaskName:  "同步GoogleArticle状态",
		//	TaskGroup: "google",
		//	CronExpr:  "0 */5 * * * ?", // 每5分钟执行一次
		//	JobClass:  jobs.GoogleArticleSyncJobClass,
		//	Status:    1,
		//},
	}

	// 获取调度器实例
	sch := scheduler.GetScheduler()

	// 添加所有任务到调度器
	for _, task := range tasks {
		if err := sch.AddJob(task); err != nil {
			return err
		}
	}

	return nil
}

// CreateCronTask 创建定时任务
func (s *CronService) CreateCronTask(task *sysmodel.CronEntity) (*sysmodel.CronEntity, error) {
	task.CreateTime = time.Now().Format(time.DateTime)

	// 保存到数据库
	result, err := s.InsertCron(task)
	if err != nil {
		return nil, err
	}

	// 如果任务是启用状态，添加到调度器
	if task.Status == 1 {
		sch := scheduler.GetScheduler()
		if err := sch.AddJob(task); err != nil {
			return nil, err
		}
	}

	return result, nil
}

// UpdateCronTask 更新定时任务
func (s *CronService) UpdateCronTask(task *sysmodel.CronEntity) (*sysmodel.CronEntity, error) {
	task.UpdateTime = time.Now().Format(time.DateTime)

	// 更新数据库
	result, err := s.UpdateCron(task)
	if err != nil {
		return nil, err
	}

	// 更新调度器中的任务
	sch := scheduler.GetScheduler()
	sch.UpdateJobStatus(task)

	return result, nil
}

// DeleteCronTask 删除定时任务
func (s *CronService) DeleteCronTask(id int64) error {
	// 从调度器中移除任务
	sch := scheduler.GetScheduler()
	sch.RemoveJob(id)

	// 从数据库中删除任务
	return s.DeleteCron(id)
}

// UpdateCronTaskStatus 更新定时任务状态
func (s *CronService) UpdateCronTaskStatus(task *sysmodel.CronEntity) (*sysmodel.CronEntity, error) {
	// 更新数据库
	result, err := s.UpdateCronStatus(task)
	if err != nil {
		return nil, err
	}

	// 更新调度器中的任务状态
	sch := scheduler.GetScheduler()
	sch.UpdateJobStatus(task)

	return result, nil
}
