package config

import (
	"fmt"
	"log"
	"os"
	"rsoc-system-go/model"
	"rsoc-system-go/store"
	"strings"

	"gorm.io/gorm/logger"

	_ "rsoc-system-go/middleware"

	dmysql "github.com/go-sql-driver/mysql"
	"gorm.io/driver/mysql"
	"gorm.io/gorm"
)

var mysqlName = "mysql"

func init() {
	go initMySQL()
}

// initMySQL 初始化MySQL数据库连接
func initMySQL() {
	// 获取所有MySQL数据库配置
	mysqlConfigs := getMySQLConfigs()
	if len(mysqlConfigs) == 0 {
		return
	}

	dbManager := store.GetDBManager()
	var defaultDB *gorm.DB

	// 初始化每个MySQL实例
	for instance, dsn := range mysqlConfigs {
		if err := validateMySQLDSN(dsn); err != nil {
			log.Printf("[%s] Invalid MySQL DSN for instance %s: %v", mysqlName, instance, err)
			continue
		}

		log.Printf("[%s] Initializing MySQL instance: %s", mysqlName, instance)
		log.Printf("[%s] DSN: %s", mysqlName, dsn)

		// 注册数据库配置
		dbManager.RegisterDB(store.DBConfig{
			Type:     store.MySQL,
			Instance: store.DBInstance(instance),
			DSN:      dsn,
		})

		config := &gorm.Config{
			PrepareStmt: true,
			//Logger:      logger.Default.LogMode(logger.Info),
		}
		if os.Getenv("dblog") != "" {
			config.Logger = logger.Default.LogMode(logger.Info)
		}
		// 创建数据库连接
		db, err := gorm.Open(mysql.Open(dsn), config)

		if err != nil {
			log.Printf("[%s] Failed to connect to MySQL instance %s: %v", mysqlName, instance, err)
			continue
		}

		// 设置到数据库管理器
		if err := dbManager.SetDB(store.DBInstance(instance), db); err != nil {
			log.Printf("[%s] Failed to set MySQL instance %s: %v", mysqlName, instance, err)
			continue
		}

		// 如果是默认实例，保存数据库连接
		if instance == "default" {
			defaultDB = db
			dbManager.SetDefaultDB(store.DBInstance(instance))
		}

		// 初始化表结构（仅对默认实例）
		if instance == "default" {
			// 获取所有表名
			var tables []string
			if err := db.Raw("SHOW TABLES").Scan(&tables).Error; err != nil {
				log.Printf("[%s] Failed to get table names for instance %s: %v", mysqlName, instance, err)
				continue
			}
			//middleware.InitTable()
			if err := db.Set("gorm:table_options", "ENGINE=InnoDB").AutoMigrate(
				model.ReplayTask{},
				model.RsocKey{},
				model.GoogleArticle{},
				model.GoogleReport{},
			); err != nil {
				log.Printf("[%s] Failed to auto migrate tables for instance %s: %v", mysqlName, instance, err)
				continue
			}
		}

		log.Printf("[%s] MySQL instance %s connected successfully", mysqlName, instance)
	}

	// 如果有默认实例连接成功，设置为全局DB
	if defaultDB != nil {
		store.SetDB(defaultDB)
		log.Printf("[%s] Default MySQL instance set successfully", mysqlName)
	}

	// 检查所有必需的数据库连接是否就绪
	store.CheckDatabaseConnections()
}

// getMySQLConfigs 获取所有MySQL数据库配置
func getMySQLConfigs() map[string]string {
	configs := make(map[string]string)

	// 处理默认数据库配置
	if dsn := os.Getenv("MYSQL_URL"); dsn != "" {
		configs["default"] = dsn
	}

	// 处理其他数据库实例配置（格式：MYSQL_URL_INSTANCE_NAME=dsn）
	for _, env := range os.Environ() {
		if !strings.HasPrefix(env, "MYSQL_URL_") {
			continue
		}

		parts := strings.SplitN(env, "=", 2)
		if len(parts) != 2 {
			continue
		}

		instance := strings.TrimPrefix(parts[0], "MYSQL_URL_")
		if instance == "" {
			continue
		}

		configs[strings.ToLower(instance)] = parts[1]
	}

	return configs
}

// validateMySQLDSN 验证MySQL DSN格式
func validateMySQLDSN(dsn string) error {
	_, err := dmysql.ParseDSN(dsn)
	if err != nil {
		log.Printf("[%s] invalid MySQL DSN: %v", mysqlName, err)
		return fmt.Errorf("[%s] invalid MySQL DSN: %v", mysqlName, err)
	}
	return nil
}
