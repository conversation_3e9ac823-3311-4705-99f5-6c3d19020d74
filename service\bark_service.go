package service

import (
	"fmt"
	"log"
	"net/http"
	"net/url"
	"sync"
	"time"
)

// BarkConfig Bark配置
type BarkConfig struct {
	ServerURL string // Bark服务器地址
	DeviceKey string // 设备密钥
	Retries   int
	Timeout   time.Duration
}

// BarkService Bark服务
type BarkService struct {
	config BarkConfig
	client *http.Client
}

var (
	barkService     *BarkService
	barkServiceOnce sync.Once
)

// GetBarkService 获取Bark服务实例
func GetBarkService() *BarkService {
	barkServiceOnce.Do(func() {
		barkService = NewBarkService(BarkConfig{
			ServerURL: getEnvOrDefault("BARK_SERVER_URL", "https://api.day.app"),
			DeviceKey: getEnvOrDefault("BARK_DEVICE_KEY", ""),
			Retries:   3,
			Timeout:   10 * time.Second,
		})
	})
	return barkService
}

// NewBarkService 创建新的Bark服务
func NewBarkService(config BarkConfig) *BarkService {
	return &BarkService{
		config: config,
		client: &http.Client{
			Timeout: config.Timeout,
		},
	}
}

// SendNotification 发送通知到Bark
func (s *BarkService) SendNotification(title, body string, options map[string]string) error {
	if s.config.DeviceKey == "" {
		return fmt.Errorf("Bark设备密钥未配置")
	}

	// 构建请求URL
	baseURL := fmt.Sprintf("%s/%s", s.config.ServerURL, s.config.DeviceKey)

	// URL编码标题和内容
	encodedTitle := url.PathEscape(title)
	encodedBody := url.PathEscape(body)
	requestURL := fmt.Sprintf("%s/%s/%s", baseURL, encodedTitle, encodedBody)

	// 添加额外参数
	params := url.Values{}
	for key, value := range options {
		params.Add(key, value)
	}
	if len(params) > 0 {
		requestURL = fmt.Sprintf("%s?%s", requestURL, params.Encode())
	}

	for i := 0; i < s.config.Retries; i++ {
		req, err := http.NewRequest("GET", requestURL, nil)
		if err != nil {
			log.Printf("创建Bark请求失败: %v", err)
			continue
		}

		resp, err := s.client.Do(req)
		if err != nil {
			log.Printf("Bark通知发送失败(尝试 %d/%d): %v", i+1, s.config.Retries, err)
			if i < s.config.Retries-1 {
				time.Sleep(2 * time.Second)
				continue
			}
			return err
		}
		defer resp.Body.Close()

		if resp.StatusCode != http.StatusOK {
			log.Printf("Bark API返回错误(尝试 %d/%d): 状态码 %d", i+1, s.config.Retries, resp.StatusCode)
			if i < s.config.Retries-1 {
				time.Sleep(2 * time.Second)
				continue
			}
			return fmt.Errorf("Bark API返回错误状态码: %d", resp.StatusCode)
		}

		log.Printf("Bark通知发送成功")
		return nil
	}

	return fmt.Errorf("发送Bark通知失败，已达到最大重试次数")
}

// SendErrorReport 发送错误报告
func (s *BarkService) SendErrorReport(err interface{}, stack string) error {
	title := "程序错误报告"
	body := fmt.Sprintf("错误: %v\n\n堆栈信息:\n%s", err, stack)

	options := map[string]string{
		"level":     "timeSensitive", // 设置为时间敏感通知
		"badge":     "1",
		"sound":     "default",
		"group":     "error", // 分组标识
		"isArchive": "1",     // 保存到通知历史
	}

	return s.SendNotification(title, body, options)
}
