package service

import (
	"context"
	"encoding/json"
	"fmt"
	"net/url"
	"os"
	"strconv"
	"sync"
	"time"

	"github.com/imroc/req/v3"
	"github.com/sirupsen/logrus"
)

// AdTechHTTPClient AdTech API HTTP客户端
type AdTechHTTPClient struct {
	client      *req.Client
	baseURL     string
	securityKey string
	logger      *logrus.Logger
}

// AdTechHTTPClientConfig HTTP客户端配置
type AdTechHTTPClientConfig struct {
	BaseURL     string         // API基础URL
	SecurityKey string         // 安全密钥
	Timeout     time.Duration  // 超时时间
	Logger      *logrus.Logger // 日志记录器
}

var (
	adTechHTTPClient     *AdTechHTTPClient
	adTechHTTPClientOnce sync.Once
)

// GetAdTechHTTPClient 获取AdTech HTTP客户端单例
func GetAdTechHTTPClient() *AdTechHTTPClient {
	adTechHTTPClientOnce.Do(func() {
		// 从环境变量中获取配置
		baseURL := getAdTechEnvOrDefault("ADTECH_API_BASE_URL", "https://adtechapi.com/v2")
		securityKey := getAdTechEnvOrDefault("ADTECH_SECURITY_KEY", "")
		timeout, _ := strconv.Atoi(getAdTechEnvOrDefault("ADTECH_API_TIMEOUT", "30"))

		// 创建日志记录器
		logger := logrus.New()
		level, err := logrus.ParseLevel(getAdTechEnvOrDefault("LOG_LEVEL", "info"))
		if err == nil {
			logger.SetLevel(level)
		}

		adTechHTTPClient = NewAdTechHTTPClient(AdTechHTTPClientConfig{
			BaseURL:     baseURL,
			SecurityKey: securityKey,
			Timeout:     time.Duration(timeout) * time.Second,
			Logger:      logger,
		})
	})
	return adTechHTTPClient
}

// NewAdTechHTTPClient 创建新的AdTech HTTP客户端
func NewAdTechHTTPClient(config AdTechHTTPClientConfig) *AdTechHTTPClient {
	// 初始化req/v3客户端
	client := req.C().
		SetTimeout(config.Timeout).
		OnBeforeRequest(func(c *req.Client, r *req.Request) error {
			method := r.Method
			url := r.RawURL
			config.Logger.Debugf("发送请求: %s %s", method, url)
			if r.Body != nil {
				config.Logger.Debugf("请求体: %v", r.Body)
			}
			return nil
		}).
		OnAfterResponse(func(c *req.Client, resp *req.Response) error {
			config.Logger.Debugf("收到响应: %d %s", resp.StatusCode, resp.Status)
			return nil
		})

	return &AdTechHTTPClient{
		client:      client,
		baseURL:     config.BaseURL,
		securityKey: config.SecurityKey,
		logger:      config.Logger,
	}
}

// SetBaseURL 设置API基础URL
func (c *AdTechHTTPClient) SetBaseURL(baseURL string) {
	c.baseURL = baseURL
}

// SetSecurityKey 设置安全密钥
func (c *AdTechHTTPClient) SetSecurityKey(securityKey string) {
	c.securityKey = securityKey
}

// Get 发送GET请求
func (c *AdTechHTTPClient) Get(ctx context.Context, path string, query url.Values) (*req.Response, error) {
	return c.Request(ctx, "GET", path, query, nil, nil)
}

// Request 发送HTTP请求
func (c *AdTechHTTPClient) Request(ctx context.Context, method, path string, query url.Values, body interface{}, headers map[string]string) (*req.Response, error) {
	// 构建完整URL
	fullURL := c.baseURL + path

	// 添加security_key查询参数
	if query == nil {
		query = url.Values{}
	}
	query.Set("security_key", c.securityKey)

	// 创建新的请求
	r := c.client.R().
		SetContext(ctx)

	// 设置查询参数
	for k, values := range query {
		for _, v := range values {
			r.SetQueryParam(k, v)
		}
	}

	// 设置请求体（如果有）
	if body != nil {
		r.SetBody(body)
	}

	// 设置请求头
	if headers != nil {
		r.SetHeaders(headers)
	}

	// 发送请求
	resp, err := r.Send(method, fullURL)
	if err != nil {
		return nil, fmt.Errorf("发送请求失败: %v", err)
	}

	return resp, nil
}

// CreateCampaignWithFormData 使用表单数据创建广告系列
func (c *AdTechHTTPClient) CreateCampaignWithFormData(ctx context.Context, publishDomainName string, article map[string]interface{}, campaign map[string]interface{}) (*req.Response, error) {
	// 构建完整URL（包含security_key参数）
	fullURL := fmt.Sprintf("%s/campaign/create?security_key=%s", c.baseURL, c.securityKey)

	// 创建请求
	r := c.client.R().
		SetContext(ctx).
		SetFormData(map[string]string{
			"publishDomainName": publishDomainName,
		})

	// 添加复杂表单字段
	err := addNestedFormData(r, "article", article)
	if err != nil {
		return nil, fmt.Errorf("添加文章表单数据失败: %v", err)
	}

	err = addNestedFormData(r, "campaign", campaign)
	if err != nil {
		return nil, fmt.Errorf("添加广告系列表单数据失败: %v", err)
	}

	// 发送请求
	resp, err := r.Post(fullURL)
	if err != nil {
		return nil, fmt.Errorf("发送请求失败: %v", err)
	}

	return resp, nil
}

// addNestedFormData 添加嵌套表单数据
func addNestedFormData(r *req.Request, prefix string, data map[string]interface{}) error {
	for key, value := range data {
		switch v := value.(type) {
		case string:
			r.SetFormData(map[string]string{
				fmt.Sprintf("%s[%s]", prefix, key): v,
			})
		case []string:
			for _, item := range v {
				r.SetFormData(map[string]string{
					fmt.Sprintf("%s[%s][]", prefix, key): item,
				})
			}
		case map[string]interface{}:
			err := addNestedFormData(r, fmt.Sprintf("%s[%s]", prefix, key), v)
			if err != nil {
				return err
			}
		default:
			// 对于其他类型，转换为字符串
			jsonValue, err := json.Marshal(v)
			if err != nil {
				return err
			}
			r.SetFormData(map[string]string{
				fmt.Sprintf("%s[%s]", prefix, key): string(jsonValue),
			})
		}
	}
	return nil
}

// ParseResponse 解析响应
func (c *AdTechHTTPClient) ParseResponse(resp *req.Response, result interface{}) error {
	// 检查响应状态码
	if resp.IsError() {
		return fmt.Errorf("API错误 (%d): %s", resp.StatusCode, resp.String())
	}

	// 解析响应体到结果对象
	if result != nil {
		if err := resp.UnmarshalJson(result); err != nil {
			return fmt.Errorf("解析响应体失败: %v, 原始数据: %s", err, resp.String())
		}
	}

	return nil
}

// getAdTechEnvOrDefault 获取环境变量或使用默认值
func getAdTechEnvOrDefault(key, defaultValue string) string {
	value := os.Getenv(key)
	if value == "" {
		return defaultValue
	}
	return value
}
