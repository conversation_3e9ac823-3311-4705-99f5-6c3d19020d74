package model

import (
	"database/sql"
	"time"
)

// FacebookInsights Facebook广告数据洞察
type FacebookInsights struct {
	ID          int          `gorm:"column:id;primaryKey;autoIncrement" json:"id,omitempty"`
	Date        string       `gorm:"column:date;type:varchar(20);not null" json:"date,omitempty"` // 广告账户所在时区的时间
	UserID      int          `gorm:"column:userId;not null" json:"userId,omitempty"`
	AccountID   string       `gorm:"column:account_id;type:varchar(20);not null" json:"accountId,omitempty"`
	CampaignID  string       `gorm:"column:campaign_id;type:varchar(20);not null" json:"campaignId,omitempty"`
	AdID        string       `gorm:"column:ad_id;type:varchar(20);not null" json:"adId,omitempty"`
	AdsetID     string       `gorm:"column:adset_id;type:varchar(30)" json:"adsetId,omitempty"`
	Reach       int          `gorm:"column:reach" json:"reach,omitempty"`
	Frequency   float64      `gorm:"column:frequency;type:decimal(20,6)" json:"frequency,omitempty"`
	Impressions int          `gorm:"column:impressions" json:"impressions,omitempty"`
	Clicks      int          `gorm:"column:clicks" json:"clicks,omitempty"`
	Spend       float64      `gorm:"column:spend;type:decimal(20,5)" json:"spend,omitempty"`
	CPM         float64      `gorm:"column:cpm;type:decimal(20,6)" json:"cpm,omitempty"`
	UpdateTime  sql.NullTime `gorm:"column:update_time;type:datetime" json:"updateTime,omitempty"`
	OraclePrice *float64     `gorm:"column:oracle_price;type:decimal(20,10)" json:"oraclePrice,omitempty"` // 预估收益
	RealPrice   *float64     `gorm:"column:real_price;type:decimal(20,10)" json:"realPrice,omitempty"`     // 真实收益
}

// TableName 指定表名
func (f *FacebookInsights) TableName() string {
	return "facebook_insights"
}

// Updates 安全更新方法
func (f *FacebookInsights) Updates(updates map[string]interface{}) map[string]interface{} {
	// 创建一个新的更新映射
	safeUpdates := make(map[string]interface{})

	// 只保留非空值
	for key, value := range updates {
		switch v := value.(type) {
		case string:
			if v != "" {
				safeUpdates[key] = v
			}
		case int, int64, uint, uint64:
			if v != 0 {
				safeUpdates[key] = v
			}
		case float64:
			if v != 0.0 {
				safeUpdates[key] = v
			}
		case time.Time:
			if !v.IsZero() {
				safeUpdates[key] = v
			}
		default:
			if value != nil {
				safeUpdates[key] = value
			}
		}
	}

	return safeUpdates
}
