package model

import (
	"database/sql"
	"time"
)

// FacebookInsights Facebook广告数据洞察
type FacebookInsights struct {
	ID          int          `gorm:"column:id;primaryKey;autoIncrement" json:"id,omitempty"`
	Date        string       `gorm:"column:date;type:varchar(20);not null" json:"date,omitempty"` // 广告账户所在时区的时间
	UserID      int          `gorm:"column:userId;not null" json:"userId,omitempty"`
	AccountID   string       `gorm:"column:account_id;type:varchar(20);not null" json:"accountId,omitempty"`
	CampaignID  string       `gorm:"column:campaign_id;type:varchar(20);not null" json:"campaignId,omitempty"`
	AdID        string       `gorm:"column:ad_id;type:varchar(20);not null" json:"adId,omitempty"`
	AdsetID     string       `gorm:"column:adset_id;type:varchar(30)" json:"adsetId,omitempty"`
	Reach       int          `gorm:"column:reach" json:"reach,omitempty"`
	Frequency   float64      `gorm:"column:frequency;type:decimal(20,6)" json:"frequency,omitempty"`
	Impressions int          `gorm:"column:impressions" json:"impressions,omitempty"`
	Clicks      int          `gorm:"column:clicks" json:"clicks,omitempty"`
	Spend       float64      `gorm:"column:spend;type:decimal(20,5)" json:"spend,omitempty"`
	CPM         float64      `gorm:"column:cpm;type:decimal(20,6)" json:"cpm,omitempty"`
	UpdateTime  sql.NullTime `gorm:"column:update_time;type:datetime" json:"updateTime,omitempty"`
	OraclePrice *float64     `gorm:"column:oracle_price;type:decimal(20,10)" json:"oraclePrice,omitempty"` // 预估收益
	RealPrice   *float64     `gorm:"column:real_price;type:decimal(20,10)" json:"realPrice,omitempty"`     // 真实收益

	// 新增字段 - 基于JSON数据扩展
	CampaignName            string     `gorm:"column:campaign_name;type:varchar(255)" json:"campaignName,omitempty"`         // 广告系列名称
	Platform                string     `gorm:"column:platform;type:varchar(100)" json:"platform,omitempty"`                  // 平台名称
	Country                 string     `gorm:"column:country;type:varchar(10)" json:"country,omitempty"`                     // 国家
	Hour                    int        `gorm:"column:hour" json:"hour,omitempty"`                                            // 小时
	RelatedLinksRequests    int        `gorm:"column:related_links_requests" json:"relatedLinksRequests,omitempty"`          // 相关链接请求数
	RelatedLinksImpressions int        `gorm:"column:related_links_impressions" json:"relatedLinksImpressions,omitempty"`    // 相关链接展示数
	RelatedLinksClicks      int        `gorm:"column:related_links_clicks" json:"relatedLinksClicks,omitempty"`              // 相关链接点击数
	RelatedLinksRpm         float64    `gorm:"column:related_links_rpm;type:decimal(20,6)" json:"relatedLinksRpm,omitempty"` // 相关链接RPM
	AdRequests              int        `gorm:"column:ad_requests" json:"adRequests,omitempty"`                               // 广告请求数
	MatchedAdRequests       int        `gorm:"column:matched_ad_requests" json:"matchedAdRequests,omitempty"`                // 匹配的广告请求数
	AdImpressions           int        `gorm:"column:ad_impressions" json:"adImpressions,omitempty"`                         // 广告展示数
	CTR                     float64    `gorm:"column:ctr;type:decimal(20,6)" json:"ctr,omitempty"`                           // 点击率
	AdCTR                   float64    `gorm:"column:ad_ctr;type:decimal(20,6)" json:"adCtr,omitempty"`                      // 广告点击率
	AdRPM                   float64    `gorm:"column:ad_rpm;type:decimal(20,6)" json:"adRpm,omitempty"`                      // 广告RPM
	CR                      float64    `gorm:"column:cr;type:decimal(20,6)" json:"cr,omitempty"`                             // 转化率
	Revenue                 *float64   `gorm:"column:revenue;type:decimal(20,10)" json:"revenue,omitempty"`                  // 收益
	CreateTime              *time.Time `gorm:"column:create_time;type:datetime" json:"createTime,omitempty"`                 // 创建时间
}

// TableName 指定表名
func (f *FacebookInsights) TableName() string {
	return "facebook_insights"
}

// Updates 安全更新方法
func (f *FacebookInsights) Updates(updates map[string]interface{}) map[string]interface{} {
	// 创建一个新的更新映射
	safeUpdates := make(map[string]interface{})

	// 只保留非空值
	for key, value := range updates {
		switch v := value.(type) {
		case string:
			if v != "" {
				safeUpdates[key] = v
			}
		case int, int64, uint, uint64:
			if v != 0 {
				safeUpdates[key] = v
			}
		case float64:
			if v != 0.0 {
				safeUpdates[key] = v
			}
		case time.Time:
			if !v.IsZero() {
				safeUpdates[key] = v
			}
		default:
			if value != nil {
				safeUpdates[key] = value
			}
		}
	}

	return safeUpdates
}
