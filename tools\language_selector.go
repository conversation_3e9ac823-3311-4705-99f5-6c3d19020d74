package tools

import (
	"math/rand"
)

// SelectLanguage 根据语言概率分布选择一个语言
// languages 是一个map，key是语言代码，value是该语言的概率（0-1之间）
// 如果没有选中任何语言或languages为空，返回默认值"en-US,en;q=0.9"
func SelectLanguage(languages map[string]float64) string {
	if len(languages) == 0 {
		return "en-US,en;q=0.9"
	}

	// 生成0到1之间的随机数
	randomValue := rand.Float64()

	// 累加概率并选择语言
	var currentProbability float64
	for language, probability := range languages {
		currentProbability += probability
		if randomValue <= currentProbability {
			return language
		}
	}

	// 如果没有选中任何语言（可能是由于概率总和小于1），返回默认值
	return "en-US,en;q=0.9"
}
