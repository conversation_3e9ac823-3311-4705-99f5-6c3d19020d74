# Google 报告同步功能使用示例

## 场景说明

您需要定期从 AdTech API 获取 Google 收益报告数据，并将这些数据同步到本地数据库中，以便进行数据分析和报表生成。

## 使用流程

### 步骤1：获取安全密钥

首先，您需要从 AdTech 平台获取 `security_key`。

### 步骤2：同步历史数据

初次使用时，建议先同步历史数据：

```bash
# 同步最近30天的数据
curl -X GET "http://your-domain.com/google/syncReportDataRange" \
  -G \
  -d "security_key=your_security_key" \
  -d "start_date=2024-12-01" \
  -d "end_date=2024-12-31"
```

### 步骤3：设置定时同步

#### 方法1：使用内置定时任务

设置环境变量启用自动同步：

```bash
# 在 .env 文件中添加
GOOGLE_REPORT_SECURITY_KEY=your_security_key

# 或在启动时设置
export GOOGLE_REPORT_SECURITY_KEY=your_security_key
./your-app
```

#### 方法2：使用系统 crontab

```bash
# 编辑 crontab
crontab -e

# 添加以下任务（每小时同步一次）
0 * * * * curl -s "http://localhost:8080/google/autoSyncReportData?security_key=your_key" > /dev/null

# 添加每日完整同步（每天凌晨3点）
0 3 * * * curl -s "http://localhost:8080/google/syncReportDataRange?security_key=your_key&start_date=$(date -d '7 days ago' +\%Y-\%m-\%d)&end_date=$(date +\%Y-\%m-\%d)" > /dev/null
```

### 步骤4：监控同步状态

#### 查看同步日志

```bash
# 查看应用日志
tail -f /var/log/your-app.log | grep "Google报告"

# 或使用 journalctl（如果使用 systemd）
journalctl -u your-app -f | grep "Google报告"
```

#### 验证数据同步

```sql
-- 检查最新同步的数据
SELECT 
    campaign_id,
    date,
    impressions,
    clicks,
    spend as google_revenue,
    oracle_price,
    real_price,
    update_time
FROM facebook_insights 
WHERE date >= DATE_SUB(CURDATE(), INTERVAL 7 DAY)
ORDER BY update_time DESC 
LIMIT 10;

-- 检查同步统计
SELECT 
    date,
    COUNT(*) as campaign_count,
    SUM(impressions) as total_impressions,
    SUM(clicks) as total_clicks,
    SUM(spend) as total_revenue
FROM facebook_insights 
WHERE date >= DATE_SUB(CURDATE(), INTERVAL 30 DAY)
GROUP BY date 
ORDER BY date DESC;
```

## 实际应用场景

### 场景1：日常数据监控

```bash
#!/bin/bash
# daily_sync_check.sh

SECURITY_KEY="your_security_key"
BASE_URL="http://localhost:8080"

# 同步昨天的数据
YESTERDAY=$(date -d "yesterday" +%Y-%m-%d)
response=$(curl -s "$BASE_URL/google/syncReportData?security_key=$SECURITY_KEY&data_date=$YESTERDAY")

# 检查同步结果
synced_count=$(echo $response | jq -r '.synced_count')
error_count=$(echo $response | jq -r '.error_count')

if [ "$error_count" -gt 0 ]; then
    echo "警告：同步过程中有 $error_count 个错误"
    # 发送告警邮件或通知
fi

echo "昨日数据同步完成：成功 $synced_count 条"
```

### 场景2：数据质量检查

```sql
-- 检查数据完整性
SELECT 
    date,
    COUNT(*) as record_count,
    COUNT(CASE WHEN impressions > 0 THEN 1 END) as has_impressions,
    COUNT(CASE WHEN clicks > 0 THEN 1 END) as has_clicks,
    COUNT(CASE WHEN spend > 0 THEN 1 END) as has_revenue
FROM facebook_insights 
WHERE date >= DATE_SUB(CURDATE(), INTERVAL 7 DAY)
GROUP BY date 
ORDER BY date DESC;

-- 检查异常数据
SELECT *
FROM facebook_insights 
WHERE date >= DATE_SUB(CURDATE(), INTERVAL 7 DAY)
  AND (
    clicks > impressions  -- 点击数大于展示数（异常）
    OR spend < 0          -- 负收益（异常）
    OR (impressions > 0 AND clicks = 0 AND spend > 0)  -- 有展示有收益但无点击（可能正常）
  );
```

### 场景3：收益分析报表

```sql
-- 广告系列收益排行
SELECT 
    campaign_id,
    SUM(impressions) as total_impressions,
    SUM(clicks) as total_clicks,
    SUM(spend) as google_revenue,
    SUM(COALESCE(real_price, 0)) as real_revenue,
    ROUND(SUM(clicks) / SUM(impressions) * 100, 2) as ctr_percent,
    ROUND(SUM(spend) / SUM(impressions) * 1000, 2) as rpm
FROM facebook_insights 
WHERE date >= DATE_SUB(CURDATE(), INTERVAL 30 DAY)
GROUP BY campaign_id 
HAVING total_impressions > 1000  -- 过滤低流量广告系列
ORDER BY google_revenue DESC 
LIMIT 20;

-- 日收益趋势
SELECT 
    date,
    SUM(spend) as daily_revenue,
    SUM(clicks) as daily_clicks,
    COUNT(DISTINCT campaign_id) as active_campaigns
FROM facebook_insights 
WHERE date >= DATE_SUB(CURDATE(), INTERVAL 30 DAY)
GROUP BY date 
ORDER BY date DESC;
```

## 故障排除

### 常见问题

1. **同步失败**
   ```bash
   # 检查网络连接
   curl -I https://adtechapi.com/v2/google/reportDataSource
   
   # 检查安全密钥
   curl "https://adtechapi.com/v2/google/reportDataSource?security_key=your_key&data_date=2025-01-01&limit=1"
   ```

2. **数据不一致**
   ```sql
   -- 查找重复数据
   SELECT campaign_id, date, COUNT(*) 
   FROM facebook_insights 
   GROUP BY campaign_id, date 
   HAVING COUNT(*) > 1;
   
   -- 清理重复数据（保留最新的）
   DELETE t1 FROM facebook_insights t1
   INNER JOIN facebook_insights t2 
   WHERE t1.id < t2.id 
     AND t1.campaign_id = t2.campaign_id 
     AND t1.date = t2.date;
   ```

3. **性能问题**
   ```sql
   -- 添加索引优化查询
   CREATE INDEX idx_facebook_insights_campaign_date ON facebook_insights(campaign_id, date);
   CREATE INDEX idx_facebook_insights_date ON facebook_insights(date);
   CREATE INDEX idx_facebook_insights_update_time ON facebook_insights(update_time);
   ```

### 监控脚本

```bash
#!/bin/bash
# monitor_sync.sh

LOG_FILE="/var/log/google_sync_monitor.log"
SECURITY_KEY="your_security_key"

# 检查最近1小时是否有数据更新
recent_updates=$(mysql -u user -p'password' -D database -se "
SELECT COUNT(*) FROM facebook_insights 
WHERE update_time >= DATE_SUB(NOW(), INTERVAL 1 HOUR)
")

if [ "$recent_updates" -eq 0 ]; then
    echo "$(date): 警告 - 最近1小时没有数据更新" >> $LOG_FILE
    # 触发手动同步
    curl -s "http://localhost:8080/google/autoSyncReportData?security_key=$SECURITY_KEY"
fi

echo "$(date): 检查完成，最近1小时更新了 $recent_updates 条记录" >> $LOG_FILE
```

## 最佳实践

1. **数据备份**：定期备份 facebook_insights 表
2. **监控告警**：设置数据同步失败的告警机制
3. **性能优化**：为常用查询字段添加索引
4. **数据验证**：定期检查数据完整性和一致性
5. **日志管理**：定期清理和归档同步日志
