package system

import (
	"rsoc-system-go/model/system"
	"rsoc-system-go/store"
)

type CronDao struct {
}

// InsertCron 创建定时任务
func (dao *CronDao) InsertCron(entity *system.CronEntity) (*system.CronEntity, error) {
	db := store.DB
	err := db.Create(entity).Error
	return entity, err
}

// UpdateCron 更新定时任务
func (dao *CronDao) UpdateCron(entity *system.CronEntity) (*system.CronEntity, error) {
	db := store.DB
	err := db.Model(&system.CronEntity{}).Where("id = ?", entity.Id).Updates(entity).Error
	return entity, err
}

// DeleteCron 删除定时任务
func (dao *CronDao) DeleteCron(id int64) error {
	db := store.DB
	return db.Delete(&system.CronEntity{}, id).Error
}

// GetCronByID 根据ID获取定时任务
func (dao *CronDao) GetCronByID(params *system.CronEntity) (*system.CronEntity, error) {
	db := store.DB
	var result system.CronEntity
	err := db.Model(&system.CronEntity{}).Where(params).First(&result).Error
	return &result, err
}

// UpdateCronStatus 更新定时任务状态
func (dao *CronDao) UpdateCronStatus(entity *system.CronEntity) (*system.CronEntity, error) {
	db := store.DB
	err := db.Model(&system.CronEntity{}).Where("id = ?", entity.Id).Update("status", entity.Status).Error
	return entity, err
}

// GetCronList 获取定时任务列表
func (dao *CronDao) GetCronList(params *system.CronEntity) ([]*system.CronEntity, error) {
	db := store.DB
	var result []*system.CronEntity
	err := db.Model(&system.CronEntity{}).Where(params).Find(&result).Error
	return result, err
}
