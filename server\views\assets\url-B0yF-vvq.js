import{_ as R,u as E,o as G,r as h,a as K,M as r,j as H,c as y,b as i,d as s,w as l,e as o,B as Q,t as b,k as S,f as u,C as $,m as B,h as _,n as W,F as X,g as Y,I as Z,p as ee,s as O}from"./index-DlVegDiC.js";import{S as te,a as oe}from"./index-CSU5nP3m.js";import{P as ne}from"./PlusOutlined-Cg2o2XQN.js";import{_ as ae}from"./index-1uCBjWky.js";import{_ as le}from"./index-BrFZluVG.js";const se={class:"main"},de={class:"filter"},re={class:"filter_item"},ie={style:{"padding-top":"15px",color:"red"}},pe={style:{"padding-top":"15px",color:"red"}},ce={__name:"url",setup(ue){const f="",p=E(),F=K();G(()=>{w()});const k=h({fbId:void 0}),w=()=>{fetch(`${f}/FaceBook/appList`,{method:"POST",headers:{token:p.token}}).then(e=>e.json()).then(e=>{console.log(e),e.code==1?(d.fb_list=e.data,d.info=e.info):e.code==3e3?(p.$patch({token:!1}),F.push("/login"),r.error({title:e.msg})):r.error({title:e.msg})}).catch(e=>{r.error({title:"服务器错误",content:`${e}`})})},d=h({data:[],loading:!1,fb_list:[],info:""}),c=h({total:0,current:1,pageSize:10,position:["bottomCenter"],showSizeChanger:!0,pageSizeOptions:["10","20","30"],showTotal:e=>`共 ${e} 项`}),m=()=>{d.loading=!0,fetch(`${f}/FaceBook/fbUrlList`,{method:"POST",body:JSON.stringify({fbId:k.fbId,page:c.current,limit:c.pageSize,directoryStatus:1}),headers:{token:p.token}}).then(e=>e.json()).then(e=>{console.log(e),e.code==1?(d.data=e.data.data,c.total=Number(e.data.total)):e.code==3e3?(p.$patch({token:!1}),F.push("/login"),r.error({title:e.msg})):(d.data=[],r.error({title:e.msg})),d.loading=!1}).catch(e=>{r.error({title:"服务器错误",content:`${e}`})})},U=e=>{c.current=e.current,c.pageSize=e.pageSize,m()},I=H(),n=h({open:!1,id:void 0,url:"",note:"",loading:!1}),N=()=>{n.open=!0},P=()=>{n.loading=!0,fetch(`${f}/FaceBook/fbUrlAdd`,{method:"POST",body:JSON.stringify({fbId:n.id,url:n.url,note:n.note}),headers:{token:p.token}}).then(e=>e.json()).then(e=>{console.log(e),e.code==1?(O.success("添加成功"),m(),I.value.resetFields()):r.error({title:e.msg}),n.open=!1,n.loading=!1}).catch(e=>{r.error({title:"服务器错误",content:`${e}`})})},T=e=>{fetch(`${f}/FaceBook/fbUrlDel`,{method:"POST",body:JSON.stringify({id:e.id}),headers:{token:p.token}}).then(t=>t.json()).then(t=>{console.log(t),t.code==1?(O.success(t.msg),m()):r.error({title:t.msg})}).catch(t=>{r.error({title:"服务器错误",content:`${t}`})})},z=[{title:"ID",dataIndex:"id",key:"id",align:"center"},{title:"域名",dataIndex:"url",key:"url",align:"center"},{title:"备注",dataIndex:"note",key:"note",align:"center"},{title:"添加日期",dataIndex:"add_time",key:"add_time",align:"center"},{title:"操作",key:"action",align:"center"}];return(e,t)=>{const x=oe,C=te,L=ne,v=Q,j=le,A=ae,g=Y,D=Z,V=ee,J=X,M=r;return u(),y(S,null,[i("div",se,[i("div",null,[i("div",de,[i("div",re,[t[5]||(t[5]=i("p",null,"FB账号：",-1)),s(C,{value:o(k).fbId,"onUpdate:value":t[0]||(t[0]=a=>o(k).fbId=a),style:{width:"300px"},onChange:m,placeholder:"请选择FB账号查看数据"},{default:l(()=>[(u(!0),y(S,null,$(o(d).fb_list,a=>(u(),B(x,{value:a.id},{default:l(()=>[_(b(a.note),1)]),_:2},1032,["value"]))),256))]),_:1},8,["value"])]),i("div",null,[s(v,{type:"primary",onClick:N},{icon:l(()=>[s(L)]),default:l(()=>[t[6]||(t[6]=_(" 添加投放域名 "))]),_:1})])])]),s(A,{columns:z,"data-source":o(d).data,rowKey:"id",pagination:o(c),loading:o(d).loading,onChange:U,bordered:""},{bodyCell:l(({column:a,record:q})=>[a.key==="action"?(u(),B(j,{key:0,title:"确认删除？",onConfirm:_e=>T(q)},{default:l(()=>[s(v,{type:"link",danger:""},{default:l(()=>t[7]||(t[7]=[_("删除")])),_:1})]),_:2},1032,["onConfirm"])):W("",!0)]),_:1},8,["data-source","pagination","loading"]),i("div",ie,[i("p",null,b(o(d).info),1)])]),s(M,{open:o(n).open,"onUpdate:open":t[4]||(t[4]=a=>o(n).open=a),title:"添加投放域名",footer:null,maskClosable:!1},{default:l(()=>[t[9]||(t[9]=i("div",{style:{height:"20px"}},null,-1)),s(J,{ref_key:"add_form",ref:I,model:o(n),onFinish:P,"label-col":{span:4},"wrapper-col":{span:18}},{default:l(()=>[s(g,{label:"FB账号",name:"id",rules:[{required:!0,message:"请选择FB账号"}]},{default:l(()=>[s(C,{value:o(n).id,"onUpdate:value":t[1]||(t[1]=a=>o(n).id=a),placeholder:"请选择FB账号"},{default:l(()=>[(u(!0),y(S,null,$(o(d).fb_list,a=>(u(),B(x,{value:a.id},{default:l(()=>[_(b(a.note),1)]),_:2},1032,["value"]))),256))]),_:1},8,["value"])]),_:1}),s(g,{label:"域名",name:"url",rules:[{required:!0,message:"请输入域名"}]},{default:l(()=>[s(D,{value:o(n).url,"onUpdate:value":t[2]||(t[2]=a=>o(n).url=a),placeholder:"域名"},null,8,["value"])]),_:1}),s(g,{label:"备注",name:"note"},{default:l(()=>[s(V,{value:o(n).note,"onUpdate:value":t[3]||(t[3]=a=>o(n).note=a),placeholder:"备注",rows:2},null,8,["value"]),i("p",pe,b(o(d).info),1)]),_:1}),s(g,{"wrapper-col":{offset:4,span:18}},{default:l(()=>[s(v,{type:"primary","html-type":"submit",loading:o(n).loading},{default:l(()=>t[8]||(t[8]=[_(" 提交 ")])),_:1},8,["loading"])]),_:1})]),_:1},8,["model"])]),_:1},8,["open"])],64)}}},ke=R(ce,[["__scopeId","data-v-3a46498a"]]);export{ke as default};
