-- 简单迁移脚本：为 facebook_insights 表添加扩展字段
-- 版本：001_simple
-- 创建时间：2025-01-19
-- 描述：简单直接的字段添加，兼容所有MySQL版本
-- 注意：如果字段已存在会报错，这是正常的，可以忽略

-- 开始迁移
SELECT 'Starting simple migration: Add extended fields to facebook_insights table' as status;

-- 添加广告系列名称字段
ALTER TABLE facebook_insights ADD COLUMN campaign_name VARCHAR(255) DEFAULT NULL COMMENT '广告系列名称';

-- 添加平台字段
ALTER TABLE facebook_insights ADD COLUMN platform VARCHAR(100) DEFAULT NULL COMMENT '平台名称';

-- 添加国家字段
ALTER TABLE facebook_insights ADD COLUMN country VARCHAR(10) DEFAULT NULL COMMENT '国家代码';

-- 添加小时字段
ALTER TABLE facebook_insights ADD COLUMN hour INT DEFAULT NULL COMMENT '小时(0-23)';

-- 添加相关链接数据字段
ALTER TABLE facebook_insights ADD COLUMN related_links_requests INT DEFAULT NULL COMMENT '相关链接请求数';
ALTER TABLE facebook_insights ADD COLUMN related_links_impressions INT DEFAULT NULL COMMENT '相关链接展示数';
ALTER TABLE facebook_insights ADD COLUMN related_links_clicks INT DEFAULT NULL COMMENT '相关链接点击数';
ALTER TABLE facebook_insights ADD COLUMN related_links_rpm DECIMAL(20,6) DEFAULT NULL COMMENT '相关链接RPM';

-- 添加广告请求数据字段
ALTER TABLE facebook_insights ADD COLUMN ad_requests INT DEFAULT NULL COMMENT '广告请求数';
ALTER TABLE facebook_insights ADD COLUMN matched_ad_requests INT DEFAULT NULL COMMENT '匹配的广告请求数';
ALTER TABLE facebook_insights ADD COLUMN ad_impressions INT DEFAULT NULL COMMENT '广告展示数';

-- 添加比率数据字段
ALTER TABLE facebook_insights ADD COLUMN ctr DECIMAL(20,6) DEFAULT NULL COMMENT '点击率';
ALTER TABLE facebook_insights ADD COLUMN ad_ctr DECIMAL(20,6) DEFAULT NULL COMMENT '广告点击率';
ALTER TABLE facebook_insights ADD COLUMN ad_rpm DECIMAL(20,6) DEFAULT NULL COMMENT '广告RPM';
ALTER TABLE facebook_insights ADD COLUMN cr DECIMAL(20,6) DEFAULT NULL COMMENT '转化率';

-- 添加收益字段
ALTER TABLE facebook_insights ADD COLUMN revenue DECIMAL(20,10) DEFAULT NULL COMMENT '收益';

-- 添加创建时间字段
ALTER TABLE facebook_insights ADD COLUMN create_time DATETIME DEFAULT NULL COMMENT '创建时间';

-- 创建索引以提高查询性能
-- 注意：如果索引已存在会报错，可以忽略
CREATE INDEX idx_facebook_insights_campaign_name ON facebook_insights(campaign_name);
CREATE INDEX idx_facebook_insights_platform ON facebook_insights(platform);
CREATE INDEX idx_facebook_insights_country ON facebook_insights(country);
CREATE INDEX idx_facebook_insights_hour ON facebook_insights(hour);
CREATE INDEX idx_facebook_insights_create_time ON facebook_insights(create_time);

-- 复合索引
CREATE INDEX idx_facebook_insights_platform_country ON facebook_insights(platform, country);
CREATE INDEX idx_facebook_insights_date_platform ON facebook_insights(date, platform);

SELECT 'Simple migration completed: Extended fields added to facebook_insights table' as status;

-- 验证新字段是否添加成功
SELECT 
    COLUMN_NAME,
    DATA_TYPE,
    IS_NULLABLE,
    COLUMN_DEFAULT,
    COLUMN_COMMENT
FROM INFORMATION_SCHEMA.COLUMNS 
WHERE TABLE_SCHEMA = DATABASE() 
  AND TABLE_NAME = 'facebook_insights'
  AND COLUMN_NAME IN (
    'campaign_name', 'platform', 'country', 'hour',
    'related_links_requests', 'related_links_impressions', 'related_links_clicks', 'related_links_rpm',
    'ad_requests', 'matched_ad_requests', 'ad_impressions',
    'ctr', 'ad_ctr', 'ad_rpm', 'cr', 'revenue', 'create_time'
  )
ORDER BY ORDINAL_POSITION;
