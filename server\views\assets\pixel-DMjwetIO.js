import{_ as F,u as D,o as z,r as k,a as U,M as s,j,c as V,b as p,d as n,w as l,B as q,e as a,k as J,f as y,h as x,m as L,n as M,F as R,g as E,I as K,s as P}from"./index-DlVegDiC.js";import{P as G}from"./PlusOutlined-Cg2o2XQN.js";import{_ as H}from"./index-1uCBjWky.js";import{_ as Q}from"./index-BrFZluVG.js";import"./index-CSU5nP3m.js";const W={class:"main"},X={class:"filter"},Y={__name:"pixel",setup(Z){const _="",u=D(),b=U();z(()=>{m()});const d=k({data:[],loading:!1}),r=k({total:0,current:1,pageSize:10,position:["bottomCenter"],showSizeChanger:!0,pageSizeOptions:["10","20","30"],showTotal:e=>`共 ${e} 项`}),m=()=>{d.loading=!0,fetch(`${_}/FaceBook/fbPixelList`,{method:"POST",body:JSON.stringify({page:r.current,limit:r.pageSize,directoryStatus:1}),headers:{token:u.token}}).then(e=>e.json()).then(e=>{console.log(e),e.code==1?(d.data=e.data.data,r.total=Number(e.data.total)):e.code==3e3?(u.$patch({token:!1}),b.push("/login"),s.error({title:e.msg})):(d.data=[],s.error({title:e.msg})),d.loading=!1}).catch(e=>{s.error({title:"服务器错误",content:`${e}`})})},v=e=>{r.current=e.current,r.pageSize=e.pageSize,m()},h=j(),o=k({open:!1,id:"",name:"",token:"",loading:!1}),I=()=>{o.open=!0},S=()=>{o.loading=!0,fetch(`${_}/FaceBook/fbPixelAdd`,{method:"POST",body:JSON.stringify({pixel_id:o.id,pixel_name:o.name,api_token:o.token}),headers:{token:u.token}}).then(e=>e.json()).then(e=>{console.log(e),e.code==1?(P.success("添加成功"),m(),h.value.resetFields()):s.error({title:e.msg}),o.open=!1,o.loading=!1}).catch(e=>{s.error({title:"服务器错误",content:`${e}`})})},C=e=>{fetch(`${_}/FaceBook/fbPixelDel`,{method:"POST",body:JSON.stringify({id:e.id}),headers:{token:u.token}}).then(t=>t.json()).then(t=>{console.log(t),t.code==1?(P.success(t.msg),m()):s.error({title:t.msg})}).catch(t=>{s.error({title:"服务器错误",content:`${t}`})})},T=[{title:"ID",dataIndex:"id",key:"id",align:"center"},{title:"Pixel ID",dataIndex:"pixel_id",key:"pixel_id",align:"center"},{title:"Pixel Name",dataIndex:"pixel_name",key:"pixel_name",align:"center"},{title:"API Token",dataIndex:"api_token",key:"api_token",align:"center",width:300,ellipsis:!0},{title:"操作",key:"action",align:"center"}];return(e,t)=>{const w=G,f=q,B=Q,N=H,g=K,c=E,O=R,$=s;return y(),V(J,null,[p("div",W,[p("div",null,[p("div",X,[t[5]||(t[5]=p("div",{class:"filter_item"},null,-1)),p("div",null,[n(f,{type:"primary",onClick:I},{icon:l(()=>[n(w)]),default:l(()=>[t[4]||(t[4]=x(" 添加像素 "))]),_:1})])])]),n(N,{columns:T,"data-source":a(d).data,rowKey:"id",pagination:a(r),loading:a(d).loading,onChange:v,bordered:""},{bodyCell:l(({column:i,record:A})=>[i.key==="action"?(y(),L(B,{key:0,title:"确认删除？",onConfirm:ee=>C(A)},{default:l(()=>[n(f,{type:"link",danger:""},{default:l(()=>t[6]||(t[6]=[x("删除")])),_:1})]),_:2},1032,["onConfirm"])):M("",!0)]),_:1},8,["data-source","pagination","loading"])]),n($,{open:a(o).open,"onUpdate:open":t[3]||(t[3]=i=>a(o).open=i),title:"添加像素",footer:null,maskClosable:!1},{default:l(()=>[t[8]||(t[8]=p("div",{style:{height:"20px"}},null,-1)),n(O,{ref_key:"add_form",ref:h,model:a(o),onFinish:S,"label-col":{span:5},"wrapper-col":{span:17}},{default:l(()=>[n(c,{label:"Pixel ID",name:"id",rules:[{required:!0,message:"请输入Pixel ID"}]},{default:l(()=>[n(g,{value:a(o).id,"onUpdate:value":t[0]||(t[0]=i=>a(o).id=i),placeholder:"Pixel ID"},null,8,["value"])]),_:1}),n(c,{label:"Pixel名称",name:"name",rules:[{required:!0,message:"请输入Pixel名称"}]},{default:l(()=>[n(g,{value:a(o).name,"onUpdate:value":t[1]||(t[1]=i=>a(o).name=i),placeholder:"Pixel名称"},null,8,["value"])]),_:1}),n(c,{label:"API Token",name:"token",rules:[{required:!0,message:"请输入API Token"}]},{default:l(()=>[n(g,{value:a(o).token,"onUpdate:value":t[2]||(t[2]=i=>a(o).token=i),placeholder:"API Token"},null,8,["value"])]),_:1}),n(c,{"wrapper-col":{offset:5,span:17}},{default:l(()=>[n(f,{type:"primary","html-type":"submit",loading:a(o).loading},{default:l(()=>t[7]||(t[7]=[x(" 提交 ")])),_:1},8,["loading"])]),_:1})]),_:1},8,["model"])]),_:1},8,["open"])],64)}}},ie=F(Y,[["__scopeId","data-v-e611f094"]]);export{ie as default};
