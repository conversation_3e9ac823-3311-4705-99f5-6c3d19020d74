import{_ as q,u as T,o as V,r as g,a as A,M as r,j as w,c as D,b as i,d as o,w as l,B as E,e as n,k as M,f as I,h as c,m as J,n as L,F as R,g as z,I as G,p as H,s as C}from"./index-DlVegDiC.js";import{P as Q}from"./PlusOutlined-Cg2o2XQN.js";import{_ as W}from"./index-1uCBjWky.js";import"./index-CSU5nP3m.js";const X={class:"main"},Y={class:"filter"},Z={__name:"account",setup(ee){const _="",m=T(),K=A();V(()=>{y()});const u=g({data:[],loading:!1}),y=()=>{u.loading=!0,fetch(`${_}/SeDo/list`,{method:"POST",headers:{token:m.token}}).then(e=>e.json()).then(e=>{console.log(e),e.code==1?u.data=e.data:e.code==3e3?(m.$patch({token:!1}),K.push("/login"),r.error({title:e.msg})):(u.data=[],r.error({title:e.msg})),u.loading=!1}).catch(e=>{r.error({title:"服务器错误",content:`${e}`})})},k=w(),d=g({open:!1,key:"",note:"",loading:!1}),$=()=>{d.open=!0},F=()=>{d.loading=!0,fetch(`${_}/SeDo/addSedo`,{method:"POST",body:JSON.stringify({key:d.key,note:d.note}),headers:{token:m.token}}).then(e=>e.json()).then(e=>{console.log(e),e.code==1?(C.success("添加成功"),y(),k.value.resetFields()):r.error({title:e.msg}),d.open=!1,d.loading=!1}).catch(e=>{r.error({title:"服务器错误",content:`${e}`})})},v=w(),a=g({open:!1,id:0,key:"",note:"",loading:!1}),N=e=>{console.log(e),a.id=e.id,a.key=e.key,a.note=e.note,a.open=!0},O=()=>{a.loading=!0,fetch(`${_}/sedo/updateNote`,{method:"POST",body:JSON.stringify({id:a.id,note:a.note}),headers:{token:m.token}}).then(e=>e.json()).then(e=>{console.log(e),e.code==1?(C.success("修改成功"),y(),v.value.resetFields()):r.error({title:e.msg}),a.open=!1,a.loading=!1}).catch(e=>{r.error({title:"服务器错误",content:`${e}`})})},P=[{title:"ID",dataIndex:"id",key:"id",align:"center"},{title:"Key",dataIndex:"key",key:"key",align:"center"},{title:"备注",dataIndex:"note",key:"note",align:"center"},{title:"添加日期",dataIndex:"add_time",key:"add_time",align:"center"},{title:"操作",key:"action",align:"center"}];return(e,t)=>{const U=Q,f=E,B=W,b=G,p=z,h=H,S=R,x=r;return I(),D(M,null,[i("div",X,[i("div",null,[i("div",Y,[t[7]||(t[7]=i("div",{class:"filter_item"},null,-1)),i("div",null,[o(f,{type:"primary",onClick:$},{icon:l(()=>[o(U)]),default:l(()=>[t[6]||(t[6]=c(" 添加Sedo账户 "))]),_:1})])])]),o(B,{columns:P,"data-source":n(u).data,rowKey:"id",pagination:!1,loading:n(u).loading,bordered:""},{bodyCell:l(({column:s,record:j})=>[s.key==="action"?(I(),J(f,{key:0,type:"link",onClick:te=>N(j)},{default:l(()=>t[8]||(t[8]=[c(" 编辑备注 ")])),_:2},1032,["onClick"])):L("",!0)]),_:1},8,["data-source","loading"])]),o(x,{open:n(d).open,"onUpdate:open":t[2]||(t[2]=s=>n(d).open=s),title:"添加Sedo账户",footer:null,maskClosable:!1},{default:l(()=>[t[10]||(t[10]=i("div",{style:{height:"20px"}},null,-1)),o(S,{ref_key:"add_form",ref:k,model:n(d),onFinish:F,"label-col":{span:4},"wrapper-col":{span:18}},{default:l(()=>[o(p,{label:"Key",name:"key",rules:[{required:!0,message:"请输入Key"}]},{default:l(()=>[o(b,{value:n(d).key,"onUpdate:value":t[0]||(t[0]=s=>n(d).key=s),placeholder:"Key"},null,8,["value"])]),_:1}),o(p,{label:"备注",name:"note",rules:[{required:!0,message:"请输入备注"}]},{default:l(()=>[o(h,{value:n(d).note,"onUpdate:value":t[1]||(t[1]=s=>n(d).note=s),placeholder:"备注",rows:2},null,8,["value"])]),_:1}),o(p,{"wrapper-col":{offset:4,span:18}},{default:l(()=>[o(f,{type:"primary","html-type":"submit",loading:n(d).loading},{default:l(()=>t[9]||(t[9]=[c(" 提交 ")])),_:1},8,["loading"])]),_:1})]),_:1},8,["model"])]),_:1},8,["open"]),o(x,{open:n(a).open,"onUpdate:open":t[5]||(t[5]=s=>n(a).open=s),title:"编辑Sedo备注",footer:null,maskClosable:!1},{default:l(()=>[t[12]||(t[12]=i("div",{style:{height:"20px"}},null,-1)),o(S,{ref_key:"edit_form",ref:v,model:n(a),onFinish:O,"label-col":{span:4},"wrapper-col":{span:18}},{default:l(()=>[o(p,{label:"Key",name:"key",rules:[{required:!0,message:"请输入Key"}]},{default:l(()=>[o(b,{value:n(a).key,"onUpdate:value":t[3]||(t[3]=s=>n(a).key=s),placeholder:"Key",disabled:""},null,8,["value"])]),_:1}),o(p,{label:"备注",name:"note",rules:[{required:!0,message:"请输入备注"}]},{default:l(()=>[o(h,{value:n(a).note,"onUpdate:value":t[4]||(t[4]=s=>n(a).note=s),placeholder:"备注",rows:2},null,8,["value"])]),_:1}),o(p,{"wrapper-col":{offset:4,span:18}},{default:l(()=>[o(f,{type:"primary","html-type":"submit",loading:n(a).loading},{default:l(()=>t[11]||(t[11]=[c(" 提交 ")])),_:1},8,["loading"])]),_:1})]),_:1},8,["model"])]),_:1},8,["open"])],64)}}},de=q(Z,[["__scopeId","data-v-d9c9927a"]]);export{de as default};
