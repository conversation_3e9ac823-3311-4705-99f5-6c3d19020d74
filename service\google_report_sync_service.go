package service

import (
	"context"
	"database/sql"
	"fmt"
	"net/url"
	"rsoc-system-go/dao"
	"rsoc-system-go/model"
	"rsoc-system-go/store"
	"strconv"
	"sync"
	"time"

	"github.com/sirupsen/logrus"
)

// GoogleReportSyncService Google报告同步服务
type GoogleReportSyncService struct {
	httpClient          *AdTechHTTPClient
	facebookInsightsDao *dao.FacebookInsightsDAO
	logger              *logrus.Logger
}

var (
	googleReportSyncService     *GoogleReportSyncService
	googleReportSyncServiceOnce sync.Once
)

// GetGoogleReportSyncService 获取Google报告同步服务单例
func GetGoogleReportSyncService() *GoogleReportSyncService {
	googleReportSyncServiceOnce.Do(func() {
		logger := logrus.New()
		googleReportSyncService = &GoogleReportSyncService{
			httpClient:          GetAdTechHTTPClient(),
			facebookInsightsDao: dao.NewFacebookInsightsDAO(store.DB),
			logger:              logger,
		}
	})
	return googleReportSyncService
}

// SyncGoogleReportData 同步Google报告数据
func (s *GoogleReportSyncService) SyncGoogleReportData(ctx context.Context, request *model.GoogleReportSyncRequest) (*model.GoogleReportSyncResponse, error) {
	s.logger.Infof("开始同步Google报告数据: date=%s", request.DataDate)

	// 设置安全密钥
	s.httpClient.SetSecurityKey(request.SecurityKey)

	var allItems []model.GoogleReportDataItem
	var syncedCount, errorCount int
	page := 1
	if request.Page > 0 {
		page = request.Page
	}
	limit := 30
	if request.Limit > 0 {
		limit = request.Limit
	}

	// 分页获取所有数据
	for {
		// 构建查询参数
		query := url.Values{}
		//query.Set("data_date", request.DataDate)
		query.Set("limit", strconv.Itoa(limit))
		query.Set("page", strconv.Itoa(page))

		// 调用API获取数据
		resp, err := s.httpClient.Get(ctx, "/google/reportDataSource", query)
		if err != nil {
			s.logger.Errorf("获取Google报告数据失败: %v", err)
			return nil, fmt.Errorf("获取Google报告数据失败: %v", err)
		}

		// 解析响应
		var apiResponse model.GoogleReportDataResponse
		if err := s.httpClient.ParseResponse(resp, &apiResponse); err != nil {
			s.logger.Errorf("解析Google报告响应失败: %v", err)
			return nil, fmt.Errorf("解析Google报告响应失败: %v", err)
		}

		// 检查API响应状态
		if apiResponse.Code != 200 {
			return nil, fmt.Errorf("API错误: %s (代码: %d)", apiResponse.Msg, apiResponse.Code)
		}

		// 添加到总列表
		allItems = append(allItems, apiResponse.Data.List...)

		s.logger.Infof("获取第%d页数据，共%d条", page, len(apiResponse.Data.List))

		// 如果是指定页码或者已经是最后一页，退出循环
		if request.Page > 0 || page >= apiResponse.Data.MaxPage {
			break
		}

		page++
	}

	// 处理每个数据项
	for _, item := range allItems {
		if err := s.processGoogleReportItem(&item, request.DataDate); err != nil {
			s.logger.Errorf("处理Google报告数据项失败: %v, item: %+v", err, item)
			errorCount++
		} else {
			syncedCount++
		}
	}

	s.logger.Infof("Google报告数据同步完成: 总数=%d, 成功=%d, 失败=%d", len(allItems), syncedCount, errorCount)

	return &model.GoogleReportSyncResponse{
		Code:        200,
		Msg:         "同步完成",
		Time:        time.Now().Unix(),
		SyncedCount: syncedCount,
		TotalCount:  len(allItems),
		ErrorCount:  errorCount,
	}, nil
}

// processGoogleReportItem 处理单个Google报告数据项
func (s *GoogleReportSyncService) processGoogleReportItem(item *model.GoogleReportDataItem, dataDate string) error {
	// 解析收益
	revenue, err := strconv.ParseFloat(item.Revenue, 64)
	if err != nil {
		s.logger.Warnf("解析收益失败: %v, revenue=%s", err, item.Revenue)
		revenue = 0
	}

	// 使用 campaign_name 作为 campaign_id
	campaignID := item.CampaignId
	if campaignID == "" {
		return fmt.Errorf("campaignID 为空")
	}

	// 查找现有记录
	conditions := map[string]interface{}{
		"campaign_id": campaignID,
		"date":        dataDate,
	}
	s.facebookInsightsDao = dao.NewFacebookInsightsDAO(store.DB)
	existingInsight, err := s.facebookInsightsDao.FindOne(conditions)
	if err != nil && err.Error() != "record not found" {
		return err
	}

	if existingInsight != nil {
		// 更新现有记录
		existingInsight.Impressions = item.Impressions
		existingInsight.Clicks = item.Clicks
		existingInsight.Spend = revenue
		if item.Impressions > 0 {
			existingInsight.CPM = (revenue / float64(item.Impressions)) * 1000
		}
		existingInsight.UpdateTime = sql.NullTime{Time: time.Now(), Valid: true}

		// 如果没有真实收益，使用Google报告的收益作为预估收益
		if existingInsight.RealPrice == nil {
			existingInsight.OraclePrice = &revenue
		}

		return s.facebookInsightsDao.Update(existingInsight)
	} else {
		// 创建新记录
		newInsight := &model.FacebookInsights{
			Date:   dataDate,
			UserID: 1, // 默认用户ID
			//AccountID:   campaignID,
			CampaignID: campaignID,
			//AdID:        campaignID,
			//AdsetID:     campaignID,
			Reach:       0, // Google报告中没有reach数据
			Frequency:   0, // Google报告中没有frequency数据
			Impressions: item.Impressions,
			Clicks:      item.Clicks,
			Spend:       revenue,
			CPM:         0,
			UpdateTime:  sql.NullTime{Time: time.Now(), Valid: true},
			OraclePrice: &revenue, // Google报告收益作为预估收益
		}

		// 计算CPM
		if item.Impressions > 0 {
			newInsight.CPM = (revenue / float64(item.Impressions)) * 1000
		}

		return s.facebookInsightsDao.Create(newInsight)
	}
}

// SyncGoogleReportDataByDateRange 按日期范围同步Google报告数据
func (s *GoogleReportSyncService) SyncGoogleReportDataByDateRange(ctx context.Context, securityKey string, startDate, endDate string) (*model.GoogleReportSyncResponse, error) {
	start, err := time.Parse("2006-01-02", startDate)
	if err != nil {
		return nil, fmt.Errorf("开始日期格式错误: %v", err)
	}

	end, err := time.Parse("2006-01-02", endDate)
	if err != nil {
		return nil, fmt.Errorf("结束日期格式错误: %v", err)
	}

	var totalSynced, totalCount, totalErrors int

	// 逐日同步
	for d := start; !d.After(end); d = d.AddDate(0, 0, 1) {
		dateStr := d.Format("2006-01-02")

		request := &model.GoogleReportSyncRequest{
			SecurityKey: securityKey,
			DataDate:    dateStr,
			Limit:       100, // 每页100条
			Page:        0,   // 获取所有页
		}

		response, err := s.SyncGoogleReportData(ctx, request)
		if err != nil {
			s.logger.Errorf("同步日期 %s 失败: %v", dateStr, err)
			totalErrors++
			continue
		}

		totalSynced += response.SyncedCount
		totalCount += response.TotalCount
		totalErrors += response.ErrorCount

		// 避免请求过于频繁
		time.Sleep(100 * time.Millisecond)
	}

	return &model.GoogleReportSyncResponse{
		Code:        200,
		Msg:         "批量同步完成",
		Time:        time.Now().Unix(),
		SyncedCount: totalSynced,
		TotalCount:  totalCount,
		ErrorCount:  totalErrors,
	}, nil
}
